<odoo>
    <record id="th_salary_range_code_history_tree" model="ir.ui.view">
        <field name="name">th_salary_range_code_history_tree</field>
        <field name="model">th.salary.range.code.history</field>
        <field name="arch" type="xml">
            <tree string="">
                <field name="name"/>
                <field name="th_employee_id"/>
                <field name="active" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="th_salary_range_code_history_form" model="ir.ui.view">
        <field name="name">th_salary_range_code_history_form</field>
        <field name="model">th.salary.range.code.history</field>
        <field name="arch" type="xml">
            <form string="L<PERSON>ch sử lương"
                  create="false"
                  edit="false"
                  delete="false"
                  duplicate="false"
                  import="false">
                <sheet>
                    <div class="oe_button_box" name="button_box"/>
                    <h1>
                        <div class="d-flex justify-content-start">
                            <div>
                                <field name="name"/>
                                <field name="active" invisible="1"/>
                            </div>
                        </div>
                    </h1>
                    <h2>
                        <field name="th_employee_id"/>
                    </h2>
                    <group>
                        <group>
                            <field name="th_department_id"/>
                            <field name="th_job_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Lương cơ bản">
                            <field name="th_salary_range_code_id" nolabel="1" readonly="1">
                                <tree decoration-primary="th_state == 'new'"
                                      decoration-muted="th_state == 'old'"
                                      no_open="1"
                                      create="0" delete="0">
                                    <field name="th_state" widget="badge" decoration-info="th_state == 'new'" decoration-warning="th_state == 'old'"/>
                                    <field name="th_currency_id" invisible="1"/>
                                    <field name="th_rank_code_id"/>
                                    <field name="th_rank_code"/>
                                    <field name="th_code"/>
                                    <field name="th_responsible_unit"/>
                                    <field name="th_description"/>
                                    <field name="th_date_from"/>
                                    <field name="th_date_to"/>
                                    <field name="th_insurance_paid"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="th_salary_range_code_history_action" model="ir.actions.act_window">
        <field name="name">Lịch sử mã ngạch bậc</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.salary.range.code.history</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'create': 0}</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                <!-- Add Text Here -->
            </p>
            <p>
                <!-- More details about what a user can do with this object will be OK -->
            </p>
        </field>
    </record>

    <!-- This Menu Item must have a parent and an action -->
    <menuitem id="th_salary_range_code_history_menu" name="Lịch sử mã ngạch bậc" parent="th_salary_menu_root"
              action="th_salary_range_code_history_action" sequence="53"/>
</odoo>