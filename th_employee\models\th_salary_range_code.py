from odoo import fields, models, api, _
from odoo.exceptions import ValidationError


class ThSalaryRangeCode(models.Model):
    _name = 'th.salary.range.code'
    _description = 'Salary range code'
    _rec_name = 'th_code'

    th_rank_code_id = fields.Many2one(comodel_name="th.rank.code", string="Mã ngạch", required=1)
    th_rank_code = fields.Selection(selection=[('00', '00'), ('01', '01'), ('02', '02'), ('03', '03'), ('04', '04'),
                                               ('05', '05'), ('06', '06'), ('07', '07'), ('08', '08'), ('09', '09')],
                                    string="Mã bậc", required=1)
    th_code = fields.Char('Salary range code', compute="_compute_th_code", store=True)
    th_level = fields.Selection([('1', '1'),
                                 ('2', '2'),
                                 ('3', '3'),
                                 ('4', '4'),
                                 ('5', '5')], string='Level of salary', default='1')
    th_insurance_paid = fields.Float('Paid for insurance')
    th_currency_id = fields.Many2one(comodel_name='res.currency', string='Currency unit', default=lambda self: self.env.company.currency_id)
    th_responsible_unit = fields.Selection([('aum', 'AUM'), ('vmc', 'VMC'), ('th', 'TH')], string='Responsible unit')
    th_date_from = fields.Date(string="Từ ngày")
    th_date_to = fields.Date(string="Đến ngày")
    th_description = fields.Char(string="Mô tả ngạch bậc")
    th_salary_range_code_history = fields.Many2one(comodel_name="th.salary.range.code.history", string="Lịch sử mã ngạch bậc")
    th_state = fields.Selection([('new', 'Hiện hành'), ('old', 'Cũ')], string="Trạng thái")

    @api.constrains('th_code')
    def constrains_th_code(self):
        if any(self.search([('th_code', '=', rec.th_code), ('id', '!=', rec.id), ('th_responsible_unit', '=', rec.th_responsible_unit), ('th_state', '=', False)]) for rec in self if rec.th_state == False):
            raise ValidationError(_('Salary range code for a unit must be unique!'))

    @api.constrains('th_date_from', 'th_date_to')
    def constrains_date(self):
        for rec in self:
            if rec.th_date_from and rec.th_date_to and rec.th_date_from > rec.th_date_to:
                raise ValidationError(_('Từ ngày không thể lớn hơn đến ngày!'))

    @api.constrains('th_insurance_paid')
    def constrains_th_insurance_paid(self):
        for rec in self:
            if rec.th_insurance_paid < 0:
                raise ValidationError(_("Paid for insurance must be greater than 0."))

    @api.depends('th_rank_code_id', 'th_rank_code')
    def _compute_th_code(self):
        for rec in self:
            rec.th_code = f'{rec.th_rank_code_id.name}{rec.th_rank_code}' if rec.th_rank_code_id and rec.th_rank_code else ""
