from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    def _domain_th_l_a_manager_ids(self):
        res = super()._domain_th_l_a_manager_ids()
        if len(res) > 0:
            user_ids = self.env['res.users'].browse(res[0][2]).filtered(lambda u: u.has_group('hr_attendance.group_hr_attendance_manager'))
        else:
            user_ids = self.env['res.users'].search([]).filtered(lambda u: u.has_group('hr_attendance.group_hr_attendance_manager'))
        return [('id', 'in', user_ids.ids)]

    th_normal_att = fields.Char('Normal attendance code')
    th_kiosk_att = fields.Char('External attendance code')
    th_validation_type = fields.Selection(selection=[('once', 'Quản lý'), ('twice', '<PERSON><PERSON><PERSON><PERSON> lý và HR')], string='Phê duyệt')
    th_l_a_manager_ids = fields.Many2many(domain=_domain_th_l_a_manager_ids)


    def set_values(self):
        super().set_values()
        self.env['ir.config_parameter'].set_param('th_normal_att', self.th_normal_att)
        self.env['ir.config_parameter'].set_param('th_kiosk_att', self.th_kiosk_att)
        self.env['ir.config_parameter'].set_param('th_validation_type', self.th_validation_type)

    @api.model
    def get_values(self):
        res = super().get_values()
        th_validation_type = self.env['ir.config_parameter'].sudo().get_param('th_validation_type')
        if not th_validation_type:
            th_validation_type = 'twice'
        res.update(th_normal_att=self.env['ir.config_parameter'].sudo().get_param('th_normal_att'),
                   th_kiosk_att=self.env['ir.config_parameter'].sudo().get_param('th_kiosk_att'),
                   th_validation_type=th_validation_type,
                   )
        return res

    # @api.model
    # def default_get(self, fields):
    #     res = super().default_get(fields)
    #     if not set(fields) & set(['th_validation_type', 'twice']):
    #         return res
