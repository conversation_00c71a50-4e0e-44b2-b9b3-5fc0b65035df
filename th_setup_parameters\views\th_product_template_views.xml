<odoo>
    <record id="product_template_form_view_inherited" model="ir.ui.view">
        <field name="name">product.template.common.form.inherited</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='group_standard_price']//field[@name='categ_id']" position="after">
                <field name="th_product_category_domain_1" invisible="1"/>
            </xpath>
            <xpath expr="//group[@name='group_standard_price']//field[@name='categ_id']" position="attributes">
                <attribute name="domain">th_product_category_domain_1</attribute>
            </xpath>
            <xpath expr="//field[@name='product_tag_ids']" position="after">
                <field name="th_origin_id" options="{'no_create': True, 'no_open': True}"/>
            </xpath>
        </field>
    </record>
</odoo>
