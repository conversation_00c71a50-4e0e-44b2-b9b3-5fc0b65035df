from odoo import fields, models


class HrDepartureWizard(models.TransientModel):
    _inherit = "hr.departure.wizard"


    def action_register_departure(self):
        employee = self.employee_id
        res = super(HrDepartureWizard, self).action_register_departure()
        self.env['th.salary.range.code.history'].search([('th_employee_id', '=', employee.id)]).active = False
        self.env['th.basic.salary'].search([('th_employee_id', '=', employee.id)]).active = False
        return res