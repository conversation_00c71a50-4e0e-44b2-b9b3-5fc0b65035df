<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- <PERSON><PERSON> hội bị trùng -->
    <record id="th_apm_duplicate_tree_view" model="ir.ui.view">
        <field name="name">th.apm.duplicate.tree</field>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <tree string="Cơ hội đang trùng" create="false" edit="false" delete="false">
                <field name="name"/>
                <field name="th_partner_id"/>
                <field name="th_partner_phone"/>
                <field name="th_partner_email" string="Email"/>
                <field name="th_origin_id"/>
                <field name="th_stage_id"/>
                <field name="th_status_detail_id"/>
                <field name="th_duplicate_type"/>
                <field name="th_selection_dup_result"/>
                <field name="th_lead_apm_source_id"/>
                <field name="create_date"/>
                <field name="th_duplicate_date"/>
                <field name="th_user_id"/>
                <field name="th_status_category_id" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="th_apm_duplicate_inherit_form_view" model="ir.ui.view">
        <field name="name">th.apm.duplicate.form</field>
        <field name="inherit_id" ref="th_apm.apm_lead_view_form"/>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <!-- Thêm nút khiếu nại vào header -->
            <xpath expr="//header" position="inside">
                <button name="th_action_complaint" string="Khiếu nại" type="object"
                        class="oe_highlight"
                        attrs="{'invisible': ['|', ('th_is_a_duplicate_opportunity', '=', False), ('th_is_under_complaint', '=', True)]}"/>
            </xpath>
            <xpath expr="//widget[@name='web_ribbon']" position="after">
                <!-- <widget name="web_ribbon" title="Đang khiếu nại" bg_color="bg-warning" invisible="not context.get('th_duplicate_type', False)"/> -->
                <widget name="web_ribbon" title="Đang khiếu nại" bg_color="bg-warning" attrs="{'invisible': ['|','|',('th_is_a_duplicate_opportunity', '=', False),('th_is_under_complaint', '=', False),('th_reason', '!=', False)]}"/>
                <widget name="web_ribbon" title="Cơ hội trùng" bg_color="bg-danger" attrs="{'invisible': ['|','|',('th_is_a_duplicate_opportunity', '=', False),('th_is_under_complaint', '=', True),('th_reason', '!=', False)]}"/>
                <widget name="web_ribbon" title="Cơ hội đóng" bg_color="bg-danger" attrs="{'invisible': [('th_is_close_lead', '=', False)]}"/>
            </xpath>
            <field name="th_is_a_duplicate_opportunity" position="after">
                <field name="th_dup_need_admin" invisible="1"/>
                <field name="th_is_under_complaint" invisible="1"/>
                <field name="th_is_close_lead" invisible="1"/>
            </field>
        </field>
    </record>

    <record id="th_apm_duplicate_search_view" model="ir.ui.view">
        <field name="name">th.apm.duplicate.search</field>
        <field name="model">th.apm</field>
        <field name="priority">99</field>
        <field name="arch" type="xml">
            <search string="Tìm kiếm cơ hội trùng">
                <field name="name"/>
                <field name="th_partner_id"/>
                <field name="th_partner_phone"/>
                <field name="th_partner_email" string="Email"/>
                <field name="th_origin_id"/>
                <field name="th_duplicate_type"/>
                    <field name="th_selection_dup_result"/>
                <field name="th_lead_apm_source_id"/>
                <field name="th_user_id"/>
                <separator/>
                <filter string="Tự động" name="auto" domain="[('th_duplicate_type', '=', 'auto')]"/>
                <filter string="Thủ công" name="manual" domain="[('th_duplicate_type', '=', 'manual')]"/>
                <filter string="Cần xử lý" name="need_handle" domain="[('th_duplicate_type', '=', 'need_handle')]"/>
                <filter string="Chưa có điều kiện" name="no_results" domain="[('th_duplicate_type', '=', 'no_results')]"/>
                <separator/>
                <searchpanel>
                    <field name="th_stage_id" icon="fa-user-plus" enable_counters="1"/>
                    <field name="th_status_category_id" icon="fa-phone" enable_counters="1"/>
                    <field name="th_status_detail_id" icon="fa-phone" enable_counters="1"/>
                    <field name="th_user_id" icon="fa-user-plus" enable_counters="1"/>
                    <field name="th_duplicate_type" icon="fa-user-plus" enable_counters="1"/>
                    <field name="th_selection_dup_result" icon="fa-user-plus" enable_counters="1"/>
                </searchpanel>
                <filter string="Trùng-Giữ" name="keep" domain="[('th_selection_dup_result', '=', 'keep')]"/>
                <filter string="Trùng-Chuyển" name="change" domain="[('th_selection_dup_result', '=', 'change')]"/>
                <group expand="0" string="Group By">
                    <filter string="Loại xử lý" name="group_by_duplicate_type" domain="[]" context="{'group_by': 'th_duplicate_type'}"/>
                    <filter string="Kết quả" name="group_by_selection_dup_result" domain="[]" context="{'group_by': 'th_selection_dup_result'}"/>
                    <filter string="Dòng sản phẩm" name="group_by_origin" domain="[]" context="{'group_by': 'th_origin_id'}"/>
                    <filter string="Người chăm sóc" name="group_by_user" domain="[]" context="{'group_by': 'th_user_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Lịch sử kiểm tra trùng -->
    <record id="th_apm_duplicate_check_history_tree_view" model="ir.ui.view">
        <field name="name">th.apm.duplicate.check.history.tree</field>
        <field name="model">th.apm.duplicate.check.history</field>
        <field name="arch" type="xml">
            <tree string="Lịch sử kiểm tra trùng">
                <field name="th_lead_apm_id_old"/>
                <field name="th_lead_apm_id_new"/>
                <field name="th_name_lead_lose"/>
                <field name="th_duplicate_type"/>
                <field name="th_origin_id"/>
                <field name="th_stage_id"/>
                <field name="th_status_detail_id"/>
                <field name="th_last_check"/>
                <field name="user_id"/>
                <button name="th_action_merge" string="Chuyển" type="object" class="oe_highlight"/>
            </tree>
        </field>
    </record>

    <record id="th_apm_duplicate_check_history_form_view" model="ir.ui.view">
        <field name="name">th.apm.duplicate.check.history.form</field>
        <field name="model">th.apm.duplicate.check.history</field>
        <field name="arch" type="xml">
            <form string="Lịch sử kiểm tra trùng">
                <header>
                    <button name="th_action_merge" string="Chuyển" type="object" class="oe_highlight"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="th_name_lead_lose" readonly="1"/>
                            <field name="th_duplicate_type" readonly="1"/>
                            <field name="th_origin_id" readonly="1"  options="{'no_create': True, 'no_open': True}"/>
                            <field name="th_stage_id" readonly="1" options="{'no_create': True, 'no_open': True}"/>
                            <field name="th_status_group_id" readonly="1" options="{'no_create': True, 'no_open': True}"/>
                        </group>
                        <group>
                            <field name="th_status_detail_id" readonly="1" options="{'no_create': True, 'no_open': True}"/>
                            <field name="th_last_check" readonly="1"/>
                            <field name="user_id" readonly="1"/>
                            <field name="th_lead_apm_id_old" readonly="1"/>
                            <field name="th_lead_apm_id_new" readonly="1"/>
                        </group>
                    </group>
                    <group>
                        <field name="th_description" readonly="1"/>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="th_apm_duplicate_check_history_search_view" model="ir.ui.view">
        <field name="name">th.apm.duplicate.check.history.search</field>
        <field name="model">th.apm.duplicate.check.history</field>
        <field name="arch" type="xml">
            <search string="Tìm kiếm lịch sử kiểm tra trùng">
                <field name="th_lead_apm_id_old"/>
                <field name="th_lead_apm_id_new"/>
                <field name="th_name_lead_lose"/>
                <field name="th_duplicate_type"/>
                <field name="th_origin_id"/>
                <field name="user_id"/>
                <separator/>
                <filter string="Tự động" name="auto" domain="[('th_duplicate_type', '=', 'auto')]"/>
                <filter string="Thủ công" name="manual" domain="[('th_duplicate_type', '=', 'manual')]"/>
                <filter string="Cần xử lý" name="need_handle" domain="[('th_duplicate_type', '=', 'need_handle')]"/>
                <filter string="Chưa có điều kiện" name="no_results" domain="[('th_duplicate_type', '=', 'no_results')]"/>
                <group expand="0" string="Group By">
                    <filter string="Loại xử lý" name="group_by_duplicate_type" domain="[]" context="{'group_by': 'th_duplicate_type'}"/>
                    <filter string="Dòng sản phẩm" name="group_by_origin" domain="[]" context="{'group_by': 'th_origin_id'}"/>
                    <filter string="Người chăm sóc" name="group_by_user" domain="[]" context="{'group_by': 'user_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Actions -->
    <record id="th_action_apm_duplicate" model="ir.actions.act_window">
        <field name="name">Cơ hội đang trùng</field>
        <field name="res_model">th.apm</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('th_is_a_duplicate_opportunity', '=', True)]</field>
        <field name="context">{'create': False}</field>
        <field name="view_id" ref="th_apm_duplicate_tree_view"/>
        <field name="search_view_id" ref="th_apm_duplicate_search_view"/>
    </record>

    <record id="th_action_apm_duplicate_check_history" model="ir.actions.act_window">
        <field name="name">Lịch sử kiểm tra trùng</field>
        <field name="res_model">th.apm.duplicate.check.history</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'create': False}</field>
        <field name="view_id" ref="th_apm_duplicate_check_history_tree_view"/>
        <field name="search_view_id" ref="th_apm_duplicate_check_history_search_view"/>
    </record>

</odoo>
