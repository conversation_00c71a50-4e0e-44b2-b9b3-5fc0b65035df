<odoo>
    <record id="attendance_request_approve_compensation_view_tree" model="ir.ui.view">
        <field name="name">attendance_request_approve_compensation_view_tree</field>
        <field name="model">th.attendance.request.approve</field>
        <field name="arch" type="xml">
<!--            <tree string="" js_class="attendance_approve_list" editable="bottom" export_xlsx="0" decoration-danger ="th_state == 'new'" decoration-warning ="th_state == 'edit'">-->
            <tree string="" js_class="attendance_approve_list" edit="0" create="0" delete="0" export_xlsx="0" decoration-danger ="th_state == 'new'" >
                <field name="th_employee_id" readonly="1"/>
                <field name="th_time" widget="float_time"/>
                <field name="th_attendance_request_id" invisible="1"/>
                <field name="th_attendance_id" invisible="1"/>
                <field name="th_state" invisible="1"/>
            </tree>
        </field>
    </record>
    <record id="attendance_request_approve_excess_work_view_tree" model="ir.ui.view">
        <field name="name">attendance_request_approve_excess_work_view_tree</field>
        <field name="model">th.attendance.request.approve</field>
        <field name="arch" type="xml">
            <tree string="" js_class="attendance_approve_list" create="0" editable="bottom" delete="1" export_xlsx="0" decoration-danger="th_state == 'new'" decoration-warning ="th_state == 'edit'">
                <field name="th_employee_id" readonly="1"/>
                <field name="th_time" widget="float_time"/>
                <field name="th_attendance_request_id" invisible="1"/>
                <field name="th_attendance_id" invisible="1"/>
                <field name="th_state" invisible="1"/>
            </tree>
        </field>
    </record>

</odoo>