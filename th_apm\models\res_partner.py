from odoo import fields, models, api, _
from odoo.exceptions import ValidationError, UserError


class ResPartner(models.Model):
    _inherit = "res.partner"

    th_apm_contact_trait_ids = fields.One2many("th.apm.contact.trait", "th_partner_id", string="Đặc điểm")
    th_lead_apm_count = fields.Integer(string="Cơ hội APM", compute="_compute_th_lead_apm_count")
    th_check_module_apm = fields.Boolean(compute="_compute_th_check_module_apm")
    phone = fields.Char(unaccent=False)
    th_is_order_apm = fields.<PERSON><PERSON><PERSON>(string="Có đơn hàng APM", default=False)
    th_apm_team_id = fields.Many2one(comodel_name="th.apm.team", string="Đội chăm sóc")
    th_apm_lead_ids = fields.One2many("th.apm", "th_partner_id", string="Cơ hội APM")
    th_origin_ids = fields.Many2many(comodel_name='th.origin', string="Dòng sản phẩm")

    @api.depends('th_apm_lead_ids')
    def _compute_th_lead_apm_count(self):
        for rec in self:
            rec.th_lead_apm_count = len(rec.th_apm_lead_ids)

    @api.depends("th_module_ids")
    def _compute_th_check_module_apm(self):
        for rec in self:
            rec.th_check_module_apm = False
            if self.env.ref('th_setup_parameters.th_apm_module').id in rec.th_module_ids.ids:
                rec.th_check_module_apm = True

    def action_view_apm_lead(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Cơ hội APM',
            'view_mode': 'tree,form',
            'res_model': 'th.apm',
            'target': 'current',
            'domain': [('th_partner_id', '=', self.id)],
            'context': {'create': False}
        }

    @api.model_create_multi
    def create(self, vals_list):
        if self._context.get('apm_contact'):
            for vals in vals_list:
                if self._context.get('import_file'):
                    vals['th_module_ids'] = [[6, 0, [self.env.ref('th_setup_parameters.th_apm_module').id]]]
                # vals['th_module_ids'] = [[4, self.env.ref('th_setup_parameters.th_apm_module').id]]
                # if self.env.ref('th_setup_parameters.th_apm_module').id not in vals.get('th_module_ids')[0][2]:
                #     raise ValidationError('Phải chọn module APM khi tạo liên hệ tiềm năng trong APM')
        # Add code here
        return super(ResPartner, self).create(vals_list)

    def write(self, values):
        contact_type_id = self.env.ref('th_setup_parameters.th_apm_module').id
        if values.get('th_apm_contact_trait_ids', False) and contact_type_id:
            values['th_contact_type_ids'] = [[4, contact_type_id]]
        if any(values.get(key) for key in ['name', 'phone', 'email', 'th_customer_code']):
            active_accounts = self.env['th.apm.active.account'].search(
                [('th_customer_name', '=', self.name), ('th_customer_phone', '=', self.phone),
                 ('th_customer_email', '=', self.email), ('th_customer_code', '=', self.th_customer_code)])
            for rec in active_accounts:
                rec.sudo().write({
                    'th_customer_name': values.get('name', self.name),
                    'th_customer_phone': values.get('phone', self.phone),
                    'th_customer_email': values.get('email', self.email),
                    'th_customer_code': values.get('th_customer_code', self.th_customer_code),
                })
        return super(ResPartner, self).write(values)

        # for rec in self:
        #     if not values.get('th_apm_contact_trait_ids'):
        #         return res
        #     vals = []
        #     apm_leads = self.env['th.apm'].search([('th_partner_id', '=', rec.id)])
        #     for trait in rec.th_apm_contact_trait_ids:
        #         vals.append((0, 0, {
        #             'th_product_line_id': trait.th_product_line_id.id,
        #             'th_apm_trait_id': trait.th_apm_trait_id.id,
        #             'th_apm_trait_value_ids': [(6, 0, trait.th_apm_trait_value_ids.ids)],
        #             'th_apm_contact_trait_id': trait.id
        #         }))
        #
        #     for apm_lead in apm_leads:
        #         lead = apm_lead.with_context(write_one=True).write({
        #             'th_apm_lead_trait_ids': [(5, 0, 0), ] + vals
        #         })
        # return res

    @api.model
    def default_get(self, field_list):
        result = super().default_get(field_list)
        if self._context.get('apm_contact'):
            result['th_module_ids'] = [[6, 0, [self.env.ref('th_setup_parameters.th_apm_module').id]]]
        return result

    @api.onchange('phone')
    def _onchange_phone(self):
        if self.phone:
            self.phone = self.phone.replace(" ", "")
            if not self.phone.startswith('0'):
                self.phone = '0' + self.phone

    @api.onchange('th_phone2')
    def _onchange_phone2(self):
        if self.th_phone2:
            self.th_phone2 = self.th_phone2.replace(" ", "")
            if not self.th_phone2.startswith('0'):
                self.th_phone2 = '0' + self.th_phone2


    def th_action_open_order_apm(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Đơn hàng',
            'view_mode': 'tree,form',
            'res_model': 'sale.order',
            'target': 'current',
            'domain': [('partner_id', '=', self.id)],
            'context': {'create': False}
        }

    def th_create_lead_apm_after_sales_care(self):
        for rec in self:
            if self.env.context.get('ome'):
                origin_to_remove = self.env.ref('th_setup_parameters.th_origin_vmc')
                rec.th_origin_ids = [(3, origin_to_remove.id, 0)]
                
                lead_parter = self.env['th.apm'].search(
                [('th_partner_id', '=', rec.id), ('th_order_id', '!=', False), ('th_origin_id', '=', origin_to_remove.id), ('th_after_sales_care', '!=', True), ('th_order_id', '!=', False), ('th_after_sales_care', '!=', True)])
            elif self.env.context.get('vstep'):
                origin_to_remove = self.env.ref('th_setup_parameters.th_origin_vstep')
                rec.th_origin_ids = [(3, origin_to_remove.id, 0)]
                
                lead_parter = self.env['th.apm'].search(
                [('th_partner_id', '=', rec.id), ('th_order_id', '!=', False), ('th_origin_id', '=', origin_to_remove.id), ('th_after_sales_care', '!=', True), ('th_order_id', '!=', False), ('th_after_sales_care', '!=', True)])
            
            if len(lead_parter) == 1 or len(lead_parter) > 1 and lead_parter.mapped('th_source_group_id') == 1 and lead_parter.mapped('th_ownership_unit_id') <= 1 and lead_parter.mapped('th_partner_reference_id') <= 1:
                # lead_parter.mapped('lead_parter.th_source_group_id')
                values = {
                    'th_partner_id': rec.id,
                    'th_after_sales_care': True,
                    'th_campaign_id': self.env.ref('th_apm.campaign_after_sales_care').id,
                    'th_is_lead_TTVH': True,
                    'th_ownership_unit_id': lead_parter.th_ownership_unit_id.id,
                    'th_source_group_id': lead_parter.th_source_group_id.id,
                    'th_partner_reference_id': lead_parter.th_partner_reference_id.id,
                    # 'th_order_history_ids': [(5, 0, 0)] + vals,
                    'th_product_ids': lead_parter.th_product_ids.ids,
                }
            else:
                values = {
                    'th_partner_id': rec.id,
                    'th_after_sales_care': True,
                    'th_campaign_id': self.env.ref('th_apm.campaign_after_sales_care').id,
                    'th_is_lead_TTVH': True,
                    'th_ownership_unit_id': False,
                    'th_source_group_id': False,
                    'th_partner_reference_id': False,
                    'th_product_ids': lead_parter.th_product_ids.ids,
                    # 'th_order_history_ids': [(5, 0, 0)] + vals,
                }
            if self.env.context.get('ome'):
                values['th_origin_id'] = self.env.ref('th_setup_parameters.th_origin_vmc').id
            elif self.env.context.get('vstep'):
                values['th_origin_id'] = self.env.ref('th_setup_parameters.th_origin_vstep').id
            check_duplicate_lead = self.env['th.apm'].search(
                [('th_partner_id', '=', rec.id), ('th_is_lead_TTVH', '=', True), ('th_after_sales_care', '=', True),
                 ('th_reason', '=', None),('th_origin_id', '=', values['th_origin_id'])])
            if not check_duplicate_lead:
                # raise UserError(
                #     'Chỉ được tạo một cơ hội sau bán tương ứng cho một liên hệ! Vui lòng dừng chăm sóc hoặc tạo đơn hàng cho cơ hội trước đó trước khi tạo cơ hội mới!')
                self.env['th.apm'].create(values)
            else:
                continue

        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    # Xóa liên hệ sau bán tạm thời
    def th_delete_lead_apm_after_sales_care(self):
        for rec in self:
            rec.th_is_order_apm = False

