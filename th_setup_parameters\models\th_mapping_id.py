
from odoo import api, models, fields
from odoo.exceptions import ValidationError


class ThSaveIDSync(models.Model):

    _name = "th.mapping.id"
    _description = "Th mapping ID"

    name = fields.Char('Name')
    th_model_name = fields.Char('Tên model')
    th_module_id = fields.Many2one(comodel_name='therp.module', string='<PERSON>ân hệ')
    th_internal_id = fields.Integer(string="ID hệ thống nội bộ")
    th_external_id = fields.Integer(string="ID hệ thống bên ngoài")
    th_system = fields.Selection(selection=[('b2b', 'B2B'), ('b2c', 'B2C')], string="Hệ thống")