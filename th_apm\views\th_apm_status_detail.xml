<odoo>
    <record id="th_status_category_view_form_apm" model="ir.ui.view">
        <field name="name">th_status_category_view_form_apm</field>
        <field name="model">th.status.category</field>
        <field name="arch" type="xml">
            <form string="">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="th_type" invisible="1"/>
                        <field name="th_apm_level_category" widget="many2many_tags" string="Mối quan hệ"/>
                        <field name="th_description"/>
                        <field name="th_categ_after_sale"/>
                    </group>
                    <notebook>
                        <page name="state_detail" string="Trạng thái chi tiết">
                            <field name="th_status_detail_ids">
                                <tree editable="bottom">
                                    <field name="name"/>
                                    <field name="th_description"/>
                                    <field name="th_apm_level_ids" string="Mối quan hệ" widget="many2many_tags" attrs="{'required': [('name', '!=', False)]}" domain="[('id' , 'in', parent.th_apm_level_category)]"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
<!--                <div class="oe_chatter">-->
<!--                    <field name="message_follower_ids"/>-->
<!--                    <field name="activity_ids"/>-->
<!--                    <field name="message_ids"/>-->
<!--                </div>-->
            </form>
        </field>
    </record>

    <record id="th_status_category_view_act_apm" model="ir.actions.act_window">
        <field name="name">Nhóm trạng thái</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.status.category</field>
        <field name="domain">[('th_type','=','apm')]</field>
        <field name="context">{'default_th_type': 'apm'}</field>
        <field name="view_mode">tree,form</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('th_setup_parameters.th_call_status_category_view_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_status_category_view_form_apm')})]"/>

    </record>
</odoo>