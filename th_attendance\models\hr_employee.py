# -*- coding: utf-8 -*-
import datetime

from odoo import models, fields, api, Command, _, exceptions
import pytz, time
from dateutil.relativedelta import relativedelta
from datetime import date, timedelta

from odoo.exceptions import ValidationError
from odoo.tools.misc import format_date
from odoo.http import request

class HrEmployee(models.Model):

    _inherit = 'hr.employee'
    _sql_constraints = [
        ('unique_employee_pin', 'UNIQUE(pin)', _('No duplication of PIN numbers is allowed'))
    ]

    th_miss_att = fields.Bo<PERSON>an('Missed Attendance')
    th_report_department = fields.Char('Department AUM', compute='compute_th_report_department')

    @api.model_create_multi
    def create(self, vals_list):
        # self.th_check_pin_employee(va)
        res = super(HrEmployee, self).create(vals_list)
        for vals in vals_list:
            if vals.get('pin'):
                # Collect all pins from the created records
                pins = [rec.pin for rec in res if rec.pin]
                if not pins:
                    continue

                # Make a single query to get all unknown attendance records
                all_att_unknown = self.env['th.att.unknown.employee'].search([('th_pin', 'in', pins)])

                # Group records by pin for easy access
                att_unknown_by_pin = {}
                for att in all_att_unknown:
                    if att.th_pin not in att_unknown_by_pin:
                        att_unknown_by_pin[att.th_pin] = []
                    att_unknown_by_pin[att.th_pin].append(att)

                # Process the data
                vals_list_att = []
                for rec in res:
                    if not rec.pin or rec.pin not in att_unknown_by_pin:
                        continue

                    att_unknown_records = att_unknown_by_pin[rec.pin]
                    vals_list_att += [{'employee_id': rec.id,
                                       'th_attendance_number': rec.pin,
                                       'check_in': datetime.datetime.strptime((att.th_attendance_date.strftime('%Y-%m-%d')+ ' ' + att.th_checkin_time), '%Y-%m-%d %H:%M:%S' if len(att.th_checkin_time.split(':'))>=3 else '%Y-%m-%d %H:%M') - timedelta(hours=7) if att.th_checkin_time else False,
                                       'check_out': datetime.datetime.strptime((att.th_attendance_date.strftime('%Y-%m-%d')+ ' ' + att.th_checkout_time), '%Y-%m-%d %H:%M:%S' if len(att.th_checkin_time.split(':'))>=3 else '%Y-%m-%d %H:%M') - timedelta(hours=7) if att.th_checkout_time else False} for att in att_unknown_records]

                # Mark all processed records as inactive in a single write operation
                if all_att_unknown:
                    all_att_unknown.write({'active': False})

                # Create attendance records
                if vals_list_att:
                    self.env['hr.attendance'].create(vals_list_att)
        return res

    def write(self, values):
        # Add code here
        for rec in self:
            if values.get('pin'):
                unknown_employee = self.env['th.att.unknown.employee'].search([('th_pin', '=', rec.pin), ('active', '=', False)], order="th_attendance_date desc")
                unknown_employee.write({'active': True})
                if unknown_employee:
                    attendance_ids = self.env['hr.attendance'].search([("th_attendance_date", ">", unknown_employee[0].th_attendance_date), ("employee_id", "=", rec.id)])
                    values_unknown = []
                    for attendance_id in attendance_ids:
                        values_unknown += [{
                            'th_pin': rec.pin,
                            'th_emp_name': unknown_employee[0].th_emp_name,
                            'th_attendance_date': attendance_id.th_attendance_date,
                            'th_checkin_time': (attendance_id.check_in + timedelta(hours=7)).strftime("%H:%M:%S") if attendance_id.check_in else False,
                            'th_checkout_time': (attendance_id.check_out + timedelta(hours=7)).strftime("%H:%M:%S") if attendance_id.check_out else False,
                        }]
                    self.env['th.att.unknown.employee'].create(values_unknown)
                self.env['hr.attendance'].search([('employee_id', '=', rec.id)]).write({'active': False})
        res = super(HrEmployee, self).write(values)
        if values.get('pin'):
            # Collect all pins from the records
            pins = [rec.pin for rec in self if rec.pin]
            if not pins:
                return res

            # Make a single query to get all unknown attendance records
            all_att_unknown = self.env['th.att.unknown.employee'].search([('th_pin', 'in', pins)])

            # Group records by pin for easy access
            att_unknown_by_pin = {}
            for att in all_att_unknown:
                if att.th_pin not in att_unknown_by_pin:
                    att_unknown_by_pin[att.th_pin] = []
                att_unknown_by_pin[att.th_pin].append(att)

            # Process the data
            vals_list_att = []
            for rec in self:
                if not rec.pin or rec.pin not in att_unknown_by_pin:
                    continue

                att_unknown_records = att_unknown_by_pin[rec.pin]
                vals_list_att += [{'employee_id': rec.id,
                                   'th_attendance_number': rec.pin,
                                   'th_att_code': "WORK100",
                                   'check_in': datetime.datetime.strptime((att.th_attendance_date.strftime('%Y-%m-%d') + ' ' + att.th_checkin_time),'%Y-%m-%d %H:%M:%S' if len(att.th_checkin_time.split(':'))>=3 else '%Y-%m-%d %H:%M') - timedelta(hours=7) if att.th_checkin_time else False,
                                   'check_out': datetime.datetime.strptime((att.th_attendance_date.strftime('%Y-%m-%d') + ' ' + att.th_checkout_time),'%Y-%m-%d %H:%M:%S' if len(att.th_checkin_time.split(':'))>=3 else '%Y-%m-%d %H:%M') - timedelta(hours=7) if att.th_checkout_time else False} for att in att_unknown_records]

            # Mark all processed records as inactive in a single write operation
            if all_att_unknown:
                all_att_unknown.write({'active': False})

            # Create attendance records
            if vals_list_att:
                self.env['hr.attendance'].create(vals_list_att)
        return res

    @api.onchange('pin')
    def onchange_check_pin_employee(self):
        for rec in self:
            if rec.pin and self.search([('pin', '=', rec.pin)]):
                raise ValidationError(_("Mã chấm công đã tồn tại trong hệ thống!"))

    def _attendance_action_change(self):
        client_ip = request.httprequest.environ['REMOTE_ADDR']
        """ Check In/Check Out action
            Check In: create a new attendance record
            Check Out: modify check_out field of appropriate attendance record
        """
        self.ensure_one()
        action_date = fields.Datetime.now()
        today_date = action_date.date()
        th_att_code = self.env['ir.config_parameter'].sudo().get_param('th_kiosk_att')
        attendance = self.env['hr.attendance'].search([('employee_id', '=', self.id),
                                                       ('check_out', '=', False),
                                                       ('th_att_code', '=', th_att_code),
                                                       ('th_attendance_date', '=', today_date)],
                                                      order='id desc',
                                                      limit=1)
        if attendance:
            attendance.check_out = action_date
            attendance.th_checkout_ip = client_ip
        else:
            vals = {
                'employee_id': self.id,
                'check_in': action_date,
                'th_checkin_ip': client_ip
            }
            return self.env['hr.attendance'].with_context({'is_kiosk_mode': True}).create(vals)

        return attendance

    @api.depends('attendance_ids')
    def _compute_last_attendance_id(self):
        th_att_code = self.env['ir.config_parameter'].sudo().get_param('th_kiosk_att')
        for employee in self:
            employee.last_attendance_id = self.env['hr.attendance'].search([
                ('employee_id', '=', employee.id),
                ('th_att_code', '=', th_att_code),
                ('th_attendance_date', '=', date.today())
            ], order='id desc', limit=1)

    def _compute_hours_today(self):
        now = fields.Datetime.now()
        now_utc = pytz.utc.localize(now)
        th_att_code = self.env['ir.config_parameter'].sudo().get_param('th_kiosk_att')
        for employee in self:
            # start of day in the employee's timezone might be the previous day in utc
            tz = pytz.timezone(employee.tz)
            now_tz = now_utc.astimezone(tz)
            start_tz = now_tz + relativedelta(hour=0, minute=0)  # day start in the employee's timezone
            start_naive = start_tz.astimezone(pytz.utc).replace(tzinfo=None)

            attendances = self.env['hr.attendance'].search([
                ('employee_id', '=', employee.id),
                ('check_in', '<=', now),
                ('th_att_code', '=', th_att_code),
                ('check_out', '>=', start_naive),
            ])

            worked_hours = 0
            for attendance in attendances:
                delta = (attendance.check_out or now) - max(attendance.check_in, start_naive)
                worked_hours += delta.total_seconds() / 3600.0
            employee.hours_today = worked_hours

    @api.depends('department_id')
    def compute_th_report_department(self):
        for rec in self:
            rec.th_report_department = rec.department_id.name if rec.department_id else False
