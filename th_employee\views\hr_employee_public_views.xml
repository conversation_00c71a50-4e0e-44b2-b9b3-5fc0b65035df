<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="th_employee_public_view_form" model="ir.ui.view">
        <field name="name">hr.employee.public.form</field>
        <field name="model">hr.employee.public</field>
        <field name="inherit_id" ref="hr.hr_employee_public_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='mobile_phone']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='work_phone']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='parent_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='coach_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='employee_type']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//div[@id='o_work_employee_main']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='address_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='work_location_id']" position="after">
                <field name="th_address" options="{'no_open': True, 'no_create': True}"/>
            </xpath>
        </field>
    </record>

    <record id="th_employee_public_view_kanban" model="ir.ui.view">
        <field name="name">hr.employee.public.kanban</field>
        <field name="model">hr.employee.public</field>
        <field name="inherit_id" ref="hr.hr_employee_public_view_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='work_email']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='work_phone']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="th_employee_public_view_tree" model="ir.ui.view">
        <field name="name">hr.employee.public.tree</field>
        <field name="model">hr.employee.public</field>
        <field name="inherit_id" ref="hr.hr_employee_public_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='work_email']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='work_phone']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='parent_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>
</odoo>