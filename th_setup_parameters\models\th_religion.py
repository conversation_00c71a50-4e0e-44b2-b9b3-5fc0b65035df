from odoo import models, fields, api
import xmlrpc


class ThReligion(models.Model):
    _name = 'th.religion'
    _description = 'Tôn giáo'

    name = fields.Char(string='Tôn giáo', required=True)
    th_religion_b2b_id = fields.Integer("Id Tôn giáo B2B", copy=False)
    active = fields.Bo<PERSON>an('Active', default=True)
    # @api.model
    # def create(self, values):
    #     res = super(ThReligion, self).create(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in res:
    #             rec.th_action_sync_b2b_religion(rec)
    #     return res
    #
    # def write(self, values):
    #     res = super(ThReligion, self).write(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in self:
    #             rec.th_action_sync_b2b_religion(rec)
    #     return res
    #
    # def unlink(self):
    #     for rec in self:
    #         rec.with_context(is_delete=True).th_action_sync_b2b_religion(rec)
    #     res = super(ThReligion, self).unlink()
    #     return res

    # def th_action_sync_b2b_religion(self, res):
    #     vals = {}
    #     if not res:
    #         return
    #     server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1,
    #                                                   order='id desc')
    #     if not server_api:
    #         return False
    #     try:
    #         result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         for rec in res:
    #             vals = {'name': rec.name,
    #                     'th_religion_samp_id': rec.id}
    #             if not rec.th_religion_b2b_id and not self._context.get('is_delete'):
    #                 religion = result_apis.execute_kw(db, uid_api, password, 'th.religion', 'create', [vals])
    #                 rec.with_context(th_test_import=True).write({'th_religion_b2b_id': religion})
    #             else:
    #                 if not self._context.get('is_delete'):
    #                     religion = result_apis.execute_kw(db, uid_api, password, 'th.religion', 'write',
    #                                                     [[rec.th_religion_b2b_id], vals])
    #                 else:
    #                     religion = result_apis.execute_kw(db, uid_api, password, 'th.religion', 'unlink',
    #                                                     [[rec.th_religion_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(res.ids),
    #             'th_input_data': str(vals),
    #             'th_function_call': str('th_action_sync_b2b_religion'),
    #         })
    #         return