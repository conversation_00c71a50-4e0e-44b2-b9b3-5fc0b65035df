from odoo import fields, models, api
import xmlrpc.client
from markupsafe import Markup

from odoo.exceptions import ValidationError


class MailMessage(models.Model):
    _inherit = ['mail.message']

    th_aff_mail_message_id = fields.Integer(string="ID Mail message AFF", copy=False)

    @api.model
    def update_th_last_check(self, model, record_id):
        record_model = self.env[model]
        record = record_model.browse(record_id)
        if record.exists():
            record.write({
                'th_last_check': fields.Datetime.now()
            })
            return True
        return False

    # @api.model_create_multi
    # def create(self, vals_list):
    #     res = super(MailMessage, self).create(vals_list)
    #     for rec in res:
    #         if rec.model in ['crm.lead'] and not self._context.get('th_test_import', False):
    #             self.sudo().with_delay(priority=10).action_synchronized_mail_message(rec)
    #     return res
    # @api.model
    # def write(self, values):
    #     # Add code here
    #     rec = super(MailMessage, self).write(values)
    #     return rec
    # def action_synchronized_mail_message(self, res):
    #     data_to_send = {}
    #     if not res:
    #         return False
    #     server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1, order='id desc')
    #
    #     try:
    #         if not server_api:
    #             raise ValidationError('Không tìm thấy server!')
    #         result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
    #
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         author_id = server_api.th_partner_api_id
    #
    #         for rec in res:
    #             res_id = False
    #             if rec.model in ['crm.lead']:
    #                 res_id = self.env[rec.model].search([('id', '=', rec.res_id)]).th_crm_lead_b2b_id
    #             data_to_send = {
    #                 'date': rec.date,
    #                 'author_id': int(author_id),
    #                 'message_type': 'comment',
    #                 'model': rec.model,
    #                 'res_id': res_id,
    #                 'body': Markup(rec.body),
    #                 'th_samp_message_id': rec.id,
    #             }
    #             data_sync = result_apis.execute_kw(db, uid_api, password, 'mail.message', 'func_receive_message',
    #                                                [[], data_to_send, 'create'], {'context': {"th_test_import": True}})
    #             if data_sync:
    #                 rec.write({'th_aff_mail_message_id': data_sync.get('id', False)})
    #
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(res.ids),
    #             'th_input_data': str(data_to_send),
    #             'th_function_call': str('action_synchronized_mail_message'),
    #         })
    #         return True

    # def prepare_data_sync(self, datas=None):
    #     server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1, order='id desc')
    #     author_id = server_api.th_partner_api_id
    #
    #     data_sync = []
    #     for rec in datas:
    #         res_id = self.env[rec.model].search([('id', '=', rec.res_id)]).th_crm_lead_b2b_id
    #         if not res_id:
    #             continue
    #         data_to_send = {
    #             'date': rec.date,
    #             'author_id': int(author_id),
    #             'message_type': 'comment',
    #             'model': rec.model,
    #             'res_id': res_id,
    #             'body': Markup(rec.body),
    #             'th_samp_message_id': rec.id,
    #         }
    #         data_sync.append([rec.th_aff_mail_message_id or 0, data_to_send])
    #     return data_sync
    #
    # def th_sync_message(self, create=True):
    #     domain = [('message_type', '=', 'comment'), ('model', '=', 'crm.lead')]
    #
    #     if create:
    #         domain.append(('th_aff_mail_message_id', 'in', [False, 0]))
    #
    #     datas = self.env['mail.message'].search(domain)
    #     chunk_list = [datas[i:i + 100] for i in range(0, len(datas), 100)]
    #     for chunk in chunk_list:
    #         data_sync = self.env['mail.message'].prepare_data_sync(chunk)
    #         if data_sync:
    #             data = chunk.sudo().with_delay(priority=20).th_sync_mess_crm_c1(data_sync)
    #
    # def th_sync_mess_crm_c1(self, data_sync):
    #     server_api = self.sudo().env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1, order='id desc')
    #     if not server_api:
    #         raise ValidationError('Không tìm thấy server!')
    #     context = {'aff_sch': True}
    #     result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
    #     db = server_api.th_db_api
    #     uid_api = server_api.th_uid_api
    #     password = server_api.th_password
    #     try:
    #         data = result_apis.execute_kw(db, uid_api, password, 'mail.message', 'th_create_message_sch', [[], data_sync], {'context': context})
    #         for rec in data:
    #             self.browse(rec[0]).sudo().write(rec[1])
    #     except Exception as e:
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': self.ids,
    #             'th_input_data': str(data_sync),
    #             'th_function_call': str('th_sync_mess_crm_c1'),
    #         })
    #         return False
    #     return
