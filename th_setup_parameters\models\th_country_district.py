import xmlrpc

from odoo import models, fields, api, exceptions, _
from odoo.exceptions import ValidationError
from odoo.osv import expression

state = [('yes', 'Đã đẩy'), ('no', 'Ch<PERSON>a đẩy'), ('not_pull', 'Đẩy lỗi')]


class ThCountryDistrict(models.Model):
    _name = 'th.country.district'
    _description = 'Quận/Huyện'

    name = fields.Char(string='Quận/ Huyện', required=True)
    th_ref = fields.Char(string='Mã Quận/ Huyện', required=True)
    th_state_id = fields.Many2one(comodel_name='res.country.state', required=True, string='Tỉnh/ Thành phố', domain="[('country_id.code', '=', 'VN')]")
    th_c_district_b2b_id = fields.Integer("ID Quận/Huyện B2B", copy=False)

    _sql_constraints = [
        ('name_th_ref_uniq', 'unique(th_state_id, th_ref)', '<PERSON>ã của Quận/<PERSON>yện không được trùng ở mỗi Tỉnh/TP !')
    ]
    # @api.model
    # def create(self, values):
    #     res = super(ThCountryDistrict, self).create(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in res:
    #             rec.th_action_sync_b2b_c_district(rec)
    #     return res
    #
    # def write(self, values):
    #     res = super(ThCountryDistrict, self).write(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in self:
    #             rec.th_action_sync_b2b_c_district(rec)
    #     return res
    #
    # def unlink(self):
    #     for rec in self:
    #         rec.with_context(is_delete=True).th_action_sync_b2b_c_district(rec)
    #     res = super(ThCountryDistrict, self).unlink()
    #     return res
    #
    # def th_action_sync_b2b_c_district(self, res):
    #     vals = {}
    #     if not res:
    #         return
    #     server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1,
    #                                                   order='id desc')
    #     try:
    #         if not server_api:
    #             raise ValidationError('Không tìm thấy server!')
    #         result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         for rec in res:
    #             vals = {'name': rec.name,
    #                     'th_ref': rec.th_ref,
    #                     'th_state_id': rec.th_state_id.th_r_c_state_b2b_id if rec.th_state_id.th_r_c_state_b2b_id else False,
    #                     'th_c_district_samp_id': rec.id}
    #             if not rec.th_c_district_b2b_id and not self._context.get('is_delete'):
    #                 c_district = result_apis.execute_kw(db, uid_api, password, 'th.country.district', 'create', [vals])
    #                 rec.with_context(th_test_import=True).write({'th_c_district_b2b_id': c_district})
    #             else:
    #                 if not self._context.get('is_delete'):
    #                     c_district = result_apis.execute_kw(db, uid_api, password, 'th.country.district', 'write',
    #                                                     [[rec.th_c_district_b2b_id], vals])
    #                 else:
    #                     c_district = result_apis.execute_kw(db, uid_api, password, 'th.country.district', 'unlink',
    #                                                     [[rec.th_c_district_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(self.ids),
    #             'th_input_data': str(vals),
    #             'th_function_call': str('th_action_sync_b2b_c_district'),
    #         })
    #         return