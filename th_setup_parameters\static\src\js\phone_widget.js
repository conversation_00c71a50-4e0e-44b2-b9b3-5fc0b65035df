/** @odoo-module **/

import { patch } from "@web/core/utils/patch";
import { PhoneField } from "@web/views/fields/phone/phone_field";
import { useService } from "@web/core/utils/hooks";
import { useState } from "@odoo/owl";

patch(PhoneField.prototype, "th_setup_parameters.PhoneFieldPatch", {
    setup() {
    this._super(...arguments);

    this.state = useState({ enableWidgetPhone: false });
    this.phoneSettingsState = useState({ enableCallWidget: false });
    const rpc = useService("rpc");
    const modelName = this.props.record?.resModel || "";

    (async () => {
        try {
            const models = await rpc('/phone_widget/models', {});
            this.state.enableWidgetPhone = models.includes(modelName);
        } catch (err) {
            console.error("Phone widget config load failed:", err);
            this.state.enableWidgetPhone = false;
        }

        try {
            const config = await rpc('/phone_widget/get_settings', {});
            this.phoneSettingsState.enableCallWidget = !!config.enable_call_widget;
        } catch (err) {
            console.error("Phone widget settings load failed:", err);
            this.phoneSettingsState.enableCallWidget = false;
        }
    })();
}
});
