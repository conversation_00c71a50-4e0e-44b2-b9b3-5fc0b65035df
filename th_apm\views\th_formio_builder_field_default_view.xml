<odoo>
<!--    <record id="view_th_formio_builder_field_default_tree_inherit" model="ir.ui.view">-->
<!--        <field name="name">th.formio.builder.field.default.tree.inherit</field>-->
<!--        <field name="model">th.formio.builder.field.default</field>-->
<!--        <field name="inherit_id" ref="th_setup_parameters.th_formio_view_tree"/>-->
<!--        <field name="arch" type="xml">-->
<!--            <xpath expr="//field[@name='th_flag']" position="after">-->
<!--                <field name="th_apm_team_ids"/>-->
<!--            </xpath>-->
<!--        </field>-->
<!--    </record>-->
     <record id="view_th_formio_builder_field_default_apm_form_inherit" model="ir.ui.view">
        <field name="name">th.formio.builder.field.default.form.inherit</field>
        <field name="model">th.formio.builder.field.aff.default</field>
        <field name="inherit_id" ref="th_setup_parameters.th_formio_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='group_right']" position="inside">
                <field name="th_apm_dividing_ring_id"  attrs="{'invisible': [('th_module','!=','apm')]}"/>
                <field name="th_user_id_domain"  invisible="1"/>
            </xpath>
            <xpath expr="//group[@name='group_left']//field[@name='th_caregiver_ids']" position="attributes">
                <attribute name="domain">th_user_id_domain</attribute>
            </xpath>
        </field>
    </record>
    </odoo>