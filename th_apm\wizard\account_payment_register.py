import xmlrpc
from odoo import fields, models, api


class AccountPaymentRegister(models.TransientModel):
    _inherit = "account.payment.register"

    # Đã ngừng sử dụng và ẩn button (chuyển qua dùng STC)
    # def action_create_payments(self):
    #     res = super(AccountPaymentRegister, self).action_create_payments()
    #     if self._context.get('active_id'):
    #         account_move = self.env['account.move'].browse(self._context.get('active_id'))
    #         sale_order_id = self.env['sale.order'].search([('invoice_ids', 'in', account_move.ids)], limit=1)
    #         if account_move.payment_state == 'in_payment':
    #             account_move.payment_state = 'paid'
    #         if account_move.payment_state == 'paid':
    #             vmc_order = self.env['sale.order'].search([('invoice_ids', '=', account_move.id)])
    #             if vmc_order.th_order_vmc_id:
    #                 vmc_order.write({'th_status': 'completed'})
    #                 if account_move.move_type == 'out_refund':
    #                     vmc_order.write({'th_status': 'refund',
    #                                      'state': 'cancel'})
    #         if sale_order_id.th_apm_id and not any(sale_order_id.invoice_ids.filtered(lambda d: d.payment_state in ['not_paid', 'partial'])) and sale_order_id and sale_order_id.invoice_status == 'invoiced':
    #             if account_move.move_type == 'out_invoice':
    #                 sale_order_id.th_apm_id.th_partner_id.sudo().write({'th_is_order_apm': True})
    #                 th_search_apm = self.env['th.apm'].sudo().search(
    #                     [('th_partner_id', '=', sale_order_id.partner_id.id), ('th_is_lead_TTVH', '=', True),
    #                      ('th_after_sales_care', '=', True), ('th_reason', '=', None), ('th_order_id', '=', False)],
    #                     limit=1)
    #                 if th_search_apm:
    #                     th_search_apm.sudo().message_post(
    #                         message_type='notification',
    #                         body='Khách hàng vừa mua thêm đơn hàng và đã được cập nhật vào lịch sử mua hàng',
    #                     )
    #         if account_move.reversed_entry_id:
    #             sale_order_id.th_refunded_invoice_apm = True
    #             sale_order_id.th_refund_invoice_apm = False
    #             for rec in sale_order_id.invoice_ids:
    #                 if rec.reversed_entry_id:
    #                     rec.is_refund_invoice_date = fields.Date.today()
    #                 else:
    #                     rec.is_refund_invoice = True
    #             sale_order_id.th_refund_invoice_apm = False
    #             if sale_order_id.th_total_received_excessive and sale_order_id.th_total_refund_excessive and (
    #                     sale_order_id.th_total_received_excessive <= sale_order_id.th_total_refund_excessive):
    #                 sale_order_id.th_apm_id.th_partner_id.sudo().write({'th_is_order_apm': False})
    #     return res

    # def th_action_sync_status(self, vmc_order, vmc_order_name):
    #     th_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'vmc')], limit=1,
    #                                               order='id desc')
    #     if not th_api:
    #         return False
    #     try:
    #         result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(th_api.th_url_api))
    #     except Exception as e:
    #         print(e)
    #         return False
    #
    #     db = th_api.th_db_api
    #     uid_api = th_api.th_uid_api
    #     password = th_api.th_password
    #     try:
    #         orders = result_apis.execute_kw(db, uid_api, password, 'sale.order', 'search', [[['name', '=', vmc_order_name]]])
    #         result_apis.execute_kw(db, uid_api, password, 'sale.order', 'write', [orders, {'state': 'sale', 'status': 'completed'}])
    #         vmc_order.write({'th_api_to_vmc': True})
    #     except Exception as e:
    #         print(e)
    #         vmc_order.write({'th_api_to_vmc': False})
