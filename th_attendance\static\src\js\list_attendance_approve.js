/** @odoo-module **/

import {registry} from '@web/core/registry';
import {listView} from '@web/views/list/list_view';
import {ListController} from '@web/views/list/list_controller';
import { ConfirmationDialog } from "@web/core/confirmation_dialog/confirmation_dialog";
import rpc from 'web.rpc';

export class AttendanceListController extends ListController {
    setup() {
        super.setup()
        this.excess_work = this.props.context.excess_work
        if (this.excess_work == false){
            this.props.allowSelectors = false;
        }
    }

    async onClickConfirmAttendance() {

        const confirmed = await this.dialogService.add(ConfirmationDialog, {
            title: "Xác nhận",
            body: "Bạn có chắc chắn muốn xác nhận ?",
            confirm: async () => {
                await rpc.query({
                    model: this.model.root.resModel,
                    method: 'create_attendance',
                    args: [[], this.props.domain[0]],
                });
                return this.actionService.doAction({ type: "ir.actions.act_window_close" });
            },
            cancel: () => {},
        });
    }
    async onDeleteSelectedRecords() {
        const activeIds = await this.getSelectedResIds()
        if (activeIds.length == 0) {
            this.dialogService.add(ConfirmationDialog, {
                title: "Xác nhận",
                body: "Bạn chưa chọn bản ghi cần xóa",
                confirm: async () => {},
                cancel: () => {},
            });

        } else {
            super.onDeleteSelectedRecords()
        }
    }
}

export const attendaceapproveListView = {
    ...listView,
    Controller: AttendanceListController,
    buttonTemplate: "th_attendance.AttendanceApproveListView.Buttons",
}
registry.category('views').add('attendance_approve_list', attendaceapproveListView);