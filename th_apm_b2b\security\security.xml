<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="th_apm_b2b_category" model="ir.module.category">
        <field name="name">APM Đối tác</field>
        <field name="sequence">9</field>
    </record>
    <record id="th_apm_group_partner_manager" model="res.groups">
        <field name="name"><PERSON><PERSON><PERSON> t<PERSON></field>
        <field name="category_id" ref="th_apm_b2b_category"/>
        <field name="implied_ids" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
    </record>

    <record id="th_apm_group_partner_manager_rule" model="ir.rule">
        <field name="name">APM: <PERSON><PERSON><PERSON><PERSON> phép xem cơ hội thuộc sở hữu đối tác</field>
        <field name="model_id" ref="model_th_apm"/>
        <field name="groups" eval="[(4, ref('th_apm_group_partner_manager'))]"/>
        <field name="domain_force">[('th_ownership_unit_id.th_user_apm_b2b_ids', 'in',user.ids)]
        </field>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_unlink" eval="0"/>
    </record>
    <record id="th_apm_group_partner_manager_auto_rule" model="ir.rule">
        <field name="name">APM: Được phép xem cơ hội tự chốt</field>
        <field name="model_id" ref="model_th_apm"/>
        <field name="groups" eval="[(4, ref('th_apm_group_partner_manager'))]"/>
        <field name="domain_force">[('th_campaign_id.is_campaign_auto','=', True),('th_user_id', '=', False)]</field>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_create" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    <record id="th_apm_group_partner_other_rule" model="ir.rule">
        <field name="name">APM: Được phép xem cơ hội sau bán có lịch sử mua hàng đối tác</field>
        <field name="model_id" ref="model_th_apm"/>
        <field name="groups" eval="[(4, ref('th_apm_group_partner_manager'))]"/>
        <field name="domain_force">[('th_user_ownership_ids','in',user.ids)]</field>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_create" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    <record id="th_apm_rule_sale_order" model="ir.rule">
        <field name="name">APM: Được phép xem đơn hàng thuộc sở hữu đối tác</field>
        <field name="model_id" ref="model_sale_order"/>
        <field name="groups" eval="[(4, ref('th_apm_b2b.th_apm_group_partner_manager'))]"/>
        <field name="domain_force">[('th_apm_id','!=',False),('th_apm_id.th_ownership_unit_id.th_user_apm_b2b_ids', 'in', user.ids)]</field>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_create" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    <record id="th_apm_rule_sale_order_line" model="ir.rule">
        <field name="name">APM: Được phép xem chi tiết đơn hàng thuộc sở hữu đối tác</field>
        <field name="model_id" ref="th_apm.model_sale_order_line"/>
        <field name="groups" eval="[(4, ref('th_apm_b2b.th_apm_group_partner_manager'))]"/>
        <field name="domain_force">[('order_id','!=',False),('order_id.th_apm_id','!=',False),('order_id.th_apm_id.th_ownership_unit_id.th_user_apm_b2b_ids', 'in', user.ids)]</field>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_create" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    </odoo>
