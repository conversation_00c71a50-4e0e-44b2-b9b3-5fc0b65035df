<odoo>
    <record id="abs_hr_employee_view_form" model="ir.ui.view">
        <field name="name">abs.hr.employee.view.form</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="priority" eval="99"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="attributes">
                <attribute name="create">0</attribute>
            </xpath>
            <xpath expr="//page[@name='personal_information']" position="attributes">
                <attribute name="groups">hr.group_hr_user</attribute>
            </xpath>
            <xpath expr="//header" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//div[@name='button_box']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='coach_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='company_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='employee_type']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='country_of_birth']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='mobile_phone']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='phone']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//div[@name='div_km_home_work']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//page[@name='hr_settings']//group[@name='application_group']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='mobile_phone']" position="before">
                <field name="th_joined_date" required="1"/>
            </xpath>
            <xpath expr="//field[@name='parent_id']" position="after">
                <field name="th_state" required="1"/>
                <field name="th_employee_official_promotion_date"
                       attrs="{'invisible': [('th_state', '!=', 'official_staff')], 'required': [('th_state', '=', 'official_staff')]}"/>
<!--                <field name="th_leave_allow" invisible="0"/>-->
                <field name="th_employee_quit_date"
                       attrs="{'invisible': [('th_state', '!=', 'employee_quit')], 'required': [('th_state', '=', 'employee_quit')]}"/>
            </xpath>
            <xpath expr="//field[@name='th_state']" position="after">
                <field name="th_employee_official_probationary_date"
                       attrs="{'invisible': [('th_state', '!=', 'probationary')], 'required': [('th_state', '=', 'probationary')]}"/>
            </xpath>
            <xpath expr="//field[@name='job_title']" position="attributes">
                <attribute name="readonly">1</attribute>
                <attribute name="placeholder">Job Title</attribute>
            </xpath>
            <xpath expr="//field[@name='emergency_phone']" position="after">
                <field name="th_emergency_rela"/>
                <field name="th_emergency_address"/>
                <field name="th_partner_id" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='identification_id']" position="after">
                <field name="th_issued_date"/>
                <field name="th_place_of_issued"/>
            </xpath>
            <xpath expr="//field[@name='private_email']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='work_email']" position="after">
                <field name="th_email"/>
                <field name="th_email_personal"/>
                <field name="th_document" widget="work_permit_upload" file_name="th_document_name"/>
                <field name="th_document_name" invisible="1"/>
            </xpath>

            <xpath expr="//field[@name='children']" position="after">
                <div style="min-width: 400px; max-width: 400px">
                    <div class="d-flex align-items-end">
                        <field name="child_information">
                            <tree editable="bottom">
                                <field name="th_children_name" required="1"/>
                                <field name="th_birthday" required="1"/>
                                <field name="th_relationship"/>
                            </tree>
                        </field>
                    </div>
                </div>
            </xpath>

            <xpath expr="//page[@name='public']" position="before">
                <xpath expr="//page[@name='personal_information']" position="move"/>
                <page name="th_academic" string="Academic Qualifications">
                    <field name="th_qualifications" widget="section_and_note_one2many">
                        <tree string="Academic Qualifications" editable="bottom">
                            <field name="th_employee_id" invisible="1"/>
                            <field name="th_institution"/>
                            <field name="th_name_qlf"/>
                            <field name="th_date_qlf"/>
                            <field name="th_major"/>
                        </tree>
                    </field>
                </page>
            </xpath>
            <xpath expr="//field[@name='bank_account_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='address_home_id']" position="attributes">
                <attribute name="string">Liên hệ</attribute>
                <attribute name="context">{'show_address': 0}</attribute>
                <attribute name="domain">[('id', '=', th_partner_id)]</attribute>
            </xpath>

            <xpath expr="//field[@name='lang']" position="after">
                <separator/>
                <separator/>
                <separator/>
                <separator/>
                <separator string="Tài khoản ngân hàng"/>
                <div style="min-width: 400px; max-width: 400px">
                    <div class="d-flex align-items-end">
                        <field name="th_bank_ids" context="{'default_partner_id': address_home_id}">
                            <tree editable="bottom">
                                <field name="sequence" widget="handle"/>
                                <field name="bank_id" required="1" string="Ngân hàng"/>
                                <field name="acc_holder_name"/>
                                <field name="partner_id" invisible="1"/>
                                <field name="acc_number" string="Số tài khoản"/>
                                <field name="th_branch_name"/>
                            </tree>
                        </field>
                    </div>
                </div>
            </xpath>
            <xpath expr="//field[@name='identification_id']" position="replace">
                <field name="th_type_of_id"/>
                <field name="th_type_of_id_name" invisible="1"/>
                <field name="identification_id"/>
                <field name="th_tax_no"/>
            </xpath>

            <xpath expr="//page[@name='hr_settings']" position="attributes">
                <attribute name="groups"></attribute>
            </xpath>
            <xpath expr="//field[@name='address_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='work_location_id']" position="after">
                <field name="th_company_name"/>
            </xpath>

            <xpath expr="//field[@name='job_id']" position="after">
                <field name="th_intern_number" attrs="{'invisible': [('th_state', '=', 'official_staff')]}"/>
            </xpath>

            <xpath expr="//field[@name='job_id']" position="before">
                <field name="th_responsible_unit"/>
                <field name="th_code"/>
                <field name="th_rank_code_id" options="{'no_open': True, 'no_create': True}"/>
                <field name="th_currency_id" invisible="1"/>
                <field name="th_insurance_paid" widget="monetary" options="{'currency_field': 'th_currency_id'}"/>
                <field name="th_today" invisible="1"/>
                <label for="th_wage"/>
                <div class="o_row mw-50" name="th_wage">
                    <field name="th_wage" class="o_field_widget o_field_monetary oe_inline o_hr_narrow_field"
                           nolabel="1"/>
                    <div class="mb-3 mr-3">/ month</div>
                </div>
            </xpath>

<!--            <xpath expr="//page[@name='hr_settings']/group/group[@name='identification_group']" position="inside">-->
<!--                <field name="th_compensated"/>-->
<!--            </xpath>-->
            <xpath expr="//field[@name='visa_no']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='visa_expire']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='passport_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="th_hr_employee_view_tree" model="ir.ui.view">
        <field name="name">th.hr.employee.view.tree</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="attributes">
                <attribute name="js_class">custom_hr_employee_list</attribute>
            </xpath>
            <xpath expr="//field[@name='name']" position="after">
                <field name="company_id" position="move"/>
            </xpath>
            <xpath expr="//field[@name='company_id']" position="after">
                <field name="job_id" position="move"/>
            </xpath>
            <xpath expr="//field[@name='job_id']" position="after">
                <field name="department_id" position="move"/>
            </xpath>
            <xpath expr="//field[@name='department_id']" position="after">
                <field name="parent_id" position="move"/>
            </xpath>
            <xpath expr="//field[@name='parent_id']" position="after">
                <field name="work_phone" position="move"/>
            </xpath>
            <xpath expr="//field[@name='work_phone']" position="after">
                <field name="work_email" position="move"/>
            </xpath>
            <xpath expr="//field[@name='work_email']" position="after">
                <field name="activity_ids" position="move"/>
            </xpath>
            <xpath expr="//field[@name='activity_ids']" position="after">
                <field name="activity_date_deadline" position="move"/>
            </xpath>
        </field>
    </record>

    <record id="th_view_department_tree" model="ir.ui.view">
        <field name="name">th.hr.department.tree</field>
        <field name="model">hr.department</field>
        <field name="inherit_id" ref="hr.view_department_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='display_name']" position="before">
                <field name="company_id" groups="base.group_multi_company" position="move"/>
            </xpath>
            <xpath expr="//field[@name='manager_id']" position="after">
                <field name="total_employee" groups="base.group_multi_company" position="move"/>
            </xpath>
            <xpath expr="//field[@name='total_employee']" position="after">
                <field name="parent_id" optional="hide" position="move"/>
            </xpath>
        </field>
    </record>

    <record id="th_view_department_form" model="ir.ui.view">
        <field name="name">th.hr.department.tree</field>
        <field name="model">hr.department</field>
        <field name="inherit_id" ref="hr.view_department_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="attributes">
                <attribute name="create">0</attribute>
            </xpath>
            <xpath expr="//field[@name='name']" position="before">
                <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"
                       position="move"/>
            </xpath>
        </field>
    </record>

    <record id="th_view_hr_job_tree" model="ir.ui.view">
        <field name="name">th.hr.job.tree</field>
        <field name="model">hr.job</field>
        <field name="inherit_id" ref="hr.view_hr_job_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="before">
                <field name="company_id" groups="base.group_multi_company" position="move"/>
            </xpath>
            <xpath expr="//field[@name='company_id']" position="after">
                <field name="department_id" position="move"/>
            </xpath>
            <xpath expr="//field[@name='expected_employees']" position="after">
                <field name="no_of_hired_employee" optional="hide" position="move"/>
            </xpath>
            <xpath expr="//field[@name='no_of_employee']" position="after">
                <field name="no_of_recruitment" optional="hide" position="move"/>
            </xpath>
            <xpath expr="//field[@name='no_of_recruitment']" position="after">
                <field name="expected_employees" optional="hide" position="move"/>
            </xpath>
        </field>
    </record>

    <record id="th_view_hr_job_form" model="ir.ui.view">
        <field name="name">th.hr.job.form</field>
        <field name="model">hr.job</field>
        <field name="inherit_id" ref="hr.view_hr_job_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="attributes">
                <attribute name="create">0</attribute>
            </xpath>
        </field>
    </record>
    <record id="th_hr_department_view_kanban" model="ir.ui.view">
        <field name="name">th.hr.department.kanban</field>
        <field name="model">hr.department</field>
        <field name="inherit_id" ref="hr.hr_department_view_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@class='o_kanban_card_manage_section']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="hr.act_employee_from_department" model="ir.actions.act_window">
        <field name="context">{'create': False}</field>
    </record>

    <record id="hr.act_employee_from_department" model="ir.actions.act_window">
        <field name="domain">[('department_id', '=', active_id)]</field>
    </record>

    <record id="hr.menu_hr_department_kanban" model="ir.ui.menu">
        <field name="groups_id" eval="[(3, ref('hr.group_hr_user')),(4, ref('hr.group_hr_manager'))]"/>
        <field name="active" eval="False"/>
    </record>

    <record id="hr.menu_human_resources_configuration" model="ir.ui.menu">
        <field name="groups_id" eval="[(3, ref('hr.group_hr_user')),(4, ref('hr.group_hr_manager'))]"/>
    </record>

    <!--    <record id="hr.menu_hr_employee_payroll" model="ir.ui.menu">-->
    <!--        <field name="groups_id" eval="[(4,ref('group_hr_staff'))]"/>-->
    <!--    </record>-->

    <record id="hr.menu_config_plan" model="ir.ui.menu">
        <field name="groups_id" eval="[(6,0,[ref('base.group_no_one')])]"/>
    </record>

    <record id="hr.menu_config_plan_plan" model="ir.ui.menu">
        <field name="groups_id" eval="[(4,ref('base.group_no_one'))]"/>
    </record>

    <record model="ir.ui.menu" id="hr.menu_hr_department_tree">
        <field name="active" eval="True"/>
    </record>

    <record id="hr.hr_department_tree_action" model="ir.actions.act_window">
        <field name="view_mode">kanban,tree,form</field>
    </record>

    <record id="hr_kanban_view_employees_inherit" model="ir.ui.view">
        <field name="name">th.hr..kanban.view.employees</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.hr_kanban_view_employees"/>
        <field name="arch" type="xml">
            <xpath expr="//ul" position="replace">
                <ul>
                    <li id="last_login"/>
                    <li t-if="record.work_email.raw_value" class="o_text_overflow">
                        <field name="work_email"/>
                    </li>
                    <li t-if="record.work_phone.raw_value" class="o_force_ltr">
                        <field name="work_phone"/>
                    </li>
                </ul>
            </xpath>
        </field>
    </record>

    <record id="view_employee_filter_inherit" model="ir.ui.view">
        <field name="name">view_employee_filter_inherit</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='inactive']" position="after">
                <filter name="th_state" string="Nhân viên nghỉ việc" domain="[('th_state','=', 'employee_quit')]"/>
            </xpath>
            <xpath expr="//filter[@name='th_state']" position="after">
                <filter name="th_employee_quit_date" string="Ngày nghỉ việc" domain="[]"
                        context="{'group_by': 'th_employee_quit_date'}"/>
            </xpath>
        </field>
    </record>

    <record id="action_th_archive_custom" model="ir.actions.server">
        <field name="name">Lưu trữ</field>
        <field name="model_id" ref="model_hr_employee"/>
        <field name="binding_model_id" ref="model_hr_employee"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">model.action_archive_custom()</field>
    </record>

    <record id="action_th_unarchive_custom" model="ir.actions.server">
        <field name="name">Bỏ lưu trữ</field>
        <field name="model_id" ref="model_hr_employee"/>
        <field name="binding_model_id" ref="model_hr_employee"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">model.action_unarchive_custom()</field>
    </record>
</odoo>