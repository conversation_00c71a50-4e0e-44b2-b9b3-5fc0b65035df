import xmlrpc
from odoo import models, fields, api, exceptions, _


class ThResCountry(models.Model):
    _inherit = 'res.country'

    th_country_b2b_id = fields.Integer("ID Qốc gia B2B", copy=False)

    # @api.model
    # def create(self, values):
    #     res = super(ThResCountry, self).create(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in res:
    #             rec.th_action_sync_b2b_r_country(rec)
    #     return res
    #
    # def write(self, values):
    #     res = super(ThResCountry, self).write(values)
    #     if not self._context.get('th_test_import', False) and not self._context.get('th_test_import', False):
    #         for rec in self:
    #             rec.th_action_sync_b2b_r_country(rec)
    #     return res
    #
    # def unlink(self):
    #     for rec in self:
    #         rec.with_context(is_delete=True).th_action_sync_b2b_r_country(rec)
    #     res = super(ThResCountry, self).unlink()
    #     return res
    #
    # def th_action_sync_b2b_r_country(self, res):
    #     if not res:
    #         return
    #     server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1,
    #                                                   order='id desc')
    #     if not server_api:
    #         return False
    #     try:
    #         result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         for rec in res:
    #             vals = {'name': rec.name,
    #                     'code': rec.code,
    #                     'country_id': rec.country_id.mapped('th_country_b2b_id') if rec.th_district_id.mapped('th_country_b2b_id')!=[0] else [],
    #                     'th_r_c_state_samp_id': rec.id}
    #             if not rec.th_country_b2b_id and not self._context.get('is_delete'):
    #                 r_country = result_apis.execute_kw(db, uid_api, password, 'res.country', 'create', [vals])
    #                 rec.with_context(th_test_import=True).write({'th_country_b2b_id': r_country})
    #             else:
    #                 if not self._context.get('is_delete'):
    #                     r_country = result_apis.execute_kw(db, uid_api, password, 'res.country', 'write',
    #                                                     [[rec.th_country_b2b_id], vals])
    #                 else:
    #                     r_country = result_apis.execute_kw(db, uid_api, password, 'res.country', 'unlink',
    #                                                     [[rec.th_country_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         return