ABS APM B2B (th_apm_b2b)
========================

I. Tổng quan
-----------

1. <PERSON><PERSON> tả module
~~~~~~~~~~~~~~

Module "ABS APM B2B" (th_apm_b2b) là một phần mở rộng của module APM trong hệ thống AUM Business System (ABS), được phát triển để quản lý cơ hội kinh doanh trong môi trường B2B. Module này cung cấp các công cụ chuyên biệt để:

* Quản lý cơ hội kinh doanh B2B
* Theo dõi trạng thái cơ hội
* Bàn giao cơ hội giữa các đơn vị
* Quản lý đơn hàng B2B
* Chăm sóc khách hàng sau bán hàng trong môi trường B2B

Module được thiết kế để hỗ trợ việc quản lý và theo dõi cơ hội kinh doanh B2B, tối ưu hóa quy trình bán hàng và chăm sóc khách hàng doanh nghiệp.

2. Đối tượng sử dụng
~~~~~~~~~~~~~~~~~~

Module này được thiết kế cho các đối tượng sau:

* Đối tác (Partner) - Đơn vị sở hữu cơ hội
* Nhân viên quản lý cơ hội B2B
* Quản lý đội nhóm B2B
* Nhân viên chăm sóc sau bán hàng
* Quản trị viên hệ thống

3. Phụ thuộc (các module liên quan)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Module th_apm_b2b phụ thuộc vào các module sau:

* th_apm: Module quản lý cơ hội cơ bản
* form_readonly: Hỗ trợ form chỉ đọc

4. Chức năng chính
~~~~~~~~~~~~~~~~

1. Quản lý cơ hội B2B:
   * Tạo và quản lý cơ hội kinh doanh B2B
   * Theo dõi trạng thái và tiến trình cơ hội
   * Bàn giao cơ hội giữa các đơn vị sở hữu
   * Lưu trữ lịch sử bàn giao cơ hội

2. Quản lý đơn hàng B2B:
   * Xem và quản lý đơn hàng liên quan đến cơ hội B2B
   * Giao diện đơn hàng chuyên biệt cho môi trường B2B
   * Theo dõi trạng thái đơn hàng

3. Chăm sóc sau bán hàng:
   * Quản lý cơ hội chăm sóc sau bán hàng trong môi trường B2B
   * Theo dõi lịch sử chăm sóc khách hàng

4. Phân quyền và bảo mật:
   * Kiểm soát quyền truy cập dựa trên đơn vị sở hữu
   * Cơ chế readonly_domain để kiểm soát quyền chỉnh sửa
   * Phân quyền theo nhóm người dùng
