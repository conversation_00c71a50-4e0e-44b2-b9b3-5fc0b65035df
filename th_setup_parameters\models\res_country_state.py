import xmlrpc
from odoo import models, fields, api, exceptions, _
from odoo.exceptions import ValidationError


class ThResCountryState(models.Model):
    _inherit = 'res.country.state'

    th_r_c_state_b2b_id = fields.Integer("ID Tỉnh B2B", copy=False)

    # @api.model
    # def create(self, values):
    #     res = super(ThResCountryState, self).create(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in res:
    #             rec.th_action_sync_b2b_r_c_state(rec)
    #     return res
    #
    # def write(self, values):
    #     res = super(ThResCountryState, self).write(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in self:
    #             rec.th_action_sync_b2b_r_c_state(rec)
    #     return res
    #
    # def unlink(self):
    #     for rec in self:
    #         rec.with_context(is_delete=True).th_action_sync_b2b_r_c_state(rec)
    #     res = super(ThResCountryState, self).unlink()
    #     return res
    #
    # def th_action_sync_b2b_r_c_state(self, res):
    #     vals = {}
    #     if not res:
    #         return
    #     server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1,
    #                                                   order='id desc')
    #     try:
    #         if not server_api:
    #             raise ValidationError('Không tìm thấy server!')
    #         result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         for rec in res:
    #             vals = {'name': rec.name,
    #                     'code': rec.code,
    #                     'country_id': rec.country_id.th_country_b2b_id if rec.country_id.th_country_b2b_id else False,
    #                     'th_r_c_state_samp_id': rec.id}
    #             if not rec.th_r_c_state_b2b_id and not self._context.get('is_delete'):
    #                 r_c_state = result_apis.execute_kw(db, uid_api, password, 'res.country.state', 'create', [vals])
    #                 rec.with_context(th_test_import=True).write({'th_r_c_state_b2b_id': r_c_state})
    #             else:
    #                 if not self._context.get('is_delete'):
    #                     r_c_state = result_apis.execute_kw(db, uid_api, password, 'res.country.state', 'write',
    #                                                     [[rec.th_r_c_state_b2b_id], vals])
    #                 else:
    #                     r_c_state = result_apis.execute_kw(db, uid_api, password, 'res.country.state', 'unlink',
    #                                                     [[rec.th_r_c_state_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(res.ids),
    #             'th_input_data': str(vals),
    #             'th_function_call': str('th_action_sync_b2b_r_c_state'),
    #         })
    #         return