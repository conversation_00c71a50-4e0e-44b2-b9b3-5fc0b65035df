<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="ir_cron_process_check_module" model="ir.cron">
            <field name="name">[Res Partner] Xử <PERSON> liên h<PERSON>ắn module</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="state">code</field>
            <field name="code">model._action_process_check_module()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="nextcall" eval="(DateTime.now().strftime('%Y-%m-%d 18:00:00'))" />
        </record>
    </data>
</odoo>