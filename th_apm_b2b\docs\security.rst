<PERSON><PERSON> quyền người dùng
===================

1. C<PERSON>u trúc phân quyền
---------------------

<PERSON><PERSON><PERSON> th_apm_b2b kế thừa cấu trúc phân quyền từ module th_apm và bổ sung các quyền đặc thù cho môi trường B2B:

* <PERSON><PERSON><PERSON> t<PERSON> (group_apm_partner): Quyền xem và quản lý cơ hội thuộc đơn vị sở hữu
* Nhân viên AUM (group_apm_user): Quyền quản lý cơ hội được phân công
* <PERSON><PERSON><PERSON><PERSON> lý (group_apm_leader): Quyền quản lý toàn bộ cơ hội trong nhóm
* Quản trị viên (group_apm_administrator): Quyền quản lý toàn bộ hệ thống
* <PERSON> dõi cơ hội của đố<PERSON> tác (apm_group_partner_manager): <PERSON><PERSON><PERSON><PERSON> xem cơ hội thuộc sở hữu đối tác

2. <PERSON><PERSON> quyền theo menu
----------------------

* **Đối tác:** Truy cập menu APM-B2B, xem cơ hội và đơn hàng thuộc đơn vị sở hữu
* **Nhân viên AUM:** Truy cập menu APM-B2B, quản lý cơ hội được phân công
* **Quản lý:** Truy cập menu APM-B2B, quản lý toàn bộ cơ hội trong nhóm
* **Quản trị viên:** Truy cập toàn bộ menu APM-B2B, quản lý toàn bộ hệ thống
* **Theo dõi cơ hội của đối tác:** Truy cập menu APM-B2B, xem cơ hội thuộc sở hữu đối tác

3. Phân quyền theo hành động
--------------------------

3.1. Bàn giao cơ hội
~~~~~~~~~~~~~~~~~~

* **Đối tác:** Không có quyền bàn giao cơ hội
* **Nhân viên AUM:** Có quyền bàn giao cơ hội được phân công
* **Quản lý:** Có quyền bàn giao toàn bộ cơ hội trong nhóm
* **Quản trị viên:** Có quyền bàn giao toàn bộ cơ hội trong hệ thống

3.2. Chấp nhận bàn giao
~~~~~~~~~~~~~~~~~~~~~

* **Đối tác:** Có quyền chấp nhận cơ hội được bàn giao cho đơn vị sở hữu
* **Nhân viên AUM:** Có quyền chấp nhận cơ hội được bàn giao cho nhóm
* **Quản lý:** Có quyền chấp nhận toàn bộ cơ hội được bàn giao cho nhóm
* **Quản trị viên:** Có quyền chấp nhận toàn bộ cơ hội trong hệ thống

3.3. Xem đơn hàng B2B
~~~~~~~~~~~~~~~~~~

* **Đối tác:** Có quyền xem đơn hàng thuộc đơn vị sở hữu
* **Nhân viên AUM:** Có quyền xem đơn hàng liên quan đến cơ hội được phân công
* **Quản lý:** Có quyền xem toàn bộ đơn hàng trong nhóm
* **Quản trị viên:** Có quyền xem toàn bộ đơn hàng trong hệ thống
* **Theo dõi cơ hội của đối tác:** Có quyền xem đơn hàng thuộc sở hữu đối tác

4. Cơ chế kiểm soát quyền chỉnh sửa
---------------------------------

Module th_apm_b2b sử dụng cơ chế readonly_domain để kiểm soát quyền chỉnh sửa cơ hội:

* Cơ hội đã bàn giao (is_handed_over = True): Chỉ đơn vị nhận bàn giao mới có quyền chỉnh sửa
* Cơ hội chưa bàn giao: Đơn vị sở hữu có quyền chỉnh sửa

Cơ chế này được triển khai thông qua hàm _compute_readonly_domain trong model th.apm.
