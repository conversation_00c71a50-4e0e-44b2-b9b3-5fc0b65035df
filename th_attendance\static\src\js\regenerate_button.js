odoo.define('th_attendance.button_generate_controller', function (require) {
    "use strict";
    const ListController = require('web.ListController');
    var rpc = require('web.rpc');

    ListController.include({
        events: Object.assign({}, ListController.prototype.events, {
        'click .o_button_print_excel_report_attendance': function (ev) {
            var self = this;
            return rpc.query({
                model: 'report.th_attendance.att_unknown_emp_xlsx',
                method: 'action_print_report_excel',
                args: [[],],
            }).then((id)=>{
                return self.do_action({
                    type: 'ir.actions.act_url',
                    url: `/report/xlsx/th_attendance.att_unknown_emp_xlsx/${id}`,
                    target: 'new',
                });
            })
        }
    }),

    })
});
