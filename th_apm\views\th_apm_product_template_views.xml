<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="th_product_template_action_apm" model="ir.actions.act_window">
        <field name="name">Products</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="domain">[('categ_id.th_module_ids.name', '=', 'APM')]</field>
        <field name="context">{"search_default_filter_to_sell":1, 'default_categ_id': False, 'view_product_apm': True}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new product
            </p><p>
                You must define a product for everything you sell or purchase,
                whether it's a storable product, a consumable or a service.
            </p>
        </field>
    </record>
    <record id="th_product_template_action" model="ir.actions.server">
        <field name="name"><PERSON><PERSON>n phẩm</field>
        <field name="model_id" ref="th_apm.model_th_apm"/>
        <field name="state">code</field>
        <field name="code">
           action = model.action_th_product_template()
        </field>
    </record>


</odoo>
