from odoo import _, fields, models, api, exceptions
import json


class APMAssignLeadsToTeam(models.TransientModel):
    _name = "apm.assign.leads"
    _description = '<PERSON><PERSON> c<PERSON> hội'

    th_team_user = fields.Selection(string="Chia cơ hội", selection=[('no', 'Không'),('dividing_ring', 'Vòng chia') , ('individual', 'Cá nhân')], default="no")
    th_user_apm_lead_id = fields.Many2one(comodel_name="res.users", string="Cá nhân", domain=lambda self: [('groups_id', '=', self.env.ref('th_apm.group_apm_user').id)])
    th_team_apm_lead_id = fields.Many2one(comodel_name="th.apm.team", string="Đội nhóm", domain="[('th_is_ttvh', '!=', True)]")
    th_is_after_order = fields.Bo<PERSON>an(string="Chăm sóc sau bán", default=False)

    th_user_id = fields.Many2one(comodel_name="res.users",
                                string="Cá nhân",
                                domain=lambda self: [('groups_id', '=', self.env.ref('th_apm.group_apm_after_order').id)])

    th_team_id = fields.Many2one(comodel_name="th.apm.team",
                                string="Đội nhóm",
                                domain="[('th_is_ttvh', '=', True)]")

    # Thêm trường vòng chia
    th_dividing_ring_id = fields.Many2one(comodel_name="th.apm.dividing.ring",
                                         string="Vòng chia",
                                         domain="th_dividing_ring_domain")
    th_dividing_ring_domain = fields.Char(compute="_compute_th_dividing_ring_domain")

    th_stage_id = fields.Many2one(comodel_name="th.apm.level", string="Level")
    th_status_category_id = fields.Many2one(comodel_name='th.status.category', string="Nhóm trạng thái", domain="[('th_apm_level_category', '=?', th_stage_id)]")
    th_status_detail_id = fields.Many2one(comodel_name="th.status.detail", string="Trạng thái chi tiết", domain="[('th_apm_level_ids', '=?', th_stage_id), ('th_status_category_id', '=?', th_status_category_id)]")

    @api.depends('th_dividing_ring_id')
    def _compute_th_dividing_ring_domain(self):
        for rec in self:
            rec.th_dividing_ring_domain = json.dumps([('th_is_opportunity_dividing', '=', True)])

    @api.onchange('th_team_user')
    def _onchange_team_or_user(self):
        if self.th_team_user == 'team':
            self.th_user_id = False
            self.th_user_apm_lead_id = False
            self.th_dividing_ring_id = False
        elif self.th_team_user == 'individual':
            self.th_team_id = False
            self.th_team_apm_lead_id = False
            self.th_dividing_ring_id = False
        elif self.th_team_user == 'dividing_ring':
            self.th_team_id = False
            self.th_team_apm_lead_id = False
            self.th_user_id = False
            self.th_user_apm_lead_id = False

    @api.model
    def action_server_apm_assign_leads(self):
        context = {'default_th_is_after_order': False}
        if self._context.get('is_after_order'):
            context['default_th_is_after_order'] = True
        return {
            'type': 'ir.actions.act_window',
            'name': 'Chia cơ hội',
            'view_mode': 'form',
            'res_model': 'apm.assign.leads',
            'target': 'new',
            'context': context,
        }

    def action_share_the_chance(self):
        active_ids = self._context.get('active_ids')
        model_name = self._context.get('active_model') or self._name
        if not active_ids or model_name not in ('th.apm'):
            return

        # Kiểm tra xem có trường nào được chọn không
        if self.th_is_after_order and not self.th_user_id and not self.th_team_id and not self.th_dividing_ring_id and not self.th_stage_id and not self.th_status_category_id and not self.th_status_detail_id:
            return False
        if not self.th_is_after_order and not self.th_user_apm_lead_id and not self.th_team_apm_lead_id and not self.th_dividing_ring_id and not self.th_stage_id and not self.th_status_category_id and not self.th_status_detail_id:
            return False

        th_team_user = self._context.get('th_team_user') or self.th_team_user
        vals = self._context.get('vals') or {}
        context = {}

        # Cập nhật các trường trạng thái nếu được chọn
        if self.th_stage_id:
            vals['th_stage_id'] = self.th_stage_id.id

        if self.th_status_category_id:
            vals['th_status_category_id'] = self.th_status_category_id.id

        if self.th_status_detail_id:
            vals['th_status_detail_id'] = self.th_status_detail_id.id

        # Xử lý theo kiểu chia cơ hội
        if th_team_user == 'team':
            # Chia theo đội nhóm
            vals['th_apm_team_id'] = self.th_team_id.id or self.th_team_apm_lead_id.id

        elif th_team_user == 'individual':
            # Chia cho cá nhân
            user_id = self._context.get('user_id') or self.th_user_id or self.th_user_apm_lead_id
            if not user_id:
                return False
            # Tìm tất cả đội nhóm mà cá nhân là thành viên hoặc quản lý
            user_teams = self.env['th.apm.team'].search([
                '|',
                ('th_member_ids', 'in', user_id.id),
                ('manager_id', '=', user_id.id)
            ])
            # Tìm tất cả vòng chia mà cá nhân là thành viên
            user_dividing_rings = self.env['th.apm.dividing.ring'].search([
                ('th_user_ids', 'in', user_id.id)
            ])
            # Logic phân đội:
            if not user_teams:
                # TH1: Không thuộc đội nào -> để trống đội
                vals['th_apm_team_id'] = False
            else:
                # TH2: Thuộc nhiều đội -> lấy đội con nhỏ nhất
                # (đội có parent_path dài nhất là đội con nhỏ nhất)
                smallest_team = sorted(user_teams, key=lambda t: len(t.parent_path.split('/')), reverse=True)[0]
                vals['th_apm_team_id'] = smallest_team.id
            
            # Thêm context để đánh dấu thay đổi từ action chia cơ hội
            context = {'from_assign_leads': True}

            vals['th_user_id'] = user_id.id

        elif th_team_user == 'dividing_ring':
            # Chia theo vòng chia
            if not self.th_dividing_ring_id:
                raise exceptions.UserError(_("Vui lòng chọn vòng chia!"))

            # Lấy user_id từ vòng chia cho từng cơ hội và đổi trường vòng chia
            for rec in self.env[model_name].browse(active_ids):
                # Gọi action_assign_leads_dividing_ring() cho mỗi bản ghi để lấy người dùng tiếp theo
                user_id = self.th_dividing_ring_id.action_assign_leads_dividing_ring()
                rec_vals = vals.copy()  # Tạo bản sao của vals để tránh ảnh hưởng đến các bản ghi khác
                rec_vals['th_apm_dividing_ring_id'] = self.th_dividing_ring_id.id
                if user_id:
                    rec_vals['th_user_id'] = user_id
                # Cập nhật từng bản ghi với người dùng được chỉ định
                rec.with_context(context).write(rec_vals)
            # Không cần gọi write() ở cuối nữa vì đã cập nhật từng bản ghi
            return True
        
        # Nếu không phải vòng chia, tiếp tục xử lý như cũ
        self.env[model_name].with_context(context).browse(active_ids).write(vals)
