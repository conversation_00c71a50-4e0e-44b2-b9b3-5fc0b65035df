from odoo import models, fields, api
from pytz import HOUR, timezone, UTC
from dateutil.relativedelta import relativedelta
import datetime


class ThWageHistory(models.Model):
    _name = 'th.wage.history'
    _description = "<PERSON><PERSON><PERSON> sử lương"
    _rec_name = 'th_basic_salary_id'

    currency_id = fields.Many2one('res.currency')
    th_basic_wage = fields.Monetary('Lương cơ bản')
    th_start_date = fields.Date(string="Ngày bắt đầu")
    th_end_date = fields.Date(string='<PERSON>ày kết thúc')
    th_basic_salary_id = fields.Many2one('th.basic.salary')
    th_state = fields.Selection([('new', 'Hiện hành'), ('old', 'Cũ')], string="Trạng thái")


class ThBasicSalary(models.Model):
    _name = "th.basic.salary"
    _description = "Lương cơ bản"

    name = fields.Char(string='Tên', compute="_compute_by_th_employee_id", store=True)
    th_employee_id = fields.Many2one('hr.employee', string="<PERSON><PERSON><PERSON> Viê<PERSON>", required=True, store=True)
    th_department_id = fields.Many2one('hr.department', string='Phòng ban', compute="_compute_by_th_employee_id", readonly=True, store=True)
    th_job_id = fields.Many2one('hr.job', string='Chức vụ', compute="_compute_by_th_employee_id", store=True)
    th_wage_history_id = fields.One2many('th.wage.history', 'th_basic_salary_id', string="Dòng lịch sử lương cơ bản", readonly=True)
    active = fields.Boolean('Active', default=True)

    @api.depends('th_employee_id')
    def _compute_by_th_employee_id(self):
        for result in self:
            if result.th_employee_id:
                result.name = f'Lịch sử lương cơ bản của: {result.th_employee_id.name}'
                result.th_department_id = result.th_employee_id.department_id
                result.th_job_id = result.th_employee_id.job_id
            else:
                result.name = False
                result.th_department_id = False
                result.th_job_id = False
