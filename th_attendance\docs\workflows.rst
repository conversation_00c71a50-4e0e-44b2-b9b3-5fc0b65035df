<PERSON> tiết chức năng
------------------

A. <PERSON><PERSON><PERSON> hợp chấm công
~~~~~~~~~~~~~~~~~~~~~~~~

**1. Luồng hoạt động**
Luồng 1: Import dữ liệu công từ máy chấm công lên:
-	<PERSON>ẩn bị mẫu import với định dạng:
-	Truy cập menu Chấm côngMáy vân tayTải lên chấm công
-	Chọn file cần import  Tải lên
-	Hệ thống tự động đọc dữ liệu và tạo dữ liệu chấm công tương ứng với các nhân viên trong file theo mã “Pin”
Luồng 2: Chấm công online
-	Truy cập menu Chấm côngChấm công onlineCheckin
-	Hệ thống tự động tạo dữ liệu checkin vào thời điểm đó cho nhân viên thực hiện thao tác
-	Tương tự với check out, hệ thống sẽ tự đồng tìm bản ghi checkin của người đó và ngày đó mà chưa có checkout, để thêm dữ liệu.

**2. Các hàm liên quan**
Luồng 1:
-	action_import_attendance
-	import_attendance
-	get_exists_unknown_employee
-	th_notifications
Luồng 2:
-	attendance_manual
-	_attendance_action

B. Phân loại nhân viên châm công chưa xác định
~~~~~~~~~~~~~~~~~~~~~~~~

**1. Luồng hoạt động**
-	Khi người dùng import công thông qua chức năng “Tổng hợp công”
-	Hệ thống tự động kiểm tra nhân viên có trong file import và chưa có trên hệ thống
-	Tự động tạo dữ liệu nhân viên không xác định đó (đế tránh mất thông tin công)-các dự liệu bao gồm: Pin, ngày chấm công, thời gian vào làm, thời gian tan làm,….
-	Khi nhân viên được tạo mới hoặc sửa nhân viên cũ với trường “Mã chấm công” trong nhân viên trùng với giá trị trường “Pin” trong nhân viên chưa xác định.
-	Hệ thống tự động tạo dữ liệu chấm công cho nhân viên đó và lưu trữ các bản ghi nhân viên chấm công không xác định tương ứng.

**2. Các hàm liên quan**
-	import_attendance
-	create (hr.employee)
-	write (hr.employee)

C. Phân loại chấm công
~~~~~~~~~~~~~~~~~~~~~~~~

**1. Luồng hoạt động**
-	Với dữ liệu được tải lên từ file import, hệ thống tự động phân loại sang làm công máy (Chấm công  Tổng hợp chấm công  Công máy)
-	Với dữ liệu chấm công online, hệ thống tự động phân loại sang làm công online (Chấm công  Tổng hợp chấm công  Công online)

**2. Các hàm liên quan**
-	create (hr.attendance)

D. Điều chỉnh công
~~~~~~~~~~~~~~~~~~~~~~~~

**1. Luồng hoạt động**
-	Hệ thống cho phép tạo mới 1 phiếu điều chỉnh công với 2 loại chính là bù công và Thừa công.
-	Bù công:
•	Với Bù công người dùng cần chọn loại bù công, ngày bù, giờ bù công
-	Thừa công:
•	Với thừa công người dùng cần chọn ngày bù công
-	Trạng thái mặc định “Nháp”
-	Nhấn gửi phiếu phê duệt
-	Hệ thống gửi phiếu tới quản lý và người duyệt (HR).
-	Trạng thái phiếu đổi sang “Chờ xác nhận”

**2. Các hàm liên quan**
-	action_send_to_approver (th.attendance.request)
-	create (th.attendance.request)


E. Phê duyệt điều chỉnh công
~~~~~~~~~~~~~~~~~~~~~~~~

**1. Luồng hoạt động**
-	Luồng 1: Quản lý xác nhận
    •	Và trạng thái phiếu đổi sang “Đã duyệt lần 1”
-	Luồng 2: HR xác nhận
    2.1: Xác nhận bù công:
    •	Khi người dùng phê duyệt phiếu nhấn nút xác nhận hệ thống sẽ mở ra 1 popup hiển thị danh sách thời gian chấm công đang có theo thông tin trong phiếu.
    •	Nếu chưa có bản ghi của ngày bù công hệ thống sẽ tự động tạo bản ghi công tường ứng trong phiếu
    2.2: Xác nhận thừa công:
    •	Khi người dùng nhấn nút xác nhận hệ thống sẽ mở ra popup người duyệt sẽ chọn ngày thừa và nhấn xác nhận.
    •	Hệ thống sẽ xoá thời gian thừa công đã chọn.
    Trạng thái phiếu đổi sang “Đã xác nhân”
    Lưu ý: HR có thể xác nhận phiếu khi quản ly chưa xác nhận.
-	Luồng 3: Từ chối
    •	Chỉ cần quản lý hoặc HR nhấn từ chối, phiếu đổi trạng thái sang “Từ chối”

**2. Các hàm liên quan**
-	action_send_to_approver (th.attendance.request)
-	action_notify_send_mail (th.attendance.request)
-	action_confirm (th.attendance.request)
-	check_second_approve (th.attendance.request)
-	action_refuse_open_view (th.attendance.request)
-	action_refuse (th.attendance.request)

F. Báo cáo chấm công muộn
~~~~~~~~~~~~~~~~~~~~~~~~

**1. Luồng hoạt động**
-	Hệ thống cho phép mở popup để chọn ngày bắt đầu và ngày kết thúc.
-	Nhấn xác nhận hệ thống sẽ xuất ra báo cáo dạng excel với thời gian đã được chọn.

**2. Các hàm liên quan**
-	action_generate_excel_report
-	_check_date_range
-	generate_xlsx_report

G. Cấu hình
~~~~~~~~~~~~~~~~~~~~~~~~

**1. Luồng hoạt động**
-	Hệ thống cho phép cấu hình các loại mã chấm công (công máy và công online)
-	Hệ thống cho phép những người có thể phê duyệt phiếu điều chỉnh công

**2. Các hàm liên quan**
-	set_values
-	get_values

