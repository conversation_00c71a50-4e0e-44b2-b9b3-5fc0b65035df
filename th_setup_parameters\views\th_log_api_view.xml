<odoo>
    <record id="th_log_api_view_tree" model="ir.ui.view">
        <field name="name">th_log_api_view_tree</field>
        <field name="model">th.log.api</field>
        <field name="arch" type="xml">
            <tree string="" default_order="create_date desc" decoration-success="state in ('success')" decoration-danger="state in ('error')">
                <field name="th_function_call" optional="show"/>
                <field name="create_date" optional="show"/>
                <field name="th_time_response" optional="show"/>
                <field name="state" optional="show"/>
                <field name="th_model" optional="hide"/>
                <field name="th_record_id" optional="hide"/>
                <field name="th_input_data" optional="hide"/>
                <field name="th_description" optional="hide"/>
            </tree>
        </field>
    </record>

    <record id="th_log_api_view_form" model="ir.ui.view">
        <field name="name">th_log_api_view_form</field>
        <field name="model">th.log.api</field>
        <field name="arch" type="xml">
            <form string="">
                <sheet>
                    <group>
                        <group>
                            <field name="th_model"/>
                            <field name="th_record_id" required="1"/>
                            <field name="state"/>
                            <field name="th_function_call"/>
                        </group>
                        <group>
                            <field name="th_time_response"/>
                            <field name="create_date"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Mô tả">
                            <field name="th_description"/>
                        </page>
                        <page string="Dữ liệu đầu vào">
                            <field name="th_input_data"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="th_log_api_view_action" model="ir.actions.act_window">
        <field name="name">Log api</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.log.api</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'create': 0, 'edit': 0, 'delete': 0}</field>
    </record>
</odoo>