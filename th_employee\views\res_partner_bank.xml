<odoo>
    <record id="th_res_partner_bank_view_form" model="ir.ui.view">
        <field name="name">th.res.partner.bank.view.form</field>
        <field name="model">res.partner.bank</field>
        <field name="inherit_id" ref="base.view_partner_bank_form"/>
        <field name="arch" type="xml">

            <xpath expr="//field[@name='bank_id']" position="after">
                <field name="th_branch_name"/>
            </xpath>
            <xpath expr="//field[@name='bank_id']" position="attributes">
                <attribute name="options">{'no_create': True}</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="options">{'no_create': True}</attribute>
                <attribute name="domain">[('id', '=', context.get('default_partner_id'))] if context.get('default_partner_id') else []</attribute>
            </xpath>

        </field>
    </record>
</odoo>