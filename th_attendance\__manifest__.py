# -*- coding: utf-8 -*-
{
    'name': "ABS Attendances",
    'summary': """
        ABS Attendances""",
    'category': 'AUM Business System/ Attendance',

    'description': """
        Long description of module's purpose
    """,
    'author': "TH",
    'website': "http://www.yourcompany.com",
    'license': 'LGPL-3',
    'version': '16.0.030324',
    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/15.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'version': '0.1',

    # any module necessary for this one to work correctly
    'depends': [
        'base',
        'mail',
        'report_xlsx',
        'th_employee',
        'hr_attendance',
    ],

    'assets': {
            'web.assets_backend': [
                'th_attendance/static/src/xml/view_button_regenerate.xml',
                'th_attendance/static/src/xml/view_kiosk_mode.xml',
                'th_attendance/static/src/xml/view_button_attendance_approve.xml',
                'th_attendance/static/src/js/kiosk_confirm.js',
                'th_attendance/static/src/js/regenerate_button.js',
                'th_attendance/static/src/js/list_attendance_approve.js',
            ],
    },

    # always loaded
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'report/report.xml',
        'data/ir_cron_data.xml',
        'data/mail_template_attendance.xml',
        'views/attendance_view.xml',
        'views/th_attendance_request.xml',
        'views/att_unknown_employee.xml',
        'views/hr_employee.xml',
        'views/res_config_settings.xml',
        'wizard/import_attendance_views.xml',
        'wizard/attendance_request_approve.xml',
        'report/th_late_in_attendance_view.xml',
        'views/menu.xml',
    ],
    # only loaded in demonstration mode
    'demo': [
        'demo/demo.xml',
    ],
    'license': 'LGPL-3',
    'application': True,
}
