<odoo>
    <record id="import_student_status_wizard_view_form" model="ir.ui.view">
        <field name="name">import.student.status.wizard.form</field>
        <field name="model">import.student.status.wizard</field>
        <field name="arch" type="xml">
            <form string="Import Student Status">
                <group>
                    <field name="file" filename="file_name" string="Excel file"/>
                    <field name="file_name" invisible="1"/>

                </group>
                <footer>
                    <button string="Import" type="object" name="th_student_status" class="btn-primary"/>
                    <button string="Cancel" special="cancel" class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_import_student_status_wizard" model="ir.actions.act_window">
        <field name="name">Import trạng thái học viên </field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">import.student.status.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
<!--    <record id="th_student_status_report_xlsx" model="ir.actions.report">-->
<!--        <field name="name">mẫu import</field>-->
<!--        <field name="model">import.student.status.wizard</field>-->
<!--        <field name="report_type">xlsx</field>-->
<!--        <field name="report_name">import.student.status.wizard</field>-->
<!--        <field name="report_file">import.student.status.wizard</field>-->
<!--        <field name="binding_type">report</field>-->
<!--    </record>-->
</odoo>
