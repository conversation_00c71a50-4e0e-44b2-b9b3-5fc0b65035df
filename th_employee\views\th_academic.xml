<odoo>
    <record id="th_academic_view_tree" model="ir.ui.view">
        <field name="name">th.academic.view.tree</field>
        <field name="model">th.academic</field>
        <field name="arch" type="xml">
            <tree string="Academic Institutions" create="1" delete="1" editable="bottom">
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record id="th_academic_action" model="ir.actions.act_window">
        <field name="name">Academic Institutions</field>
        <field name="res_model">th.academic</field>
        <field name="view_mode">tree</field>
        <field name="type">ir.actions.act_window</field>
    </record>

    <menuitem id="th_academic_menu" name="Academic Institutions" parent="hr.menu_config_employee" action="th_academic_action" sequence="2"/>
</odoo>