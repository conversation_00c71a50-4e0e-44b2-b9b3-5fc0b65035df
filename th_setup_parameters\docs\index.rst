.. setup parameters documentation master file, created by
   sphinx-quickstart on Fri Apr 18 15:34:32 2025.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.


SETUP PARAMETERS MODULE
==========

Tổng quan
---------

<PERSON><PERSON> tả module
~~~~~~~~~~~~~~~

Module th_setup_parameters cho phép quản trị viên cấu hình các thông số hệ thống và thông tin nền tảng (đơn vị sở hữu, xu<PERSON><PERSON> x<PERSON>, ng<PERSON><PERSON> học, vùng - tr<PERSON><PERSON>, nh<PERSON><PERSON> ngu<PERSON>,  tr<PERSON><PERSON> thái, m<PERSON><PERSON> họ<PERSON>, ph<PERSON> h<PERSON>, nh<PERSON><PERSON> sản phẩm...). <PERSON><PERSON><PERSON> trự<PERSON> quan, d<PERSON>ao tá<PERSON>, gi<PERSON><PERSON> chu<PERSON><PERSON> hóa dữ liệu đầu và<PERSON> ph<PERSON> v<PERSON> c<PERSON> phân hệ <PERSON>, Helpdesk, <PERSON><PERSON><PERSON><PERSON>, v.v.

<PERSON><PERSON> tượng sử dụng
~~~~~~~~~~~~~~~~~~~~

<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> (Administrator)

C<PERSON> <PERSON><PERSON> thuộc (c<PERSON>c module liên quan)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. base
2. web
3. mail
4. sms
5. product
6. th_base
7. th_contact
8. web_domain_field

D. Chức năng
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. Quản lý danh sách các đơn vị sở hữu, đối tác triển khai, cấu hình liên kết APM/SRM/CRM.
2. Cấu hình các thông số hệ thống:

- Ngành học và xuất xứ
- Kênh
- Nhóm nguồn
- Nhóm trạng thái và trạng thái chi tiết
- Cấu hình form nhúng
- Vùng và trạm
- Tổ hợp môn
- Trạng thái
- Phân hệ
- Hệ tốt nghiệp
- Nhóm sản phẩm
- Ẩn log
- Log API

3. Đồng bộ hóa dữ liệu cấu hình từ hệ thống ngoài (nếu bật cờ).
4. Tự động hóa một số hành động định kỳ (Scheduled Actions)

- Đồng bộ xuất xứ
- Lưu mã khách hàng của liên hệ vào các bảng cơ hội và kế toán
- Xử lý gắn module vào liên hệ

5. Cài đặt dữ liệu mặc định (th_setup_data.xml)


.. toctree::
   :maxdepth: 2
   :caption: Chi tiết module:

   docs/workflows
   docs/api
   docs/security
