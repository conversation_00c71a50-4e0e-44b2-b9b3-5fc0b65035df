<?xml version="1.0"?>
<odoo>
    <record id="apm_assign_leads_form" model="ir.ui.view">
        <field name="name">apm_assign_leads_form</field>
        <field name="model">apm.assign.leads</field>
        <field name="arch" type="xml">
            <form string="Giao cơ hội">
                <group>
                    <group string="Đội nhóm">
                        <field name="th_is_after_order" invisible="0"/>
                        <field name="th_dividing_ring_domain" invisible="1"/>
                        <field name="th_team_user" widget="radio" options="{'horizontal': true}"/>

                        <!-- Trường cho vòng chia -->
                        <field name="th_dividing_ring_id" 
                               attrs="{
                                   'invisible': ['|','|',('th_team_user', '!=', 'dividing_ring'),('th_team_user', '=', 'no'),('th_is_after_order', '=', True)],'required': [('th_team_user', '=', 'dividing_ring'),('th_is_after_order', '=', False)]
                               }"
                               domain="[('th_is_opportunity_dividing', '=', True)]"/>
                        <field name="th_team_id"
                               attrs="{'invisible': ['|', '|', ('th_team_user', '!=', 'team'), ('th_team_user', '=', 'no'), ('th_is_after_order', '=', False)], 'required': [('th_team_user', '=', 'team'), ('th_is_after_order', '=', True)]}"/>
                        <field name="th_user_id"
                               attrs="{'invisible': ['|', '|', ('th_team_user', '!=', 'individual'), ('th_team_user', '=', 'no'), ('th_is_after_order', '=', False)], 'required': [('th_team_user', '=', 'individual'), ('th_is_after_order', '=', True)]}"/>
                        <field name="th_team_apm_lead_id"
                               attrs="{'invisible': ['|', '|', ('th_team_user', '!=', 'team'), ('th_team_user', '=', 'no'), ('th_is_after_order', '=', True)], 'required': [('th_team_user', '=', 'team'), ('th_is_after_order', '=', False)]}"/>
                        <field name="th_user_apm_lead_id"
                               attrs="{'invisible': ['|', '|', ('th_team_user', '!=', 'individual'), ('th_team_user', '=', 'no'), ('th_is_after_order', '=', True)], 'required': [('th_team_user', '=', 'individual'), ('th_is_after_order', '=', False)]}"/>
                    </group>

                    <group string="Trạng thái">
                        <field name="th_stage_id"/>
                        <field name="th_status_category_id"/>
                        <field name="th_status_detail_id"
                               attrs="{'invisible': [('th_status_category_id', '=', 'False')]}"/>
                    </group>
                </group>
                <footer>
                    <button name="action_share_the_chance" type="object" string="Xác nhận" class="btn btn-primary"/>
                    <button string="Hủy" special="cancel" class="btn btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

<!--    <record id="action_apm_assign_leads_view" model="ir.actions.act_window">-->
<!--        <field name="name">Chia cơ hội</field>-->
<!--        <field name="type">ir.actions.act_window</field>-->
<!--        <field name="res_model">apm.assign.leads</field>-->
<!--        <field name="view_mode">form</field>-->
<!--        <field name="target">new</field>-->
<!--        <field name="binding_model_id" ref="model_th_apm"/>-->
<!--    </record>-->
    <record id="action_server_apm_assign_leads_view" model="ir.actions.server">
        <field name="name">Chia cơ hội</field>
        <field name="model_id" ref="th_apm.model_apm_assign_leads"/>
        <field name="binding_model_id" ref="th_apm.model_th_apm"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">action = model.action_server_apm_assign_leads()</field>
    </record>
</odoo>
