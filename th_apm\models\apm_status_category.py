from odoo import fields, models, api, _
from odoo.exceptions import ValidationError


class APMStatusCategory(models.Model):
    _inherit = "th.status.category"

    th_apm_level_category = fields.Many2many(comodel_name="th.apm.level", relation="apm_level_th_status_category_rel", column1="apm_status_category_id", column2="apm_level_category_id", string="Mối quan hệ (APM)")
    th_categ_after_sale = fields.Boolean(string='trạng thái sau bán')

    def unlink(self):
        for rec in self:
            if rec.id == self.env.ref('th_apm.th_no_process_category').id:
                raise ValidationError(_('Đ<PERSON>y là nhóm trạng thái mặc định của hệ thống, không thể xóa nhóm trạng thái này!'))
            return super(APMStatusCategory, self).unlink()