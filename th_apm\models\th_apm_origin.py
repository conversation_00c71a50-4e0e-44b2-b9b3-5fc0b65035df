from odoo import fields, models, api

class ThA<PERSON><PERSON><PERSON><PERSON>(models.Model):
    _inherit = "th.origin"
    
    th_is_synchronized = fields.Bo<PERSON>an(string="Được đồng bộ", default=False, help="Đ<PERSON>h dấu dòng sản phẩm này đã được đồng bộ")
    th_use_duplicate_check = fields.Boolean(string="Sử dụng check trùng", default=False,help="Đánh dấu sử dụng kiểm tra trùng lặp cho dòng sản phẩm này")