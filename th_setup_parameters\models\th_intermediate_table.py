# Copyright 2022 ACSONE SA/NV
# License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl).

from odoo import api, models, fields
from odoo.exceptions import ValidationError


class ThIntermediateTable(models.Model):
    _name = 'th.intermediate.table'
    _description = "Bảng chung"

    def optimal_data(self, rec, list_keys):
        # Tậ<PERSON> hợp tất cả các khóa từ list_keys
        all_keys = [key for d in list_keys for key in d.keys()]
        if not all_keys:
            return []
        # <PERSON><PERSON><PERSON> tất cả các giá trị cần thiết từ bản ghi
        res = rec.read(all_keys)

        # <PERSON><PERSON><PERSON>t qua từng khóa để xử lý
        for key in all_keys:
            # Truy cập metadata của trường
            field = rec._fields[key]
            field_type = field.type

            # <PERSON><PERSON> lý các trường liên quan đến quan hệ
            if field_type in ['many2many', 'one2many', 'many2one']:
                ids = rec[key].ids if field_type in ['many2many', 'one2many'] else [rec[key].id]
                if ids:
                    rec_key = self.env['th.mapping.id'].search([
                        ('th_internal_id', 'in', ids),
                        ('th_model_name', '=', field.comodel_name)
                    ]).mapped('th_external_id')
                    if rec_key and field_type in ['many2many', 'one2many']:
                        res[0][key] = rec_key
                    elif len(ids) == 1 and rec_key:
                        res[0][key] = rec_key[0]
                    else:
                        res[0][key] = False



            # Xử lý các trường ngày giờ
            elif field_type in ['datetime', 'date'] and res[0][key]:
                res[0][key] = res[0][key].isoformat()
        return res

    def _find_internal_id(self, th_external_id=None, th_external_ids=None, th_model_name=None, th_system='b2b'):
        if th_external_ids:
            return self.env['th.mapping.id'].search([
                ('th_external_id', 'in', th_external_ids),
                ('th_model_name', '=', th_model_name),
                ('th_system', '=', th_system),
            ]).mapped('th_internal_id')
        if th_external_id:
            mapid = self.env['th.mapping.id'].search([
                ('th_external_id', '=', th_external_id),
                ('th_model_name', '=', th_model_name)
            ], limit=1)
            return mapid.th_internal_id if mapid else False
        return

    def _find_external_id(self, th_internal_id=None, th_internal_ids=None, th_model_name=None, th_system='b2b'):
        if th_internal_ids:
            return self.env['th.mapping.id'].search([
                ('th_internal_id', 'in', th_internal_ids),
                ('th_model_name', '=', th_model_name),
                ('th_system', '=', th_system)
            ]).mapped('th_external_id')

        if th_internal_id:
            mapid = self._find_mapping_id(th_internal_id, th_model_name, th_system)
            return mapid.th_external_id if mapid else False
        return

    def _find_mapping_id(self, th_internal_id=None, th_model_name=None, th_system=None):
        return self.env['th.mapping.id'].search([
            ('th_internal_id', '=', th_internal_id),
            ('th_model_name', '=', th_model_name),
            ('th_system', '=', th_system)
        ], limit=1)

    @api.model
    def th_func_create_or_update(self, data=None, model=None, **kwargs):
        try:
            mapping = self._find_mapping_id(data.get('th_internal_id'), self._name, data.get('th_system'))
            if mapping:
                clipboard = self.env['th.clipboard'].search([
                    ('th_mapping_id', '=', mapping.id),
                    ('th_status', '=', 'waiting'),
                    ('th_type_sync', '=', data.get('th_type_sync', 'ontime')),
                ])

                if clipboard:
                    clipboard.th_data = data.get('th_data', {})
                    if clipboard.th_method == "DELETE":
                        return
                    th_data_send = clipboard.th_data_send
                    th_data_send.update(data.get('th_data_send', {}))
                    clipboard.th_data_send = th_data_send
                else:
                    th_data_send = data.get('th_data_send', {})
                    old_data = self._context.get('old_values', {})
                    list_key = set()
                    if old_data:
                        for record_id, fields_data in old_data.items():
                            list_key.update(fields_data.keys())
                        list_key = list(list_key)
                        filtered_values = {key: th_data_send[key] for key in list_key if key in th_data_send}
                        if filtered_values:
                            clipboard = self.env['th.clipboard'].create({
                                'th_mapping_id': mapping.id,
                                'th_method': "PUT" if mapping.th_external_id else "POST",
                                'th_type_sync': data.get('th_type_sync', 'ontime'),
                                'th_data': data.get('th_data', {}),
                                'th_data_send': filtered_values,
                            })
            else:
                mapping = self.env["th.mapping.id"].create({
                    'name': data.get('name', {}),
                    'th_model_name': self._name,
                    'th_internal_id': data.get('th_internal_id'),
                    'th_system': data.get('th_system'),
                    'th_module_id': data.get('th_module_id'),
                })

                self.env['th.clipboard'].create({
                    'th_mapping_id': mapping.id,
                    'th_method': 'POST',
                    'th_type_sync': data.get('th_type_sync', 'ontime'),
                    'th_data': data.get('th_data', {}),
                    'th_data_send': data.get('th_data_send', {}),
                })
        except ValidationError as e:
            raise ValidationError(e)

    @api.model
    def th_func_delete(self, data):
        try:
            mapping = self._find_mapping_id(data.get('th_internal_id'), self._name, data.get('th_system'))
            if mapping:
                clipboard = self.env['th.clipboard'].search([
                    ('th_mapping_id', '=', mapping.id),
                    ('th_status', '=', 'waiting')
                ])

                if mapping.th_external_id:
                    if clipboard:
                        clipboard.th_data = data.get('th_data')
                        clipboard.th_method = "DELETE"
                    else:
                        clipboard = self.env['th.clipboard'].create({
                            'th_mapping_id': mapping.id,
                            'th_status': 'waiting',
                            'th_method': 'DELETE',
                            'th_type_sync': data.get('th_type_sync', 'ontime'),
                            'th_data': data.get('th_data')
                        })
                else:
                    if clipboard.exists():
                        clipboard.unlink()

        except ValidationError as e:
            raise ValidationError(e)
