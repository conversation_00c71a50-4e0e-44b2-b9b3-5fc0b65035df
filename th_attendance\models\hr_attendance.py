from odoo import models, fields, api, Command, _, exceptions
from lxml import etree
from odoo.exceptions import ValidationError, AccessError
from odoo.tools import format_datetime
from datetime import datetime, timedelta, date
from pytz import HOUR, timezone, UTC
from dateutil.relativedelta import relativedelta

def float_to_time(th_time):
    integer_part = int(th_time)
    decimal_part = round(th_time-integer_part, 2)
    hour_part, minute_part = str(integer_part).zfill(2), str(int(decimal_part * 60 + 0.5)).zfill(2)
    return "%s:%s" % (hour_part, minute_part)

class HrAttendance(models.Model):
    _name = 'hr.attendance'
    _inherit = ['hr.attendance', 'mail.thread']
    _order = 'th_attendance_date desc,check_in,check_out'

    def init(self):
        sql = """
        CREATE INDEX IF NOT EXISTS hr_attendance_employee_id_check_in_idx ON hr_attendance(employee_id, check_in);
        CREATE INDEX IF NOT EXISTS hr_attendance_employee_id_th_attendance_date_idx ON hr_attendance(th_attendance_date, employee_id);        
        """
        self._cr.execute(sql)

    def _get_default_attendance_type(self):
        return self.env['ir.config_parameter'].sudo().get_param('th_normal_att')

    check_in = fields.Datetime(string="Check In", required=False, default=False, tracking=True)
    check_out = fields.Datetime(string="Check Out", tracking=True)
    active = fields.Boolean(default=True)
    department_id = fields.Many2one('hr.department', string="Department", related="employee_id.department_id",
                                    readonly=True, store=True)
    department_name = fields.Char("Department name", related="department_id.name")
    th_attendance_date = fields.Date('Attendance date', compute='compute_attendance_date', store=True)
    th_attendance_number = fields.Char('Registration Number of the Employee')
    th_ern = fields.Char(related='employee_id.pin')
    th_real_work_numbers = fields.Float('Real worked number', readonly=True, store=True)
    th_diff_in = fields.Float('Different Check-in Time', compute='compute_th_diff_in_out')
    th_diff_out = fields.Float('Different Check-out Time', compute='compute_th_diff_in_out')
    th_att_code = fields.Char('Attendance Code', default=_get_default_attendance_type)
    th_att_type = fields.Selection([('normal', 'Công Máy'), ('online', 'Công Online')], string="Loại chấm công")
    th_checkin_ip = fields.Char('Check-in IP address', readonly=True)
    th_checkout_ip = fields.Char('Check-out IP address', readonly=True)
    state = fields.Selection(selection=[('wait', 'Waiting approve'), ('approved', 'Approved'), ('refused', 'Refused')], required=True, default="approved", tracking=True)
    th_is_changed = fields.Boolean('Changed record', default=False)
    th_is_morning_att = fields.Boolean(string="Morning Shift", default=True, compute="_compute_work_shift", store=True)
    th_over_time = fields.Boolean("Là quá thời gian cho phép đi muộn", default=False, copy=False)
    @api.depends('check_in', 'check_out')
    def _compute_work_shift(self):
        for attendance in self:
            attendance.th_is_morning_att = True
            if attendance.check_in:
                if (attendance.check_in + timedelta(hours=7)).hour >= 12:
                    attendance.th_is_morning_att = False
            else:
                if (attendance.check_out + timedelta(hours=7)).hour >= 13:
                    attendance.th_is_morning_att = False

    def _missed_checkin(self):
        employees = self.env['hr.employee'].sudo().search([])
        for emp in employees:
            emp.th_miss_att = False
            if not self.sudo().search([('employee_id', '=', emp.id), ('check_in', '>=', fields.Datetime.now().replace(hour=0,minute=0,second=0))]):
                emp.th_miss_att = True

    @api.depends('check_in', 'check_out')
    def compute_attendance_date(self):
        for rec in self:
            if rec.check_in:
                rec.th_attendance_date = rec.check_in + timedelta(hours=7)
            elif rec.check_out:
                rec.th_attendance_date = rec.check_out + timedelta(hours=7)

    @api.model_create_multi
    def create(self, vals_list):
        res = super().create(vals_list)
        # res._missed_checkin()
        if self._context.get('import_file'):
            for rec in res:
                if rec.th_attendance_number:
                    rec.write({'employee_id': self.env['hr.employee'].search([('pin', '=', rec.th_attendance_number)])})
        if self._context.get('is_kiosk_mode', False):
            for rec in res:
                rec.th_att_code = self.env['ir.config_parameter'].sudo().get_param('th_kiosk_att')
                rec.state = 'wait'
        return res

    @api.constrains('check_in', 'check_out', 'employee_id')
    def _check_validity(self):
        for rec in self:
            self.check_attendance_approved(rec.employee_id, rec.th_attendance_date)


    def compute_th_diff_in_out(self):
        for rec in self:
            rec.th_diff_in = abs((rec.check_in - rec.check_in.replace(hour=1,minute=0,second=0)).total_seconds() / 3600.0) if rec.check_in else 0
            rec.th_diff_out = abs((rec.check_out - rec.check_out.replace(hour=10,minute=0,second=0)).total_seconds() / 3600.0) if rec.check_out else 0

    @api.model
    def fields_view_get(self, view_id=None, view_type='form', toolbar=False, submenu=False):
        res = super().fields_view_get(view_id, view_type, toolbar, submenu)
        if view_type == 'tree' and res.get('view_id') == self.env['ir.model.data']._xmlid_to_res_id('th_attendance.th_hr_attendance_report_view_tree'):
            document = etree.XML(res['arch'])
            for field in ['th_diff_in', 'th_diff_out']:
                if field in res['fields'] and self._context.get(field):
                    res['fields'][field]['string'] = _(self._context.get(field))
            res['arch'] = etree.tostring(document, encoding='unicode')
        return res

    def action_approve_online_attendance(self):
        for rec in self:
            if not rec.check_in or not rec.check_out:
                raise ValidationError(_("Không thể duyệt bản ghi không có công"))

            self.check_attendance_approved(rec.employee_id, rec.th_attendance_date, True)
            th_l_a_manager_ids = self.env['ir.config_parameter'].sudo().get_param('th_l_a_manager_ids')
            if self.env.user.id in eval(th_l_a_manager_ids):
                if rec.state != 'wait':
                    return
                rec.state = 'approved'
            else:
                raise ValidationError(_("Bạn không có quyền duyệt bản ghi chấm công online. Xin cảm ơn!"))

    def action_refuse_online_attendance(self):
        for rec in self:
            th_l_a_manager_ids = self.env['ir.config_parameter'].sudo().get_param('th_l_a_manager_ids')
            if self.env.user.id in eval(th_l_a_manager_ids) or self.user_has_groups('hr_attendance.group_hr_attendance_manager'):
                if rec.state != 'wait':
                    return
                rec.state = 'refused'
            else:
                raise ValidationError(_("Bạn không có quyền từ chối bản ghi chấm công online. Xin cảm ơn!"))

    def check_attendance_approved(self, employee_id, th_attendance_date, online=None):
        dt_format = 'MMMM d, y'
        all_att_in_day = self.env['hr.attendance'].search([('employee_id', '=', employee_id.id), ('th_attendance_date', '=', th_attendance_date), ('state', '=', 'approved')])
        if online:
            all_att_in_day = self.env['hr.attendance'].search([('employee_id', '=', employee_id.id), ('th_attendance_date', '=', th_attendance_date)])

        for attendance in all_att_in_day:
            if not (attendance.check_in or attendance.check_out):
                raise exceptions.ValidationError(_('An attendance must have "Check In" time or "Check Out" time.'))
            if attendance.check_in and attendance.check_out:
                if (attendance.check_in + timedelta(hours=7)).date() != (
                        attendance.check_out + timedelta(hours=7)).date():
                    raise exceptions.ValidationError(_('Checkin and checkout must be in same day!.'))
            check_in = attendance.check_in
            check_out = attendance.check_out
            att_no_self = all_att_in_day.filtered(lambda l: l.id != attendance.id and l.check_in and l.check_out)
            if att_no_self:
                for att in att_no_self:
                    if check_in and not check_out:
                        if att.check_in <= check_in <= att.check_out:
                            raise exceptions.ValidationError(
                                _('Overlapping time slices is not allowed. Please check attendance data of %(employee)s in %(error_time)s') % {
                                    'error_time': format_datetime(self.env, check_in, dt_format=dt_format),
                                    'employee': attendance.employee_id.name,
                                })
                    elif check_out and not check_in:
                        if att.check_in <= check_out <= att.check_out:
                            raise exceptions.ValidationError(
                                _('Overlapping time slices is not allowed. Please check attendance data of %(employee)s in %(error_time)s') % {
                                    'error_time': format_datetime(self.env, check_out, dt_format=dt_format),
                                    'employee': attendance.employee_id.name,
                                })
                    elif check_in and check_out:
                        if att.check_in <= check_in <= att.check_out or att.check_in <= check_out <= att.check_out:
                            raise exceptions.ValidationError(
                                _('Overlapping time slices is not allowed. Please check attendance data of %(employee)s in %(error_time_from)s') % {
                                    'error_time_from': format_datetime(self.env, check_in, dt_format=dt_format),
                                    'employee': attendance.employee_id.name,
                                })

    # Query update loại chấm công mới từ mã chấm công cũ
    def update_th_att_type_from_th_att_code(self):
        self.env.cr.execute("""
                            UPDATE hr_attendance
                            SET th_att_type = 'normal'
                            WHERE th_att_code = 'WORK100';
                            UPDATE hr_attendance
                            SET th_att_type = 'online'
                            WHERE th_att_code = 'WFH';
                            """)
