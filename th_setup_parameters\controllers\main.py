from odoo import http
from odoo.http import Controller, route, request

class PhoneWidgetController(Controller):
    @http.route('/phone_widget/models', type='json', auth='user')
    def models(self):
        records = request.env['th.widget.phone.config'].sudo().search([])
        return records.mapped('th_model_name')

    @http.route('/phone_widget/get_settings', type='json', auth='user')
    def get_phone_widget_settings(self):
        config = request.env['res.config.settings'].sudo().get_values()
        return {
            'enable_call_widget': bool(config.get('th_enable_widget_phone', False))
        }