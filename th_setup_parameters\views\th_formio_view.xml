<odoo>
    <record id="th_formio_view_tree" model="ir.ui.view">
        <field name="name">th_formio_view_tree</field>
        <field name="model">th.formio.builder.field.aff.default</field>
        <field name="arch" type="xml">
            <tree string="">

                <field name="th_uuid"/>
                <field name="th_module"/>
                <field name="th_ownership_unit_id"/>
                <field name="th_caregiver_ids"/>
                <field name="th_status_category_id"/>
                <field name="th_flag"/>
            </tree>
        </field>
    </record>
       <record id="th_formio_view_form" model="ir.ui.view">
        <field name="name">th_formio_view_from</field>
        <field name="model">th.formio.builder.field.aff.default</field>
        <field name="arch" type="xml">
            <form string="">
                <sheet>
                    <group>
                        <group name='group_left'>
                             <field name="th_uuid"/>
                             <field name="th_module" required="1"/>
                             <field name="th_ownership_unit_id" />
                             <field name="th_caregiver_ids" widget="many2many_tags" attrs="{'invisible': [('th_module', '!=', 'prm')]}" options="{'no_create': True}"/>
                             <field name="th_status_category_id" attrs="{'invisible': [('th_module', '!=', 'prm')]}"/>
                            <field name="th_flag" invisible="1"/>
                        </group>
                        <group  name='group_right'>
                            <field name="th_origin_id"  attrs="{'invisible': ['|',('th_module','=','prm'),('th_module','=','apm')]}"/>
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>
    <record id="th_formio_view_act" model="ir.actions.act_window">
        <field name="name">Form nhúng</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.formio.builder.field.aff.default</field>
        <field name="view_mode">tree,form</field>
    </record>

</odoo>