<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="th_check_interact_view_form_apm" model="ir.ui.view">
            <field name="name">th_check_interact_view_form_apm</field>
            <field name="model">th.check.all.interact.wizard</field>
            <field name="arch" type="xml">
                <form string="">
                    <group>
                        <field name="th_apm_user_id" options="{'no_create': True, 'no_open': True}"
                               required="1"/>
                        <field name="th_check_origin_only"/>
                        <field name="th_origin_id" attrs="{'invisible': [('th_check_origin_only', '=', False)]}" options="{'no_create': True, 'no_open': True}"/>
                    </group>
                    <footer>
                        <button string="Kiểm tra" name="th_apm_action_open_lognote_list" type="object"
                                class="btn-primary"/>
                        <button string="Hủy" special="cancel" class="btn-secondary"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="th_check_interact_action_apm" model="ir.actions.act_window">
            <field name="name">Kiểm tra tương tác của nhân viên APM</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">th.check.all.interact.wizard</field>
            <field name="view_id" ref="th_check_interact_view_form_apm"/>
            <field name="view_mode">form</field>
            <field name="context">{'default_th_module': 'apm'}</field>
            <field name="target">new</field>
        </record>
    </data>
</odoo>