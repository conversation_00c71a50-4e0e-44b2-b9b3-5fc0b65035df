from odoo import fields, models, api
from odoo.exceptions import ValidationError

ORIGIN_LINE = ['th_setup_parameters.th_origin_vmc', 'th_setup_parameters.th_origin_vstep', ]

class APMTrait(models.Model):
    _name = "th.apm.trait"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = "Đặc điểm"
    _rec_name = "name"

    # def _domain_th_origin(self):
    #     rec = self.env.ref('th_setup_parameters.th_apm_module')
    #     return [('th_module_ids', 'in', rec.ids)]

    name = fields.Char(string="Đặc điểm", required=True)
    # th_product_line_id = fields.Many2one('th.apm.product.line', string="Dòng sản phẩm", required=True)
    th_origin_id = fields.Many2one('th.origin', string="Dòng sản phẩm", required=True)
    th_apm_trait_value_ids = fields.One2many('th.apm.trait.value', 'th_apm_trait_id', string="Gi<PERSON> trị đặc điểm")

    @api.constrains('name')
    def check_name_trait(self):
        record = self.env['th.apm.trait'].search([('name', '=', self.name)])
        if len(record) > 1:
            raise ValidationError('Đã có bản ghi có tên %r' % self.name)
