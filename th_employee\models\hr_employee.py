from email.policy import default
from odoo import models, fields, api, _
from pytz import HOUR, timezone, UTC
from dateutil.relativedelta import relativedelta
from datetime import datetime, timedelta, date
import datetime

from odoo.exceptions import ValidationError


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    documents_share_id = fields.Many2one(groups="base.group_user")
    address_home_id = fields.Many2one(groups="hr.group_hr_user")
    first_contract_date = fields.Date(groups="hr.group_hr_user")
    contract_warning = fields.Boolean(groups="hr.group_hr_user")
    barcode = fields.Char(groups="hr.group_hr_user")
    department_id = fields.Many2one(tracking=True)
    parent_id = fields.Many2one(tracking=True)

    th_joined_date = fields.Date('Joined date')
    th_left_date = fields.Date('Left date')
    th_activeness = fields.Selection([('working', 'Working'),
                                      ('quit', 'Quit'),
                                      ('maternity', 'Maternity leave'),
                                      ('unpaid', 'Unpaid leave')], string='Activeness')
    th_company_name = fields.Selection([('aum', 'AUM'),
                                      ('th', 'TH'),
                                      ('hcm', 'HCM')], string='Tên công ty')
    th_issued_date = fields.Date('Issued date')
    th_place_of_issued = fields.Char('Place of Issue')
    th_email = fields.Char('Email AUM')
    th_email_personal = fields.Char('Email Cá nhân')
    th_emergency_rela = fields.Char('Relationship')
    th_emergency_address = fields.Char('Address AUM')
    th_qualifications = fields.One2many(
        'th.qualifications', 'th_employee_id', 'Academic qualifications')
    th_branch = fields.Char(
        'Branch name', related='bank_account_id.th_branch_name')
    th_partner_id = fields.Many2one(
        related='user_id.partner_id', string='Partner')

    th_state = fields.Selection(string='Employee status', selection=[('collaborators', 'Collaborators'),('intern', 'Intern'), ('probationary', 'Probationary'), ('official_staff', 'Official Staff'), ('employee_quit', 'Employee Quit')], tracking=True)
    th_time_of_service = fields.Float(string="Time of service (year)")
    th_employee_quit_date = fields.Date('Ngày nghỉ việc', inverse='_inverse_th_employee_quit_date')

    th_tax_no = fields.Char('Tax No')
    th_type_of_id = fields.Selection([('01', 'Identity card'), ('02', 'Citizen identification'),
                                          ('03', 'Passport'), ('04', 'Birth certificate'),
                                          ('05', 'Others')], string='Type of ID', tracking=True)
    th_type_of_id_name = fields.Char(compute='_compute_th_type_of_id_string')
    child_information = fields.One2many("children.employee", "th_child_information", string='Child information')
    th_document = fields.Binary(string="Hồ sơ nhân viên")
    th_document_name = fields.Char()
    pin = fields.Char(tracking=True)
    th_intern_number = fields.Char('Mã nhân viên cấp tạm', groups="hr.group_hr_user", copy=False)

    children = fields.Integer(string='Số người phụ thuộc', groups="hr.group_hr_user", tracking=True, compute="_compute_children")
    th_wage = fields.Monetary(string="Lương cơ bản", tracking=True, store=True)

    th_responsible_unit = fields.Selection([('aum', 'AUM'), ('vmc', 'VMC'), ('th', 'TH')], string='Đơn vị sở hữu')
    th_code = fields.Many2one('th.salary.range.code', string='Mã ngạch bậc',
                              domain="[('th_responsible_unit', '=', th_responsible_unit), ('th_responsible_unit', '!=', False), ('th_salary_range_code_history', '=', False), ('th_date_from', '<=', th_today), '|', ('th_date_to','>=', th_today), ('th_date_to', '=', False)]")
    th_rank_code_id = fields.Many2one(comodel_name="th.rank.code", related='th_code.th_rank_code_id', string="Mã ngạch")
    th_insurance_paid = fields.Float(related='th_code.th_insurance_paid', string="Lương ngạch bậc")
    th_today = fields.Date(string=" ", compute="_compute_th_today")
    th_currency_id = fields.Many2one(comodel_name='res.currency', string='Đơn vị tiền tệ', default=lambda self: self.env.company.currency_id)
    th_employee_official_promotion_date = fields.Date(string="Ngày lên Nhân viên chính thức" , tracking=True)
    th_employee_official_probationary_date = fields.Date(string="Ngày lên Nhân viên thử việc" , tracking=True)
    # th_compensated = fields.Boolean(string="Được bù công")
    th_bank_ids = fields.One2many('res.partner.bank', 'th_employee_id', string='Ngân hàng')
    departure_date = fields.Date(string="Departure Date", inverse='_inverse_departure_date')

    def action_archive_custom(self):
        selected_records = self._context.get('active_ids')
        if self.env['hr.employee'].browse(selected_records).filtered(lambda s: s.th_state == False)  or self.env['hr.employee'].browse(selected_records).filtered(lambda s: s.th_employee_quit_date == False):
            raise ValidationError(_('Bạn đang không chọn "Ngày nghỉ việc" hoặc "Trạng thái của nhân viên" là "Nhân viên đã nghỉ việc".'))

        for rec in self.env['hr.employee'].browse(selected_records):

            rec.departure_date = rec.th_employee_quit_date
            rec.departure_reason_id = self.env.ref('hr.departure_fired').id
            new_archive_records = self.env['hr.departure.wizard'].create({'departure_reason_id':self.env.ref('hr.departure_fired').id, 'departure_date':rec.th_employee_quit_date, 'employee_id':rec.id})
            new_archive_records.action_register_departure()
            rec.active = False

    def action_unarchive_custom(self):
        selected_records = self._context.get('active_ids')
        for rec in self.env['hr.employee'].browse(selected_records):
            rec.active = True

    @api.constrains('th_joined_date', 'th_employee_official_probationary_date','th_employee_official_promotion_date')
    def th_check_start_date_of_work(self):
        for record in self:
            if record.th_employee_official_probationary_date and record.th_joined_date > record.th_employee_official_probationary_date:
                raise ValidationError("Ngày bắt đầu công việc không được lớn hơn ngày bắt đầu thử việc")
            elif record.th_employee_official_promotion_date and record.th_joined_date > record.th_employee_official_promotion_date:
                raise ValidationError("Ngày bắt đầu công việc không thể lớn hơn ngày làm việc chính thức")
            elif record.th_employee_official_promotion_date and record.th_employee_official_probationary_date \
                and record.th_employee_official_probationary_date > record.th_employee_official_promotion_date:
                raise ValidationError("Ngày bắt đầu thử việc không thể lớn hơn ngày làm việc chính thức")
            else:
                pass

    def _default_employee_quit_date(self):
        if self.th_state == 'employee_quit':
            return fields.Date.today()
        else:
            return False

    @api.onchange('th_state')
    def _onchange_th_state(self):
        if self.th_state == 'employee_quit':
            self.th_employee_quit_date = fields.Date.today()
    def _inverse_departure_date(self):
        for rec in self:
            rec.th_employee_quit_date = rec.departure_date
    def _inverse_th_employee_quit_date(self):
        for rec in self:
            rec.departure_date = rec.th_employee_quit_date

    @api.depends('name')
    def _compute_th_today(self):
        for rec in self:
            rec.th_today = fields.Date.today()

    def _compute_children(self):
        for rec in self:
            rec.children = len(rec.child_information) if rec.child_information else 0
            
    @api.depends('th_type_of_id')
    def _compute_th_type_of_id_string(self):
        for rec in self:
            rec.th_type_of_id_name = dict(rec._fields['th_type_of_id'].selection).get(rec.th_type_of_id)

    def th_TimeofService(self):
        if self._fields.get('contract_id'):
            employee_ids = self.search([('th_employee_official_promotion_date', '!=', False)])
            if employee_ids:
                for record in employee_ids:
                    td = fields.Datetime.now().astimezone(timezone(record.tz)).replace(tzinfo=None, hour=0, minute=0)
                    if record.th_joined_date:
                        th_date_start = record.th_joined_date + \
                                        relativedelta(hour=0, minute=0)
                        time_between = td - th_date_start
                        record.th_time_of_service = time_between.days // 365

    def _read(self, fields):
        if self.check_access_rights('read', raise_exception=False):
            return super(HrEmployee, self)._read(fields)

        # HACK: retrieve publicly available values from hr.employee.public and
        # copy them to the cache of self; non-public data will be missing from
        # cache, and interpreted as an access error
        self.flush_recordset(fields)
        public = self.env['hr.employee.public'].browse(self._ids)
        public.read()
        for fname in fields:
            if fname in self.env['hr.employee.public']._fields:
                values = self.env.cache.get_values(public, public._fields[fname])
                if self._fields[fname].translate:
                    values = [(value.copy() if value else None) for value in values]
                self.env.cache.update_raw(self, self._fields[fname], values)

    @api.model_create_multi
    def create(self, vals_list):
        # Add code here
        res = super(HrEmployee, self).create(vals_list)
        for rec in res:
            th_wage = rec.th_wage
            if th_wage:
                basic_salary = self.env['th.basic.salary'].create({
                    'th_employee_id': rec.id,
                })
                self.env['th.wage.history'].create({
                    'th_basic_salary_id': basic_salary.id,
                    'th_basic_wage': th_wage,
                    'th_start_date': basic_salary.create_date,
                    'th_state': 'new'
                })
            if rec.th_code:
                data = rec.th_code.copy_data()
                data[0]['th_state'] = 'new'
                self.env['th.salary.range.code.history'].create({
                    'th_employee_id': rec.id,
                    'th_department_id': rec.department_id.id,
                    'th_job_id': rec.job_id.id,
                    'th_salary_range_code_id': [(0, 0, data[0])]
                })


        return res

    def write(self, values):
        # Add code here
        res = super(HrEmployee, self).write(values)
        th_wage = values.get('th_wage', False)

        if th_wage:
            basic_salary = self.env['th.basic.salary'].search([('th_employee_id', '=', self.id)])
            if not basic_salary:
                basic_salary = self.env['th.basic.salary'].create({
                    'th_employee_id': self.id,
                })
            salary_history = self.env['th.wage.history'].search([('th_basic_salary_id', '=', basic_salary.id), ('th_end_date', '=', False)], limit=1, order='id desc')

            new_salary_history = self.env['th.wage.history'].create({
                'th_basic_salary_id': basic_salary.id,
                'th_basic_wage': th_wage,
                'th_start_date': basic_salary.create_date,
                'th_state': 'new'
            })

            if salary_history:
                salary_history.write({'th_end_date': new_salary_history.create_date, 'th_state': 'old'})
        if values.get('th_code'):
            for rec in self:
                range_history = self.env['th.salary.range.code.history'].search([('th_employee_id', '=', rec.id)], limit=1)
                data = rec.th_code.copy_data()
                data[0]['th_state'] = 'new'
                if not range_history:
                    self.env['th.salary.range.code.history'].create({
                        'th_employee_id': rec.id,
                        'th_department_id': rec.department_id.id,
                        'th_job_id': rec.job_id.id,
                        'th_salary_range_code_id': [(0, 0, data[0])]
                    })
                for history in range_history.th_salary_range_code_id:
                    history.write({'th_state': 'old'})
                range_history.write({'th_salary_range_code_id': [(0, 0, data[0])]})
        return res
