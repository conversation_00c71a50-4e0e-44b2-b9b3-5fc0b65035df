from odoo import fields, models, _
from odoo.exceptions import ValidationError


class APMStatusDetail(models.Model):
    _inherit = "th.status.detail"

    th_apm_level_ids = fields.Many2many(comodel_name="th.apm.level", string="<PERSON><PERSON><PERSON> quan hệ (APM)")
    th_processing_status_ids = fields.One2many('th.processing.status', 'th_status_detail_id', string='Trạn thái xử lý')
    def unlink(self):
        for rec in self:
            if rec.id == self.env.ref('th_apm.th_no_process_detail').id:
                raise ValidationError(_('Đ<PERSON><PERSON> là trạng thái chi tiết mặc định của hệ thống, không thể xóa trạng thái này!'))
            return super(APMStatusDetail, self).unlink()

class APMProcessingStatus(models.Model):
    _name = "th.processing.status"
    _description = 'Trạng thái xử lý'

    name = fields.Char(string="Tên", required=True)
    th_description = fields.Char(string="M<PERSON> tả")
    th_status_detail_id = fields.Many2one('th.status.detail', string='Trạng thái chi tiết')