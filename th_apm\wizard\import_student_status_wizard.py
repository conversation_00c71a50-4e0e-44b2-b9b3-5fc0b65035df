from odoo import models, fields, _
import xlrd
from odoo.exceptions import UserError
import base64
from datetime import timedelta

class importlearningresultsWizard(models.TransientModel):
    _name = "import.student.status.wizard"
    _description = "Import trạng thái học viên"

    file = fields.Binary(string='Upload File')
    file_name = fields.Char()

    def th_student_status(self):
        try:
            wb = xlrd.open_workbook(file_contents=base64.decodebytes(self.file))
        except xlrd.biffh.XLRDError:
            raise UserError('Chỉ hỗ trợ các tập tin excel.')

        sheet = wb.sheet_by_index(0)

        for row in range(1, sheet.nrows):
            row_values = sheet.row_values(row)

            if not row_values[0]:
                continue
            line_product = self.env['th.apm.student.status'].sudo().search([('id', '=', int(row_values[0]))], limit=1)
            if line_product:
                if row_values[8]:
                    th_closing_date = xlrd.xldate.xldate_as_datetime(float(row_values[8]), 0)
                elif not row_values[8] and row_values[7]:
                    th_closing_date = xlrd.xldate.xldate_as_datetime(float(row_values[7]), 0) + timedelta(days=90)
                else:
                    th_closing_date = False
                if row_values[10]:
                    th_extension_end_date = xlrd.xldate.xldate_as_datetime(float(row_values[10]), 0)
                elif not row_values[10] and row_values[9]:
                    th_extension_end_date = xlrd.xldate.xldate_as_datetime(float(row_values[9]), 0) + timedelta(days=15)
                else:
                    th_extension_end_date = False
                if row_values[5]:
                    level = self.env['th.apm.level'].sudo().search([('name', '=', row_values[5].strip())], limit=1)
                else:
                    level = False
                line_product.write({
                    'th_level': level.id if level else False,
                    'th_registration_date': xlrd.xldate.xldate_as_datetime(float(row_values[6]), 0) if row_values[6] else False,
                    'th_activation_date': xlrd.xldate.xldate_as_datetime(float(row_values[7]), 0) if row_values[7] else False,
                    'th_closing_date': th_closing_date,
                    'th_renewal_date': xlrd.xldate.xldate_as_datetime(float(row_values[9]), 0) if row_values[9] else False,
                    'th_extension_end_date': th_extension_end_date,
                })
            elif not line_product:
                continue
            else:
                continue

        return {
            'type': 'ir.actions.client',
            'tag': 'reload'
        }

