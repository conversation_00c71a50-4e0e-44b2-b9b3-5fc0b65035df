# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import models, api, fields, _
from odoo.exceptions import UserError, ValidationError
import xmlrpc.client
from psycopg2 import ProgrammingError, errorcodes
from odoo.osv import expression
from dateutil.relativedelta import relativedelta
import json
import ast
import timeit
import logging
import re

from odoo.osv.expression import get_unaccent_wrapper

_logger = logging.getLogger(__name__)


# Merge list of list based on their common element
#   Input: [['a', 'b'], ['b', 'c'], ['d', 'e']]
#   Output: [['a', 'b', 'c'], ['d', 'e']]
# https://stackoverflow.com/a/9112588
def merge_common_lists(lsts):
    sets = [set(lst) for lst in lsts if lst]
    merged = True
    while merged:
        merged = False
        results = []
        while sets:
            common, rest = sets[0], sets[1:]
            sets = []
            for x in rest:
                if x.isdisjoint(common):
                    sets.append(x)
                else:
                    merged = True
                    common |= x
            results.append(common)
        sets = results
    return sets


class DataMergeModel(models.Model):
    _inherit = 'data_merge.model'

    def th_apm_action_find_duplicates(self):
        for dm_model in self.search([]):
            res_model = self.env[dm_model.res_model_name]
            if res_model._table == 'th_apm':
                self = dm_model
                self.sudo().find_duplicates()
        return self.open_records()

    # def th_schedule_mark_duplicate_lead_crm(self):
    #     crm_merge_model = self.search([('res_model_name', '=', 'crm.lead')], limit=1)
    #     if crm_merge_model:
    #         crm_merge_model.active = False
    #         crm_merge_model.active = True
    #         crm_merge_model.action_find_duplicates()
    #         crm_dup_lead_groups = self.env['data_merge.group'].search([('model_id', '=', crm_merge_model.id)])
    #         if crm_dup_lead_groups:
    #             for rec in crm_dup_lead_groups:
    #                 dup_lead = self.env['data_merge.record'].search([('group_id', '=', rec.id)],limit=1)._original_records()
    #                 for res in rec.record_ids._original_records():
    #                     if res.th_lead_aff_id:
    #                         server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1, order='id desc')
    #                         if not server_api:
    #                             return False
    #                         try:
    #                             result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
    #                         except Exception as e:
    #                             print(e)
    #                             return False
    #                         db = server_api.th_db_api
    #                         uid_api = server_api.th_uid_api
    #                         password = server_api.th_password
    #                         merge_data = {
    #                             'th_dup_state': 'processing',
    #                             'th_dup_description': 'Cơ hội của bạn đã bị trùng với cơ hội %s'% dup_lead.name,
    #                             'th_affiliate_code': res.th_affiliate_code,
    #                         }
    #                         context = {
    #                             'module': res.env.ref('th_setup_parameters.th_crm_module').name,
    #                             'origin': res.th_origin_id.th_code,
    #                             'company_code': res.th_ownership_id.th_code,
    #                             'th_create': False,
    #                             'th_opportunity_aff_id': [res.th_lead_aff_id] if res.th_lead_aff_id else []}
    #                         try:
    #                             res_id = result_apis.execute_kw(db, uid_api, password, 'th.opportunity.ctv', 'receive_data', [[], merge_data], {'context': context})
    #                             if res_id.get('id', False):
    #                                 res.write({'th_lead_aff_id': int(res_id.get('id', False))})
    #                         except Exception as e:
    #                             print(e)
    #         else:
    #             return False

    def find_duplicates(self, batch_commits=False):
        unaccent = get_unaccent_wrapper(self.env.cr)
        self.env.flush_all()
        for dm_model in self:
            t1 = timeit.default_timer()
            ids = []
            res_model = self.env[dm_model.res_model_name]
            table = res_model._table
            if table != 'th_apm':
                return super().find_duplicates(batch_commits)
            else:
                # self._cr.execute("""DELETE FROM data_merge_record WHERE model_id = %s """, [dm_model.id])
                self._cr.execute("""DELETE FROM data_merge_group WHERE model_id = %s """, [dm_model.id])
            rows = []
            for rec in self.env['th.origin'].search([]):
                for rule in dm_model.rule_ids:
                    not_final_campaign_domain = [
                        "&",
                        ("th_stage_id.name", "<=", "L7"),
                        ("th_campaign_id", "!=", self.env.ref('th_apm.campaign_lead_auto').id)
                    ]
                    final_campaign_domain = ["&", ("th_campaign_id", "=", self.env.ref('th_apm.campaign_lead_auto').id), ("th_stage_id.name" , "<=", "L8")]
                    domain = eval(dm_model.domain) + ['|'] + not_final_campaign_domain + final_campaign_domain
                    domain = ast.literal_eval(str(domain) or '[]')
                    query = res_model._where_calc(domain)
                    field_name = res_model._inherits_join_calc(table, rule.field_id.name, query)
                    if rule.field_id.relation:
                        related_model = self.env[rule.field_id.relation]
                        lhs_alias, lhs_column = re.findall(r'"([^"]+)"', field_name)
                        rhs_alias = query.join(lhs_alias, lhs_column, related_model._table, 'id', lhs_column)
                        field_name = related_model._inherits_join_calc(rhs_alias, related_model._rec_name, query)

                    if rule.match_mode == 'accent':
                        # Since unaccent is case sensitive, we must add a lower to make field_name insensitive
                        field_name = unaccent('lower(%s)' % field_name)

                    group_by = ''
                    company_field = res_model._fields.get('company_id')
                    if company_field and not dm_model.mix_by_company:
                        group_by = ', %s' % res_model._inherits_join_calc(table, 'company_id', query)

                    tables, where_clause, where_clause_params = query.get_sql()
                    where_clause = where_clause and ('AND %s' % where_clause) or ''

                    # Get all the rows matching the rule defined
                    # (e.g. exact match of the name) having at least 2 records
                    # Each row contains the matched value and an array of matching records:
                    #   | value matched | {array of record IDs matching the field}
                    # query = """
                    #                     SELECT
                    #                         %(field)s as group_field_name,
                    #                         array_agg(
                    #                             %(model_table)s.id order by %(model_table)s.id asc
                    #                         )
                    #                     FROM %(tables)s
                    #                         WHERE length(%(field)s) > 0 %(where_clause)s
                    #                     GROUP BY group_field_name %(group_by)s
                    #                         HAVING COUNT(%(field)s) > 1""" % {
                    #     'field': field_name,
                    #     'model_table': table,
                    #     'tables': tables,
                    #     'where_clause': where_clause,
                    #     'group_by': group_by,
                    # }

                    query = """
                        SELECT
                            %(field)s as group_field_name,
                            array_agg(
                                %(model_table)s.id order by %(model_table)s.id asc
                            )
                        FROM %(tables)s
                            WHERE length(%(field)s) > 0 %(where_clause)s and ("th_apm"."th_origin_id" = %(origin_id)s) 
                        GROUP BY group_field_name %(group_by)s
                            HAVING COUNT(%(field)s) > 1""" % {
                        'field': field_name,
                        'model_table': table,
                        'tables': tables,
                        'where_clause': where_clause,
                        'group_by': group_by,
                        'origin_id': rec.id,
                    }

                    try:
                        self._cr.execute(query, where_clause_params)
                    except ProgrammingError as e:
                        if e.pgcode == errorcodes.UNDEFINED_FUNCTION:
                            raise UserError(_('Missing required PostgreSQL extension: unaccent'))
                        raise

                    rows += self._cr.fetchall()
            ids = ids + [row[1] for row in rows]

                    # Fetches the IDs of all the records who already matched (and are not merged),
                    # as well as the discarded ones.
                    # This prevents creating twice the same groups.
            self._cr.execute("""
                SELECT
                    ARRAY_AGG(res_id ORDER BY res_id ASC)
                FROM data_merge_record
                WHERE model_id = %s
                GROUP BY group_id
                """, [dm_model.id])
            done_groups_res_ids = [set(x[0]) for x in self._cr.fetchall()]

            _logger.info('Query identification done after %s' % str(timeit.default_timer() - t1))
            t1 = timeit.default_timer()
            if ast.literal_eval(self.env['ir.config_parameter'].get_param('data_merge.merge_lists', 'True')):
                merge_list = merge_common_lists
            else:
                merge_list = lambda x: x
            groups_to_create = [set(r) for r in merge_list(ids) if len(r) > 1]
            _logger.info('Merging lists done after %s' % str(timeit.default_timer() - t1))
            t1 = timeit.default_timer()
            _logger.info('Record creation started at %s', str(t1))
            groups_created = 0
            groups_to_create_count = len(groups_to_create)
            for group_to_create in groups_to_create:
                groups_created += 1

                if groups_created % 100 == 0:
                    _logger.info('Created groups %s / %s' % (groups_created, groups_to_create_count))

                # Check if the IDs of the group to create is already part of an existing group
                # e.g.
                #   The group with records A B C already exists:
                #       1/ If group_to_create equals A B, do not create a new group
                #       2/ If group_to_create equals A D, create the new group (A D is not a subset of A B C)
                if any(group_to_create <= x for x in done_groups_res_ids):
                    continue

                group = self.env['data_merge.group'].with_context(prefetch_fields=False).create(
                    {'model_id': dm_model.id})
                d = [{'group_id': group.id, 'res_id': rec} for rec in group_to_create]
                self.env['data_merge.record'].with_context(prefetch_fields=False).create(d)

                if groups_created % 1000 == 0 and batch_commits:
                    self.env.cr.commit()
                group.with_context(th_apm=True)._elect_master_record()
                if dm_model.create_threshold > 0 and group.similarity * 100 <= dm_model.create_threshold:
                    group.unlink()
                    continue
                data_merge = self.env['data_merge.record'].search([('group_id', '=', group.id)])
                self_closing_campaign = False
                no_self_closing_campaign = False
                for rec in data_merge:
                    group_records = self.env['th.apm'].search([('id', '=', rec.res_id)])
                    if group_records.th_campaign_id.id == self.env.ref('th_apm.campaign_lead_auto').id:
                        self_closing_campaign = True
                    elif group_records.th_campaign_id.id != self.env.ref('th_apm.campaign_lead_auto').id:
                        no_self_closing_campaign = True
                if not self_closing_campaign:
                    group.unlink()
                elif self_closing_campaign and not no_self_closing_campaign:
                    group.unlink()
                if dm_model.merge_mode == 'automatic':
                    if group.similarity * 100 >= dm_model.merge_threshold:
                        group.merge_records()
                        group.unlink()
                if any(self.env['th.apm'].search([('id', '=', record_id)]).th_campaign_id.id == self.env.ref('th_apm.campaign_lead_auto').id
                        for record_id in group_to_create):
                    has_master_record = False
                    has_non_master_record = []
                    data_merge = self.env['data_merge.record'].search([('group_id', '=', group.id)])
                    for rec in data_merge:
                        group_records = self.env['th.apm'].search([('id', '=', rec.res_id)])
                        if group_records.th_campaign_id.id != self.env.ref('th_apm.campaign_lead_auto').id:
                            rec.is_master = True
                            has_non_master_record.append(rec)
                        elif group_records.th_campaign_id.id == self.env.ref('th_apm.campaign_lead_auto').id:
                            rec.is_master = False
                            has_master_record = True
                    if len(has_non_master_record) == 1 and has_master_record:
                        group.merge_records()
                        group.unlink()
                    elif len(has_non_master_record) > 1 and has_master_record:
                        continue

            _logger.info('Record creation done after %s' % str(timeit.default_timer() - t1))


# for record_id in group_to_create:
#     group_records = self.env['th.apm'].search([('id', '=', record_id)])
#     data_merge = self.env['data_merge.record'].search([('group_id', '=', group.id)])
#     for record in group_records:
#         if record.exists():
#             is_master_value = True if record.th_campaign_id.name == 'Chiến dịch tự chốt' else False
#             data_merge.is_master = is_master_value
#             if not is_master_value:
#                 group.merge_records()
#                 group.unlink()