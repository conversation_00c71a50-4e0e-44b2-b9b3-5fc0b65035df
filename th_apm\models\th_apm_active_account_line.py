from odoo import api, fields, models, _, exceptions


class ThAPMActiveAccountLine(models.Model):
    _name = "th.apm.active.account.line"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = "Trạng thái kích hoạt kho<PERSON> học APM"

    th_apm_active_account_id = fields.Many2one("th.apm.active.account",
                                               string="Trạng thái kích hoạt tài khoản APM",
                                               ondelete='cascade',
                                               required=True)
    th_default_code = fields.Char(related="th_product_id.default_code", string="Mã sản phẩm")
    th_product_id = fields.Many2one("product.product",
                                    string="Sản phẩm")
    th_course_activation_status = fields.Selection(selection=[('wait_for_open', 'Chờ mở khoá học'),
                                                              ('opened', 'Đã mở kho<PERSON> học')],
                                                   string="Trạng thái kích hoạt kho<PERSON> họ<PERSON>",
                                                   default="wait_for_open",
                                                   required=True)
    th_waiting_reason = fields.Char(string="Lí do chờ")
