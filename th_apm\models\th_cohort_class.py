from odoo import fields, models, api, exceptions
import json

class ThCohortClass(models.Model):
    _name = "th.cohort.class"
    _description = "Lớ<PERSON> học theo <PERSON> tuyển sinh"
    _rec_name = "th_class_code"


    th_cohort_id = fields.Many2one("th.cohort", string="Khóa tuyển sinh", ondelete="cascade")
    th_product_ids = fields.Many2many("product.product", string="Khóa học đăng ký", required=True)
    th_class_code = fields.Char(string="Mã lớp", required=True)
    th_type = fields.Selection([
        ('co_ban', '<PERSON><PERSON> bản'),
        ('cap_toc', 'Cấp tốc')
    ], string="Lộ trình", required=True)
    th_product_domain = fields.Char(compute='_compute_product_domain')

    @api.depends('th_cohort_id')
    def _compute_product_domain(self):
        """
           Domain lọc các sản phẩm thuộc category có origin VSTEP
        """
        for record in self:
            domain = [
                ('categ_id', 'in', self.env['product.category'].search([]).filtered(
                    lambda cat: self.env.ref('th_setup_parameters.th_origin_vstep').id in cat.th_origin_ids.ids
                ).ids)
            ]
            record.th_product_domain = json.dumps(domain)

    @api.constrains('th_class_code')
    def _th_check_unique_constraints(self):
        """
        Kiểm tra mã lớp học là duy nhất trong cùng khóa tuyển sinh.
        """
        for rec in self:
            if self.search_count([
                ('th_class_code', '=', rec.th_class_code),
                ('id', '!=', rec.id)]):
                raise exceptions.ValidationError("Mã lớp học phải là duy nhất.")
