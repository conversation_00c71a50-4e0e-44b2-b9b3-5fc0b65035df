<odoo>
    <record id="th_view_attendance_tree" model="ir.ui.view">
        <field name="name">th_view_attendance_tree</field>
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.view_attendance_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="attributes">
                <attribute name="export_xlsx">0</attribute>
                <attribute name="edit">0</attribute>
                <attribute name="create">0</attribute>
            </xpath>
            <xpath expr="//field[@name='worked_hours']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>

            <xpath expr="//field[@name='worked_hours']" position="after">
                <field name="state" widget="badge" decoration-muted="state == 'refused'" decoration-warning="state == 'wait'" decoration-success="state == 'approved'"/>
                <button string="Approve" name="action_approve_online_attendance" type="object"
                        icon="fa-thumbs-up" states="wait"/>
                <button string="Refuse" name="action_refuse_online_attendance" type="object"
                        icon="fa-times" states="wait"/>
            </xpath>

        </field>
    </record>
    <record id="th_view_attendance_online_tree" model="ir.ui.view">
        <field name="name">th_view_attendance_online_tree</field>
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.view_attendance_tree"/>
        <field name="mode">primary</field>
        <field name="priority">33</field>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="attributes">
                <attribute name="export_xlsx">0</attribute>
                <attribute name="edit">0</attribute>
                <attribute name="create">0</attribute>
            </xpath>
            <xpath expr="//field[@name='state']" position="attributes">
                <attribute name="optional">show</attribute>
            </xpath>

        </field>
    </record>

    <record id="th_view_attendance_form" model="ir.ui.view">
        <field name="name">th_view_attendance_form</field>
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.hr_attendance_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="attributes">
                <attribute name="create">0</attribute>
                <attribute name="delete">0</attribute>
                <attribute name="edit">1</attribute>
            </xpath>
            <xpath expr="//field[@name='employee_id']" position="attributes">
                <attribute name="readonly">1</attribute>
            </xpath>
            <xpath expr="//field[@name='check_in']" position="attributes">
                <attribute name="groups">hr_attendance.group_hr_attendance_manager</attribute>
            </xpath>
            <xpath expr="//field[@name='check_out']" position="attributes">
                <attribute name="groups">hr_attendance.group_hr_attendance_manager</attribute>
            </xpath>
            <xpath expr="//form/sheet" position="after">
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="message_ids"/>
                </div>
            </xpath>
        </field>
    </record>

    <record id="th_view_attendance_worked_days_tree" model="ir.ui.view">
        <field name="name">hr.attendance.tree</field>
        <field name="model">hr.attendance</field>
        <field name="groups_id" eval="[(4, ref('hr_attendance.group_hr_attendance_user'))]"/>
        <field name="arch" type="xml">
            <tree string="Employee attendances" edit="0" editable="bottom" export_xlsx="0" sample="1">
                <field name="employee_id"/>
                <field name="check_in"/>
                <field name="check_out"/>
                <field name="state" widget="badge" decoration-muted="state == 'refused'" decoration-warning="state == 'wait'" decoration-success="state == 'approved'"/>
            </tree>
        </field>
    </record>


    <record id="th_hr_attendance_view_filter" model="ir.ui.view">
        <field name="name">th_hr_attendance_view_filter</field>
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.hr_attendance_view_filter"/>
        <field name="mode">primary</field>
        <field name="priority">32</field>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='check_in_filter']" position="replace">
                <filter string="Check In" name="check_in_filter" date="check_in" default_period="this_month"/>
                <filter string="Check Out" name="check_out_filter" date="check_out" default_period="this_month"/>
            </xpath>
        </field>
    </record>

    <record id="hr_attendance.menu_hr_attendance_view_attendances" model="ir.ui.menu">
        <field name="name">Normal Attendances</field>

    </record>
    <record id="hr_attendance.menu_hr_attendance_kiosk_no_user_mode" model="ir.ui.menu">
        <field name="name">External Attendance</field>

    </record>

    <record id="hr_attendance.hr_attendance_action" model="ir.actions.act_window">
        <field name="name">Normal Attendances</field>
        <field name="context">{"create": 1}</field>
        <field name="domain">['|', ('th_att_code', '=', 'WORK100'), ('th_att_type', '=', 'normal')]</field>
    </record>

    <record id="th_hr_attendance_online_view_action" model="ir.actions.act_window">
        <field name="name">External Attendances</field>
        <field name="res_model">hr.attendance</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="context">{"search_default_today":1, "create": 0}</field>
        <field name="domain">['|', ('th_att_code', '=', 'WFH'), ('th_att_type', '=', 'online')]</field>
        <field name="search_view_id" ref="hr_attendance.hr_attendance_view_filter" />
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree','view_id': ref('th_view_attendance_online_tree')}),
            ]"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No attendance records found
            </p><p>
                The attendance records of your employees will be displayed here.
            </p>
        </field>
    </record>

    <record id="th_hr_attendance_check_worked_days_action" model="ir.actions.act_window">
        <field name="name">Monthly Attendances</field>
        <field name="res_model">hr.attendance</field>
        <field name="view_mode">tree,kanban</field>
        <field name="context">{'search_default_check_in_filter':1,'search_default_check_out_filter':1, 'create':0, 'edit':0, 'no_open':1, 'delete':0}</field>
        <field name="domain">[('employee_id.user_id', '=', uid), ('state', '=', 'approved')]</field>
        <field name="search_view_id" ref="th_attendance.th_hr_attendance_view_filter" />
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree','view_id': ref('th_view_attendance_worked_days_tree')}),
            ]"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No attendance records found
            </p><p>
                The attendance records of your employees will be displayed here.
            </p>
        </field>
    </record>
</odoo>