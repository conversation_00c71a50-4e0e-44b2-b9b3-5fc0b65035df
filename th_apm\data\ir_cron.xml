<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_cron_auto_get_apm_coincident_opportunity" model="ir.cron">
        <field name="name"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> h<PERSON>i</field>
        <field name="interval_number">12</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="nextcall" eval="(DateTime.now() + timedelta(hours=7))"/>
        <field name="doall" eval="False"/>
        <field name="model_id" ref="model_data_merge_model"/>
        <field name="code">model.th_apm_action_find_duplicates()</field>
        <field name="state">code</field>
    </record>
</odoo>