import json
from odoo.exceptions import UserError
from odoo import models, fields, api,_


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    product_template_id = fields.Many2one(
        string="Product Template",
        comodel_name='product.template',
        compute='_compute_product_template_id',
        search='_search_product_template_id',
        domain=[('sale_ok', '=', True)],

    )

    @api.model_create_multi
    def create(self, vals_list):
        existing_product_ids = set()

        for vals in vals_list:
            product_id = vals.get('product_id')
            product_name = vals.get('name')
            if product_id in existing_product_ids:
                raise UserError(_("<PERSON>ản phẩm bị trùng: %s") % product_name)
            existing_product_ids.add(product_id)

            if vals.get('display_type') or self.default_get(['display_type']).get('display_type'):
                vals['product_uom_qty'] = 0.0
        lines = super().create(vals_list)
        for line in lines:
            if line.product_id and line.state == 'sale':
                msg = _("Extra line with %s", line.product_id.display_name)
                line.order_id.message_post(body=msg)
                # create an analytic account if at least an expense product
                if line.product_id.expense_policy not in [False, 'no'] and not line.order_id.analytic_account_id:
                    line.order_id._create_analytic_account()
        return lines