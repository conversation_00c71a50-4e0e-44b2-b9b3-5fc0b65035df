<odoo>
    <record id="th_basic_salary_form" model="ir.ui.view">
        <field name="name">th.basic.salary.view</field>
        <field name="model">th.basic.salary</field>
        <field name="arch" type="xml">
            <form string="<PERSON><PERSON>ch sử lương"
                  create="false"
                  edit="false"
                  delete="false"
                  duplicate="false"
                  import="false">
                <sheet>
                    <div class="oe_button_box" name="button_box"/>
                    <h1>
                        <div class="d-flex justify-content-start">
                            <div>
                                <field name="name"/>
                            </div>
                        </div>
                    </h1>
                    <h2>
                        <field name="th_employee_id"/>
                    </h2>
                    <group>
                        <group>
                            <field name="th_department_id"/>
                            <field name="th_job_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Lương cơ bản">
                            <field name="th_wage_history_id" nolabel="1">
                                <tree decoration-primary="th_state == 'new'"
                                      decoration-muted="th_state == 'old'"
                                      no_open="1"
                                      create="0" delete="0">
                                    <field name="th_state" widget="badge" decoration-info="th_state == 'new'" decoration-warning="th_state == 'old'"/>
                                    <field name="currency_id" invisible="1"/>
                                    <field name="th_start_date"/>
                                    <field name="th_end_date"/>
                                    <field name="th_basic_wage"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="th_basic_salary_action" model="ir.actions.act_window">
        <field name="name">Lịch sử lương</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.basic.salary</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'create': 0}</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                <!-- Add Text Here -->
            </p>
            <p>
                <!-- More details about what a user can do with this object will be OK -->
            </p>
        </field>
    </record>

    <!-- This Menu Item must have a parent and an action -->
    <menuitem id="th_basic_salary_menu" name="Lịch sử lương" parent="th_salary_menu_root"
              action="th_basic_salary_action" sequence="100"/>
</odoo>