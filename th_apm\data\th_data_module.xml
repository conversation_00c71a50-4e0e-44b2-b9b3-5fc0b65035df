<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('module', '=', 'th_setup_parameters'),'|', ('name', '=', 'th_origin_vmc'),'|', ('name', '=', 'th_origin_vstep'), ('name', '=', 'th_apm_module')]"/>
        </function>
        <value eval="{'noupdate': False}"/>
    </function>
    <!--        data module-->
        <record id="th_setup_parameters.th_apm_module" model="therp.module">
            <field name="active">True</field>
        </record>

<!--        data origin-->
        <record id="th_setup_parameters.th_origin_vmc" model="th.origin">
    <!--            <field name="name">VMC</field>-->
    <!--            <field name="th_code">AUM.VMC</field>-->
            <field name="active">True</field>
        </record>
        <record id="th_setup_parameters.th_origin_vstep" model="th.origin">
    <!--            <field name="name">VSTEP</field>-->
    <!--            <field name="th_code">AUM.VSTEP</field>-->
            <field name="active">True</field>
        </record>

    <function name="write" model="ir.model.data">
        <function name="search" model="ir.model.data">
            <value eval="[('module', '=', 'th_setup_parameters'),'|', ('name', '=', 'th_origin_vmc'),'|', ('name', '=', 'th_origin_vstep'), ('name', '=', 'th_apm_module')]"/>
        </function>
        <value eval="{'noupdate': True}"/>
    </function>
</odoo>