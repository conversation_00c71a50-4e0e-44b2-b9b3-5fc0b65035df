<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="th_ir_cron_update_miss_check_in" model="ir.cron">
        <field name="name">Update missed check-in</field>
        <field name="model_id" ref="model_hr_attendance"/>
        <field name="state">code</field>
        <field name="code">model.search([])._missed_checkin()</field>
        <field name="nextcall" eval="DateTime.now().strftime('%Y-%m-%d 03:30:00')" />
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False" />
        <field name="active">True</field>
    </record>

    <record id="th_ir_cron_update_last_attendance_id" model="ir.cron">
        <field name="name">Update last attendance id</field>
        <field name="model_id" ref="model_hr_employee"/>
        <field name="state">code</field>
        <field name="code">model.search([])._compute_last_attendance_id()</field>
        <field name="nextcall" eval="(DateTime.now() - relativedelta(days=1)).strftime('%Y-%m-%d 19:00:00')" />
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False" />
        <field name="active">True</field>
    </record>

    <record id="th_ir_cron_update_th_att_type_from_th_att_code" model="ir.cron">
        <field name="name">Update th_att_type from th_att_code</field>
        <field name="model_id" ref="model_hr_attendance"/>
        <field name="state">code</field>
        <field name="code">model.update_th_att_type_from_th_att_code()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="True"/>
        <field name="doall" eval="True"/>
        <field name="nextcall"
               eval="DateTime.now().replace(hour=16, minute=0, second=0).strftime('%Y-%m-%d %H:%M:%S')"/>
    </record>
</odoo>