<odoo>
    <record id="th_salary_range_code_view_tree" model="ir.ui.view">
        <field name="name">th.salary.range.code.view.tree</field>
        <field name="model">th.salary.range.code</field>
        <field name="arch" type="xml">
            <tree string="Salary range code" create="1" editable="bottom" delete="1">
                <field name="th_rank_code_id" />
                <field name="th_rank_code" />
                <field name="th_code"/>
                <field name="th_responsible_unit" required="1"/>
                <field name="th_description"/>
                <field name="th_date_from" required="1"/>
                <field name="th_date_to"/>
                <field name="th_currency_id" invisible="1"/>
                <field name="th_salary_range_code_history" invisible="1"/>
                <field name="th_state" invisible="1"/>
                <field name="th_insurance_paid" widget="monetary" options="{'currency_field': 'th_currency_id'}"  required="1"/>
            </tree>
        </field>
    </record>

    <record id="th_salary_range_code_action" model="ir.actions.act_window">
        <field name="name">Rank code</field>
        <field name="res_model">th.salary.range.code</field>
        <field name="view_mode">tree</field>
        <field name="domain">[('th_salary_range_code_history', '=', False)]</field>
        <field name="view_id" ref="th_salary_range_code_view_tree"/>
    </record>
    <!-- This Menu Item Must have a parent -->
    <menuitem id="th_salary_menu_root" name="Lương" parent="hr.menu_human_resources_configuration" sequence="25">
        <menuitem id="th_salary_range_code_menu" action="th_salary_range_code_action" name="Rank code" sequence="46"/>
    </menuitem>
</odoo>
