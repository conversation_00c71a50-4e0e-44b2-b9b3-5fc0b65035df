from odoo import models,fields, _

class SampleImportXlsx(models.TransientModel):
    _name = 'report.th_apm.sample_import_student_status'
    _description = "sample import student status"
    _inherit = 'report.report_xlsx.abstract'

    def generate_xlsx_report(self, workbook, data, partners):
        for obj in partners:
            sheet = workbook.add_worksheet('sheet1')
            self.generate_header(workbook, sheet)
            self.generate_data(workbook, sheet, obj)
            self.format_xlsx_sheet(sheet)


    def generate_header(self, workbook, sheet):
        sub_header = workbook.add_format({'bold': True, 'font_name': 'Times New Roman', 'font_size': 13, 'border': 1})
        sheet.write(0, 0, "ID", sub_header)
        sheet.write(0, 1, "Dòng sản phẩm", sub_header)
        sheet.write(0, 2, "Đơn hàng", sub_header)
        sheet.write(0, 3, "Đơn vị sở hữu", sub_header)
        sheet.write(0, 4, "Sản phẩm", sub_header)
        sheet.write(0, 5, "Level", sub_header)
        sheet.write(0, 6, "Ngày đăng ký", sub_header)
        sheet.write(0, 7, "Ngày kích hoạt", sub_header)
        sheet.write(0, 8, "Ngày đóng khóa", sub_header)
        sheet.write(0, 9, "Ngày gia hạn", sub_header)
        sheet.write(0, 10, "Ngày ngày kết thúc", sub_header)

    def generate_data(self, workbook, sheet,obj):
        row_index = 1
        record_with_largest_id = self.env['th.apm'].sudo().search([('id', '=', obj.id)], order='id desc', limit=1)
        data_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 13, 'num_format': '@', 'right': 1, 'bottom': 3})
        date_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'num_format': 'dd/mm/yyyy', 'right': 1,
             'bottom': 3})


        if record_with_largest_id:
            for rec in record_with_largest_id.th_student_status_ids:
                sheet.write(row_index, 0, rec.id or '', data_format)
                sheet.write(row_index, 1, rec.th_origin_ids.name or '', data_format)
                sheet.write(row_index, 2, rec.th_sale_order_id.name or '', data_format)
                sheet.write(row_index, 3, rec.th_ownership_unit_id or '', data_format)
                sheet.write(row_index, 4, rec.th_product_id.name or '', data_format)
                sheet.write(row_index, 5, rec.th_level.name or '', date_format)
                sheet.write(row_index, 6, rec.th_registration_date or '', date_format)
                sheet.write(row_index, 7, rec.th_activation_date or '', date_format)
                sheet.write(row_index, 8, rec.th_closing_date or '', date_format)
                sheet.write(row_index, 9, rec.th_renewal_date or '', date_format)
                sheet.write(row_index, 10, rec.th_extension_end_date or '', date_format)
                row_index = row_index + 1

    def format_xlsx_sheet(self, sheet):
        sheet.set_column(0, 0, 22)
        sheet.set_column(1, 1, 35)
        sheet.set_column(2, 2, 35)
        sheet.set_column(3, 3, 30)
        sheet.set_column(4, 4, 100)
        sheet.set_column(5, 5, 35)
        sheet.set_column(6, 6, 30)
        sheet.set_column(7, 7, 30)
        sheet.set_column(8, 8, 30)
        sheet.set_column(9, 9, 30)


    # def action_report_excel(self):
    #     res = self.sudo().create({})
    #     return res.id
