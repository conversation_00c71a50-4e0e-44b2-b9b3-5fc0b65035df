#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_hr_employee__attendence_registration_number
msgid "Attendance registration number"
msgstr "Mã nhân viên"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_hr_attendance__th_att_code
msgid "Attendance Code"
msgstr "Mã chấm công"

#. module: th_attendance
#: model:ir.actions.act_window,name:th_attendance.th_attendance_request_action
#: model:ir.model,name:th_attendance.model_th_attendance_request
#: model:ir.ui.menu,name:th_attendance.th_attendance_request_menu
#: model_terms:ir.ui.view,arch_db:th_attendance.th_attendance_request_view_form
#: model_terms:ir.ui.view,arch_db:th_attendance.th_attendance_request_view_tree
msgid "Attendance Correction Request"
msgstr "Điều chỉnh công"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_attendance_request_view_form
msgid "Request created in month"
msgstr "Yêu cầu đã tạo trong tháng"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_attendances
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action
msgid "Normal Attendances"
msgstr "Công máy"

#. module: th_attendance
#: model:ir.ui.menu,name:th_attendance.menu_hr_attendance_view_attendances_online
#: model:ir.actions.act_window,name:th_attendance.th_hr_attendance_online_view_action
msgid "External Attendance"
msgstr "Công online"

#. module: th_attendance
#: model:ir.ui.menu,name:th_attendance.th_attendance_menuitem
msgid "Attendances"
msgstr "Tổng hợp chấm công"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__name
msgid "Name"
msgstr "Tên"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__th_type
msgid "Type"
msgstr "Loại"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__th_time
msgid "Time"
msgstr "Vào lúc"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__th_status
msgid "Status"
msgstr "Trạng thái"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__th_date_req
#: model_terms:ir.ui.view,arch_db:th_attendance.th_attendance_request_view_search
msgid "Request Date"
msgstr "Ngày yêu cầu"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__th_reason
#: code:addons/th_attendance/models/th_attendance_request.py:0
msgid "Reason"
msgstr "Lý do"

#. module: th_attendance
#: model:ir.ui.menu,name:th_attendance.th_attendance_report_menu_root
msgid "Attendance Report"
msgstr "Báo cáo chấm công"

#. module: th_attendance
#: model:ir.actions.act_window,name:th_attendance.th_not_check_out_action
#: model:ir.ui.menu,name:th_attendance.th_not_check_out_menu
msgid "Not Check-out"
msgstr "Không chấm công tan làm"

#. module: th_attendance
#: model:ir.actions.act_window,name:th_attendance.th_not_check_in_action
#: model:ir.ui.menu,name:th_attendance.th_not_check_in_menu
msgid "Not Check-in"
msgstr "Không chấm công vào làm"

#. module: th_attendance
#: model:ir.actions.act_window,name:th_attendance.th_late_in_action
#: model:ir.ui.menu,name:th_attendance.th_late_in_menu
msgid "Late Check-in"
msgstr "Vào làm muộn"

#. module: th_attendance
#: model:ir.actions.act_window,name:th_attendance.th_early_departure_action
#: model:ir.model.fields,field_description:th_attendance.field_hr_attendance__th_is_early_departure
#: model:ir.ui.menu,name:th_attendance.th_early_departure_menu
msgid "Early Departure"
msgstr "Tan làm sớm"

#. module: th_attendance
#: model:ir.actions.act_window,name:th_attendance.th_early_bird_action
#: model:ir.model.fields,field_description:th_attendance.field_hr_attendance__th_is_early_bird
#: model:ir.ui.menu,name:th_attendance.th_early_bird_menu
msgid "Early Birds"
msgstr "Vào làm sớm"

#. module: th_attendance
#: model:ir.actions.act_window,name:th_attendance.th_late_bird_action
#: model:ir.ui.menu,name:th_attendance.th_late_bird_menu
msgid "Late Birds"
msgstr "Tan làm muộn"

#. module: th_attendance
#: model:ir.actions.act_window,name:th_attendance.th_missed_check_in_action
#: model:ir.ui.menu,name:th_attendance.th_missed_check_in_menu
msgid "Missed Check-in Today"
msgstr "Quên chấm công vào làm hôm nay"

#. module: th_attendance
#: code:th_attendance/models/hr_attendance.py:0
msgid "Delayed Period"
msgstr "Khoảng thời gian bị trì hoãn"

#. module: th_attendance
#: code:th_attendance/models/hr_attendance.py:0
msgid "Lost Period"
msgstr "Khoảng thời gian bị mất"

#. module: th_attendance
#: code:th_attendance/models/hr_attendance.py:0
msgid "Additional Period"
msgstr "Khoảng thời gian bổ sung"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_res_config_settings_view_form
msgid "Attendance Type"
msgstr "Loại mã chấm công"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_res_config_settings__th_normal_att
msgid "Normal attendance code"
msgstr "Mã chấm công bình thường"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_res_config_settings__th_kiosk_att
msgid "Kiosk attendance code"
msgstr "Mã chấm công Online"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_attendance_request_view_form
msgid "Send to Approver"
msgstr "Gửi tới Người phê duyệt"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_attendance_request_view_form
msgid "Confirm"
msgstr "Xác nhận"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_attendance_request_view_form
#: model_terms:ir.ui.view,arch_db:th_attendance.th_attendance_request_view_tree
#: model_terms:ir.ui.view,arch_db:th_attendance.th_view_attendance_tree
msgid "Refuse"
msgstr "Từ chối"

#. module: th_attendance
#: model:ir.model.fields.selection,name:th_attendance.selection__th_attendance_request__th_status__draft
msgid "Draft"
msgstr "Nháp"

#. module: th_attendance
#: model:ir.model.fields.selection,name:th_attendance.selection__th_attendance_request__th_status__waiting
msgid "Waiting"
msgstr "Chờ xác nhận"

#. module: th_attendance
#: model:ir.model.fields.selection,name:th_attendance.selection__th_attendance_request__th_status__confirm
msgid "Confirmed"
msgstr "Đã xác nhận"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_attendance_request_view_tree
msgid "Confirm"
msgstr "Xác nhận"

#. module: th_attendance
#: model:ir.model.fields.selection,name:th_attendance.selection__th_attendance_request__th_status__refuse
#: model:ir.model.fields.selection,name:th_attendance.selection__hr_attendance__state__refused
msgid "Refused"
msgstr "Bị từ chối"

#. module: th_attendance
#: model:ir.model.fields.selection,name:th_attendance.selection__th_attendance_request__th_type__morning_in
msgid "Morning Check-in"
msgstr "Vào làm buổi sáng"

#. module: th_attendance
#: model:ir.model.fields.selection,name:th_attendance.selection__th_attendance_request__th_type__morning_out
msgid "Morning Check-out"
msgstr "Tan làm buổi sáng"

#. module: th_attendance
#: model:ir.model.fields.selection,name:th_attendance.selection__th_attendance_request__th_type__afternoon_in
msgid "Afternoon Check-in"
msgstr "Vào làm buổi chiều"

#. module: th_attendance
#: model:ir.model.fields.selection,name:th_attendance.selection__th_attendance_request__th_type__afternoon_out
msgid "Afternoon Check-out"
msgstr "Tan làm buổi chiều"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_attendance_request_view_form_refuse
msgid "Save"
msgstr "Lưu"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_attendance_request_view_form_refuse
#: model_terms:ir.ui.view,arch_db:th_attendance.th_import_attendance_view_form
msgid "Cancel"
msgstr "Huỷ"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__th_employee_id
#: model:ir.model.fields,field_description:th_attendance.field_report_th_attendance_attendance_period_report__employee_id
#: model_terms:ir.ui.view,arch_db:th_attendance.th_att_unknown_employee_view_search
msgid "Employee"
msgstr "Nhân viên"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__th_employee_id
#: model:ir.model.fields,field_description:th_attendance.field_report_th_attendance_attendance_period_report__employee_id
#: model_terms:ir.ui.view,arch_db:th_attendance.th_att_unknown_employee_view_search
msgid "Pin"
msgstr "Mã chấm công"

#. module: th_attendance
#: model:ir.ui.menu,name:th_attendance.th_att_unknown_employee_menuitem
#: model:ir.actions.act_window,name:th_attendance.th_att_unknown_employee_action
msgid "Attendance Unknown Employee"
msgstr "Nhân viên chấm công không xác định"

#. module: th_attendance
#: model:ir.ui.menu,name:th_attendance.th_import_attendance_menuitem
#: model:ir.actions.act_window,name:th_attendance.th_import_attendance_action
msgid "Import Attendance"
msgstr "Tải lên chấm công"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_import_attendance__file
msgid "Upload File"
msgstr "Cập nhật dữ liệu"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_import_attendance_view_form
msgid "Import"
msgstr "Tải lên"

#. module: th_attendance
#: model:ir.ui.menu,name:th_attendance.th_fingerprint_machine_menuitem
msgid "Fingerprint Machine"
msgstr "Máy vân tay"

#. module: th_attendance
#: code:addons/th_attendance/report/unknown_employee.py:0
#: model:ir.model.fields,field_description:th_attendance.field_th_att_unknown_employee__th_emp_name
msgid "Employee name"
msgstr "Tên nhân viên"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_hr_attendance__th_attendance_date
#: model:ir.model.fields,field_description:th_attendance.field_th_att_unknown_employee__th_attendance_date
msgid "Attendance date"
msgstr "Ngày chấm công"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_att_unknown_employee__th_checkin_time
msgid "Check-in Time"
msgstr "Thời gian vào làm"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_att_unknown_employee__th_checkout_time
msgid "Check-out Time"
msgstr "Thời gian tan làm"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_kiosk_no_user_mode
msgid "External Attendance"
msgstr "Chấm công Online"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "\"Check Out\" time cannot be earlier than \"Check In\" time."
msgstr "Giờ \"tan làm\" không thể trước giờ \"vào làm\"."

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid ""
"Add a few employees to be able to select an employee here and perform his check in / check out.\n"
"                To create employees go to the Employees menu."
msgstr ""
"Thêm một vài nhân viên để có thể lựa chọn nhân viên tại đây và thực hiện chấm công vào làm / tan làm.\n"
"                Để tạo nhân viên, hãy đi tới menu Nhân viên."

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee hasn't "
"checked out since %(datetime)s"
msgstr ""
"Không thể tạo hồ sơ chấm công mới cho %(empl_name)s, nhân viên chưa "
"chấm công tan làm từ %(datetime)s"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid ""
"Cannot perform check out on %(empl_name)s, could not find corresponding "
"check in. Your attendances have probably been modified manually by human "
"resources."
msgstr ""
"Không thể thực hiện chấm công tan làm cho nhân viên %(empl_name)s, do không tìm thấy"
" lần chấm công vào làm tương ứng. Dữ liệu Chấm công của bạn có thể đã được điều "
"chỉnh bởi bộ phận nhân sự."

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_my_attendances
msgid "Check In / Check Out"
msgstr "Chấm công online"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_out
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Check Out"
msgstr "Tan làm"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Check-In/Out"
msgstr "Chấm công vào làm/tan làm"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_out
msgid "Checked out"
msgstr "Chấm công tan làm"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Checked out at"
msgstr "Chấm công tan làm vào"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "No Check Out"
msgstr "Không chấm công tan làm"

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_use_pin
msgid ""
"The user will have to enter his PIN to check in and out manually at the "
"company screen."
msgstr ""
"Người dùng sẽ phải nhập mã PIN của mình để chấm công vào làm và chấm công tan làm thủ công ở"
" màn hình công ty."

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Want to check out?"
msgstr "Bạn có muốn chấm công tan làm?"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "check out"
msgstr "tan làm"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "check in"
msgstr "vào làm"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Use PIN codes to check in in Kiosk Mode"
msgstr "Sử dụng mã PIN để chấm công vào làm trong chấm công online"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Checked in at"
msgstr "Chấm công vào làm vào"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_in
msgid "Checked in"
msgstr "Vào làm"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_in
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Check In"
msgstr "Vào làm"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#: code:addons/hr_attendance/models/hr_attendance.py:0
#: code:addons/hr_attendance/models/hr_attendance.py:0
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee was "
"already checked in on %(datetime)s"
msgstr ""
"Không thể tạo hồ sơ chấm công mới cho %(empl_name)s, nhân viên đã chấm công vào làm "
"vào %(datetime)s"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid ""
"<b>Warning! Last check in was over 12 hours ago.</b><br/>If this isn't "
"right, please contact Human Resource staff"
msgstr ""
"<b>Cảnh báo! Lần chấm công vào làm cuối cùng đã hơn 12 giờ trước.</b><br/>Nếu điều "
"này không đúng, xin vui lòng liên hệ với nhân viên phòng nhân sự"

#. module: th_attendance
#: code:addons/th_attendance/models/th_attendance_request.py:0
#, python-format
msgid ""
"You already have an attendance request record of the same type on %s, you can not create another one!"
msgstr ""
"Bạn đã có một bản ghi yêu cầu chấm công bù cùng loại vào ngày %s, bạn không thể tạo một cái cùng loại nữa!"

#. module: th_attendance
#: code:addons/th_attendance/models/th_attendance_request.py:0
#, python-format
msgid "%s's Attendance Request on %s"
msgstr "%s Yêu cầu chấm công vào %s"

#. module: th_attendance
#: code:addons/th_attendance/models/th_attendance_request.py:0
#: code:addons/th_attendance/models/th_attendance_request.py:0
#: code:addons/th_attendance/models/th_attendance_request.py:0
#, python-format
msgid "AM"
msgstr "sáng"

#. module: th_attendance
#: code:addons/th_attendance/models/th_attendance_request.py:0
#: code:addons/th_attendance/models/th_attendance_request.py:0
#: code:addons/th_attendance/models/th_attendance_request.py:0
#, python-format
msgid "PM"
msgstr "chiều"

#. module: th_attendance
#: code:addons/th_attendance/models/hr_attendance.py:0
#, python-format
msgid "An attendance must have \"Check In\" time or \"Check Out\" time."
msgstr "Một hồ sơ chấm công phải có thời gian vào làm hoặc tan làm."

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_hr_attendance__th_is_late_in
msgid "Late Attendance"
msgstr "Chấm công muộn"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_hr_attendance__th_is_late_bird
msgid "Late Bird"
msgstr "Tan làm muộn"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_hr_attendance__th_is_not_checkin
msgid "Not check-in"
msgstr "Không chấm công vào làm"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_hr_attendance__th_is_not_checkout
msgid "Not check-out"
msgstr "Không chấm công tan làm"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_hr_attendance__th_attendance_number
msgid "Registration Number of the Employee"
msgstr "Mã nhân viên"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_att_unknown_employee__create_uid
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__create_uid
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__create_uid
#: model:ir.model.fields,field_description:th_attendance.field_th_import_attendance__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_att_unknown_employee__create_date
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__create_date
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__create_date
#: model:ir.model.fields,field_description:th_attendance.field_th_import_attendance__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_att_unknown_employee__write_uid
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__write_uid
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__write_uid
#: model:ir.model.fields,field_description:th_attendance.field_th_import_attendance__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_att_unknown_employee__write_date
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__write_date
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__write_date
#: model:ir.model.fields,field_description:th_attendance.field_th_import_attendance__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: th_attendance
#: code:addons/th_attendance/models/th_attendance_request.py:0
#: model:ir.model.constraint,message:th_attendance.constraint_th_attendance_request_check_th_time
#, python-format
msgid "Invalid time value!"
msgstr "Giá trị thời gian không phù hợp!"

#. module: th_attendance
#: code:addons/th_attendance/models/th_att_unknown_employee.py:0
#, python-format
msgid ""
"There was an error while re-generate attendance, please contact the "
"administrator!"
msgstr ""
"Đã xảy ra lỗi trong khi tạo lại chấm công, vui lòng liên hệ "
"quản trị viên để được xử lý!"

#. module: th_attendance
#: model:ir.model,name:th_attendance.model_th_att_unknown_employee
msgid "th.att.unknown.employee"
msgstr "Nhân viên không xác định"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.template_attendance_request_th
msgid "Dear"
msgstr "Chào"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.template_attendance_request_th
msgid "View Request"
msgstr "Xem yêu cầu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Your"
msgstr "Của bạn"

#. module: th_attendance
#: code:addons/th_attendance/models/th_check_attendence.py:0
#: code:addons/th_attendance/models/th_check_attendence.py:0
#, python-format
msgid " but not checked-out yet."
msgstr " nhưng vẫn chưa chấm công tan làm"

#. module: th_attendance
#: code:addons/th_attendance/models/th_check_attendence.py:0
#: code:addons/th_attendance/models/th_check_attendence.py:0
#, python-format
msgid " checked in "
msgstr " đã chấm công vào làm lúc"

#. module: th_attendance
#: code:addons/th_attendance/models/th_check_attendence.py:0
#, python-format
msgid "Missing Check-in for "
msgstr "Thiếu chấm công vào làm ngày "

#. module: th_attendance
#: code:addons/th_attendance/models/th_check_attendence.py:0
#: code:addons/th_attendance/models/th_check_attendence.py:0
#, python-format
msgid "Missing Check-out for "
msgstr "Thiếu chấm công tan làm ngày"

#. module: th_attendance
#: code:addons/th_attendance/models/th_check_attendence.py:0
#: code:addons/th_attendance/models/th_check_attendence.py:0
#, python-format
msgid "Please check out this morning: "
msgstr "Xin hãy chấm công tan làm sáng nay: "

#. module: th_attendance
#: code:addons/th_attendance/models/th_check_attendence.py:0
#: code:addons/th_attendance/models/th_check_attendence.py:0
#, python-format
msgid "These employees still have not checked out this morning ("
msgstr "Những nhân viên này vẫn chưa chấm công tan làm sáng nay ("

#. module: th_attendance
#: code:addons/th_attendance/models/th_check_attendence.py:0
#: code:addons/th_attendance/models/th_check_attendence.py:0
#: code:addons/th_attendance/models/th_check_attendence.py:0
#, python-format
msgid ""
"Please check if employees have come to the office or not, or if there is "
"any\n"
msgstr ""
"Xin hãy kiểm tra nhân viên đã đến công ty hay chưa, hoặc có "
"\n"

#. module: th_attendance
#: code:addons/th_attendance/models/th_check_attendence.py:0
#: code:addons/th_attendance/models/th_check_attendence.py:0
#: code:addons/th_attendance/models/th_check_attendence.py:0
#, python-format
msgid "leave application that you have not approved.)"
msgstr "phiếu xin nghỉ mà bạn vẫn chưa duyệt."

#. module: th_attendance
#: code:addons/th_attendance/models/th_check_attendence.py:0
#, python-format
msgid "Please check in this afternoon: "
msgstr "Xin hãy chấm công vào làm chiều nay: "

#. module: th_attendance
#: code:addons/th_attendance/models/th_check_attendence.py:0
#, python-format
msgid "These employees still have not checked in this afternoon ("
msgstr "Những nhân viên này vẫn chưa chấm công vào làm chiều nay ("

#. module: th_attendance
#: model:mail.template,body_html:th_attendance.email_template_attendance_1
msgid ""
"<style type=\"text/css\">\n"
"                *{\n"
"                    font-family:Arial, Helvetica, sans-serif ;\n"
"                    }\n"
"            </style>\n"
"\n"
"            <div>\n"
"                <table border=\"0\" style=\"padding: 16px 0; background-color: rgb(241, 241, 241); font-family: Verdana, Arial, sans-serif; color: rgb(69, 71, 72); width: 100%; border-collapse: separate; border-spacing: 0px;\">\n"
"                    <tbody>\n"
"                        <tr>\n"
"                            <td align=\"center\">\n"
"                                <table border=\"0\" width=\"760\" style=\"padding: 24px; background-color: white; color: rgb(69, 71, 72); border-collapse: separate; border-spacing: 0px;\">\n"
"                                    <tbody>\n"
"                                        <tr>\n"
"                                            <td align=\"center\" style=\"min-width: 760px;\">\n"
"                                                <table border=\"0\" width=\"100%\" style=\"background-color: white; padding: 0px; border-collapse: separate; border-spacing: 0px;\">\n"
"                                                    <tbody>\n"
"                                                        <tr>\n"
"                                                            <td valign=\"middle\">\n"
"                                                                <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                                                                    <t t-out=\"ctx.get('notice')\"/>\n"
"                                                                </span>\n"
"                                                            </td>\n"
"                                                            <!-- <td valign=\"middle\" align=\"right\">\n"
"                                                                <img style=\"padding: 0px; margin: 0px; height: 48px;\" alt=\"Sambala\" src=\"https://sambala.net/logo.png?company=1\"/>\n"
"                                                            </td> -->\n"
"                                                        </tr>\n"
"                                                        <tr>\n"
"                                                            <td colspan=\"2\" style=\"text-align:center;\">\n"
"                                                                <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:4px 0px 32px 0px;\"/>\n"
"                                                            </td>\n"
"                                                        </tr>\n"
"                                                    </tbody>\n"
"                                                </table>\n"
"                                            </td>\n"
"                                        </tr>\n"
"                                        <tr>\n"
"                                            <td style=\"min-width: 760px;\">\n"
"                                                <span style=\"margin:0px\">\n"
"                                                    <p style=\"padding: 0 0 0 12px; margin:0;\">\n"
"                                                        Dear\n"
"                                                        <span style=\"font-weight: bold;\">\n"
"                                                            <t t-out=\"ctx.get('hrm')\"/>\n"
"                                                        </span>\n"
"                                                    </p>\n"
"                                                    \n"
"                                                    <p style=\"margin:2px 0; padding: 0 0 0 12px\">\n"
"                                                        Please note that the\n"
"                                                        <span style=\"font-weight: bold;\">\n"
"                                                            <t t-out=\"ctx.get('check_day')\"/>\n"
"                                                        </span>\n"
"                                                        information of following members are not in the system as of\n"
"                                                        <span style=\"font-weight: bold;\">\n"
"                                                            <t t-out=\"ctx.get('date')\"/>.\n"
"                                                        </span>\n"
"                                                    </p>\n"
"                                                    \n"
"                                                    <br/>\n"
"                                                        <table>\n"
"                                                            <t t-foreach=\"ctx.get('mail_name')\" t-as=\"record\">\n"
"                                                                <tr>\n"
"                                                                    <p style=\"padding: 0 0 0 24px; margin:2px 0\">\n"
"                                                                        <t t-out=\"record\"/>\n"
"                                                                    </p>\n"
"                                                                </tr>\n"
"                                                            </t>\n"
"                                                        </table>\n"
"\n"
"                                                    <p style=\"margin:24px 0 0 0 !important; padding: 0 0 0 12px\">\n"
"                                                        Please do the needful to regularize this status.\n"
"                                                    </p>\n"
"                                                    <p style=\"padding: 0 0 0 12px; margin:5px 0 0 0;\">\n"
"                                                        Thanks,\n"
"                                                    </p>\n"
"                                                    <p style=\"padding: 0 0 0 12px;\">\n"
"                                                        HUMAN RESOURCE DEPARTMENT\n"
"                                                    </p>\n"
"                                                    <p style=\"padding: 0 0 0 12px; margin:0\">AUM</p>\n"
"                                                </span>\n"
"                                            </td>\n"
"                                        </tr>\n"
"                                    </tbody>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            </div>\n"
"            <!-- Dear Bùi Ngọc Sơn,\n"
"                <br/>\n"
"                Please note that the <b><t t-out=\"ctx.get('check_day')\"/></b> information of following members are not in the system as of <b><t t-out=\"ctx.get('date')\"/></b>.\n"
"                <br/>\n"
"                    <table>                        \n"
"                        <t t-foreach=\"ctx.get('mail_name')\" t-as=\"record\">\n"
"                            <tr><td><b><t t-out=\"record\"/></b></td></tr>\n"
"                        </t>                   \n"
"                    </table>\n"
"               \n"
"                <br />\n"
"                Please do the needful to regularize this status.\n"
"                <br />\n"
"                Thanks,\n"
"                <br />\n"
"                HUMAN RESOURCE DEPARTMENT\n"
"                <br />\n"
"                AUM -->\n"
"        "
msgstr ""
"<style type=\"text/css\">\n"
"                *{\n"
"                    font-family:Arial, Helvetica, sans-serif ;\n"
"                    }\n"
"            </style>\n"
"\n"
"            <div>\n"
"                <table border=\"0\" style=\"padding: 16px 0; background-color: rgb(241, 241, 241); font-family: Verdana, Arial, sans-serif; color: rgb(69, 71, 72); width: 100%; border-collapse: separate; border-spacing: 0px;\">\n"
"                    <tbody>\n"
"                        <tr>\n"
"                            <td align=\"center\">\n"
"                                <table border=\"0\" width=\"760\" style=\"padding: 24px; background-color: white; color: rgb(69, 71, 72); border-collapse: separate; border-spacing: 0px;\">\n"
"                                    <tbody>\n"
"                                        <tr>\n"
"                                            <td align=\"center\" style=\"min-width: 760px;\">\n"
"                                                <table border=\"0\" width=\"100%\" style=\"background-color: white; padding: 0px; border-collapse: separate; border-spacing: 0px;\">\n"
"                                                    <tbody>\n"
"                                                        <tr>\n"
"                                                            <td valign=\"middle\">\n"
"                                                                <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                                                                    <t t-out=\"ctx.get('notice')\"/>\n"
"                                                                </span>\n"
"                                                            </td>\n"
"                                                            <!-- <td valign=\"middle\" align=\"right\">\n"
"                                                                <img style=\"padding: 0px; margin: 0px; height: 48px;\" alt=\"Sambala\" src=\"https://sambala.net/logo.png?company=1\"/>\n"
"                                                            </td> -->\n"
"                                                        </tr>\n"
"                                                        <tr>\n"
"                                                            <td colspan=\"2\" style=\"text-align:center;\">\n"
"                                                                <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:4px 0px 32px 0px;\"/>\n"
"                                                            </td>\n"
"                                                        </tr>\n"
"                                                    </tbody>\n"
"                                                </table>\n"
"                                            </td>\n"
"                                        </tr>\n"
"                                        <tr>\n"
"                                            <td style=\"min-width: 760px;\">\n"
"                                                <span style=\"margin:0px\">\n"
"                                                    <p style=\"padding: 0 0 0 12px; margin:0;\">\n"
"                                                        Chào\n"
"                                                        <span style=\"font-weight: bold;\">\n"
"                                                            <t t-out=\"ctx.get('hrm')\"/>\n"
"                                                        </span>\n"
"                                                    </p>\n"
"                                                    \n"
"                                                    <p style=\"margin:2px 0; padding: 0 0 0 12px\">\n"
"                                                        Xin lưu ý rằng\n"
"                                                        <span style=\"font-weight: bold;\">\n"
"                                                            <t t-out=\"ctx.get('check_day')\"/>\n"
"                                                        </span>\n"
"                                                        thông tin của các nhân viên sau không có trong hệ thống kể từ\n"
"                                                        <span style=\"font-weight: bold;\">\n"
"                                                            <t t-out=\"ctx.get('date')\"/>.\n"
"                                                        </span>\n"
"                                                    </p>\n"
"                                                    \n"
"                                                    <br/>\n"
"                                                        <table>\n"
"                                                            <t t-foreach=\"ctx.get('mail_name')\" t-as=\"record\">\n"
"                                                                <tr>\n"
"                                                                    <p style=\"padding: 0 0 0 24px; margin:2px 0\">\n"
"                                                                        <t t-out=\"record\"/>\n"
"                                                                    </p>\n"
"                                                                </tr>\n"
"                                                            </t>\n"
"                                                        </table>\n"
"\n"
"                                                    <p style=\"margin:24px 0 0 0 !important; padding: 0 0 0 12px\">\n"
"                                                        Hãy làm những việc cần thiết để xử lý vấn đề này\n"
"                                                    </p>\n"
"                                                    <p style=\"padding: 0 0 0 12px; margin:5px 0 0 0;\">\n"
"                                                        Cảm ơn,\n"
"                                                    </p>\n"
"                                                    <p style=\"padding: 0 0 0 12px;\">\n"
"                                                        PHÒNG NHÂN SỰ\n"
"                                                    </p>\n"
"                                                    <p style=\"padding: 0 0 0 12px; margin:0\">AUM</p>\n"
"                                                </span>\n"
"                                            </td>\n"
"                                        </tr>\n"
"                                    </tbody>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            </div>\n"
"            <!-- Dear Bùi Ngọc Sơn,\n"
"                <br/>\n"
"                Please note that the <b><t t-out=\"ctx.get('check_day')\"/></b> information of following members are not in the system as of <b><t t-out=\"ctx.get('date')\"/></b>.\n"
"                <br/>\n"
"                    <table>                        \n"
"                        <t t-foreach=\"ctx.get('mail_name')\" t-as=\"record\">\n"
"                            <tr><td><b><t t-out=\"record\"/></b></td></tr>\n"
"                        </t>                   \n"
"                    </table>\n"
"               \n"
"                <br />\n"
"                Please do the needful to regularize this status.\n"
"                <br />\n"
"                Thanks,\n"
"                <br />\n"
"                HUMAN RESOURCE DEPARTMENT\n"
"                <br />\n"
"                AUM -->\n"
"        "

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__message_needaction
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__message_needaction
msgid "Action Needed"
msgstr "Hoạt động cần thiết"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__activity_ids
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__activity_ids
msgid "Activities"
msgstr "Các hoạt động"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__activity_exception_decoration
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Thêm ngoại lệ cho hoạt động"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__activity_state
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__activity_state
msgid "Activity State"
msgstr "Trạng thái hoạt động"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__activity_type_icon
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__activity_type_icon
msgid "Activity Type Icon"
msgstr "Biểu tượng loại hoạt động"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__message_follower_ids
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__message_partner_ids
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__has_message
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__message_is_follower
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__message_main_attachment_id
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__message_main_attachment_id
msgid "Main Attachment"
msgstr "Phần đính kèm chính"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__message_has_error
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__message_has_error
msgid "Message Delivery error"
msgstr "Lỗi gửi tin nhắn"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__message_ids
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__message_ids
msgid "Messages"
msgstr "Tin nhắn"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__my_activity_date_deadline
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Thời hạn hoạt động của tôi"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__activity_calendar_event_id
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sự kiện lịch hoạt động tiếp theo"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__activity_date_deadline
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Thời hạn hoạt động tiếp theo"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__activity_summary
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__activity_summary
msgid "Next Activity Summary"
msgstr "Tóm tắt hoạt động tiếp theo"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__activity_type_id
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__activity_type_id
msgid "Next Activity Type"
msgstr "Loại hoạt động tiếp theo"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__activity_user_id
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__activity_user_id
msgid "Responsible User"
msgstr "Người dùng có trách nhiệm"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_th_attendance_request__message_has_sms_error
#: model:ir.model.fields,field_description:th_attendance.field_th_check_attendence__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Lỗi gửi SMS"

#. module: th_attendance
#: code:addons/th_attendance/models/th_attendance_request.py:0
#, python-format
msgid "You only can delete request in Draft state!"
msgstr "Bạn chỉ có thể xóa yêu cầu ở trạng thái nháp"

#. module: th_attendance
#: code:addons/th_attendance/wizard/import_attendance_wizard.py:0
#, python-format
msgid "Success: %s, Update: %s, Unknown employee: %s"
msgstr "Thành công: %s, Cập nhật: %s, Không xác định nhân viên: %s"

#. module: th_attendance
#: code:addons/th_attendance/wizard/import_attendance_wizard.py:0
#, python-format
msgid "Import successfully."
msgstr "Tải dữ liệu thành công."

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_view_attendance_worked_days_tree
msgid "Work Hours"
msgstr "Giờ làm việc"

#. module: th_attendance
#: model:ir.actions.act_window,name:th_attendance.th_hr_attendance_check_worked_days_action
#: model:ir.ui.menu,name:th_attendance.menu_hr_attendance_view_check_worked_days
msgid "Check Worked Days"
msgstr "Kiểm tra công"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_hr_attendance_view_filter
msgid "This month"
msgstr "Tháng này"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_hr_attendance_view_filter
msgid "Check In"
msgstr "Vào Làm"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_hr_attendance_view_filter
msgid "Check Out"
msgstr "Tan Làm"

#. module: th_attendance
#: code:addons/th_attendance/models/th_attendance_request.py:0
#, python-format
msgid "Requested date must be less than or equal to today."
msgstr "Ngày gửi yêu cầu phải nhỏ hơn hoặc bằng ngày hôm nay."

#. module: th_attendance
#: code:addons/th_attendance/models/th_attendance_request.py:0
#, python-format
msgid "You can only have a maximum of 3 requests per month!"
msgstr "Bạn chỉ có thể có tối đa 3 lần bù công trong tháng!"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Employee PIN and show menu External "
"attendance</span>"
msgstr ""
"<span class=\"o_form_label\">Mã PIN nhân viên và hiển thị menu chấm công online "
"</span>"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_res_config_settings_view_form
msgid "Use PIN codes to check in/out in External attendance"
msgstr "Sử dụng mã PIN để chấm công vào/ra trong chấm công online"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_view_missed_check_in_today
msgid "Attendance Code"
msgstr "Mã chấm công"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_hr_employee__th_report_department
msgid "Department AUM"
msgstr "Phòng ban"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_res_config_settings_view_form
msgid "Ex: WORK100"
msgstr "Ví dụ: WORK100"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_res_config_settings_view_form
msgid "Ex: WFH"
msgstr "Ví dụ: WFH"

#. module: th_attendance
#. openerp-web
#: code:addons/th_attendance/static/src/xml/view_button_regenerate.xml:0
#, python-format
msgid "Export to excel"
msgstr "Xuất file excel"

#. module: th_attendance
#. openerp-web
#: code:addons/th_attendance/static/src/xml/view_kiosk_mode.xml:0
#, python-format
msgid "External Attendance"
msgstr "Chấm công online"

#. module: th_attendance
#. openerp-web
#: code:addons/th_attendance/static/src/xml/view_button_regenerate.xml:0
#, python-format
msgid "Regenerate"
msgstr "Tạo lại chấm công"

#. module: th_attendance
#: code:addons/th_attendance/report/unknown_employee.py:0
#, python-format
msgid "Unknown Employee List"
msgstr "Danh sách nhân viên mới"

#. module: th_attendance
#: code:addons/th_attendance/report/unknown_employee.py:0
#, python-format
msgid "No"
msgstr "Stt"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_attendance_request_view_form
msgid "Back to draft"
msgstr "Quay về bản nháp"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_hr_attendance__department_name
msgid "Department name"
msgstr "Phòng ban"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_report_th_attendance_attendance_period_report__date_from
#: model:ir.model.fields,field_description:th_attendance.field_report_th_attendance_attendance_period_report_all__date_from
msgid "Date from"
msgstr "Từ ngày"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_report_th_attendance_attendance_period_report__date_to
#: model:ir.model.fields,field_description:th_attendance.field_report_th_attendance_attendance_period_report_all__date_to
msgid "Date to"
msgstr "Đến ngày"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_hr_attendance__th_is_online
msgid "Online Attendance"
msgstr "Chấm công online"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_hr_attendance__th_is_changed
msgid "Changed by attendance request"
msgstr "Điểm danh bù"

#. module: th_attendancedays
#: model:ir.model.fields.selection,name:th_attendance.selection__hr_attendance__state__wait
msgid "Waiting approve"
msgstr "Chờ duyệt"

#. module: th_attendance
#: model:ir.model.fields.selection,name:th_attendance.selection__hr_attendance__state__approved
msgid "Approved"
msgstr "Đã duyệt"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_view_attendance_tree
msgid "Approve"
msgstr "Phê duyệt"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_hr_attendance__state
msgid "State"
msgstr "Trạng thái"

#. module: th_attendance
#: model:ir.model.fields,field_description:th_attendance.field_hr_attendance__th_real_work_numbers
msgid "Real worked number"
msgstr "Công máy"

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_attendance_request_view_search
msgid "Need approve"
msgstr "Bản ghi cần duyệt"

#. module: th_attendance
#: code:addons/th_attendance/models/hr_attendance.py:0
#: code:addons/th_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Overlapping time slices is not allowed. Please check attendance data of "
"%(employee)s in %(error_time)s"
msgstr ""
"Thời gian điểm danh không cho phép chồng chéo. Xin vui lòng kiểm tra dữ liệu chấm công của "
"%(employee)s %(error_time)s"

#. module: th_attendance
#: code:addons/th_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Overlapping time slices is not allowed. Please check attendance data of "
"%(employee)s in %(error_time_from)s"
msgstr ""
"Thời gian điểm danh không cho phép chồng chéo. Xin vui lòng kiểm tra dữ liệu chấm công của "
"%(employee)s %(error_time_from)s"

#. module: th_attendance
#: code:addons/th_attendance/models/hr_attendance.py:0
#, python-format
msgid "Checkin and checkout must be in same day!."
msgstr "Thời gian vào làm và tan làm phải cùng trong một ngày!."

#. module: th_attendance
#: model_terms:ir.ui.view,arch_db:th_attendance.th_attendance_request_view_form
msgid "Detail attendance"
msgstr "Chi tiết chấm công"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_root
msgid "Attendances"
msgstr "Chấm công"