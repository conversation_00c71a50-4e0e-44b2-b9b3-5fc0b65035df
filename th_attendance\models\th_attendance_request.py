from datetime import datetime
import datetime as dt
from odoo import models, fields, _, api, exceptions
from odoo.exceptions import UserError, ValidationError
from dateutil.relativedelta import relativedelta
from odoo.addons.resource.models.resource import float_to_time
from odoo.tools import float_round


def time_to_float(t):
    return float_round(t.hour + t.minute/60 + t.second/3600, precision_digits=2)


class AttendanceRequest(models.Model):
    _name = 'th.attendance.request'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Attendance request'

    _sql_constraints = [
        ('check_th_time', 'CHECK(th_time >= 0 AND th_time < 24)', _('Invalid time value!'))
    ]

    name = fields.Char('Name', required=1)
    th_employee_id = fields.Many2one('hr.employee', string='Employee', required=1, ondelete='cascade')
    th_time = fields.Float('Time')
    th_status = fields.Selection([('draft', 'Draft'), ('waiting', 'Waiting'), ('confirm1', '<PERSON><PERSON> duyệt lần 1'), ('confirm', 'Confirmed'), ('refuse', 'Refused')], string='Status', default='draft', required=1, tracking=True)
    th_is_visible_button = fields.Boolean(default=False, compute='_compute_th_is_visible_button')
    th_reason = fields.Char(string="Reason")
    th_date_req = fields.Date(string="Request Date", default=fields.date.today(), required=1)
    th_attendance_request_ids = fields.Many2many("th.attendance.request", string="Yêu cầu điều chỉnh công", compute="_compute_th_attendance_request_ids")
    th_type = fields.Selection([('morning_in', 'Morning Check-in'),
                                ('morning_out', 'Morning Check-out'),
                                ('afternoon_in', 'Afternoon Check-in'),
                                ('afternoon_out', 'Afternoon Check-out')], string='Type', )
    th_attendance_ids = fields.Many2many("hr.attendance", string="Chấm công", compute="_compute_th_attendance_ids")
    # Handling all attendance cases
    th_time1 = fields.Float("Time 1", compute='_compute_all_time')
    th_select_time1 = fields.Boolean("")
    th_time2 = fields.Float("Time 2", compute='_compute_all_time')
    th_select_time2 = fields.Boolean("")
    th_time3 = fields.Float("Time 3", compute='_compute_all_time')
    th_select_time3 = fields.Boolean("")
    th_time4 = fields.Float("Time 4", compute='_compute_all_time')
    th_select_time4 = fields.Boolean("")
    th_time5 = fields.Float("Time 5", compute='_compute_all_time')
    th_select_time5 = fields.Boolean("")
    th_time6 = fields.Float("Time 6", compute='_compute_all_time')
    th_select_time6 = fields.Boolean("")
    th_time7 = fields.Float("Time 7", compute='_compute_all_time')
    th_select_time7 = fields.Boolean("")
    th_time8 = fields.Float("Time 8", compute='_compute_all_time')
    th_select_time8 = fields.Boolean("")

    th_attendance_type = fields.Selection(selection=[('compensation', 'Bù công'), ('excess_work', 'Thừa công')], string="Loại điều chỉnh", default='compensation')

    @api.constrains('th_employee_id', 'th_attendance_type')
    def _constrains_th_compensated(self):
        for rec in self:
            # if rec.th_attendance_type and rec.th_attendance_type == 'compensation' and rec.th_employee_id and not rec.th_employee_id.th_compensated:
            #     raise ValidationError(_('%s không có quyền bù công. Vui lòng liện hệ với Hành chính nhân sự để biết thêm thông tin!', 'Bạn' if self.env.user.employee_id == rec.th_employee_id else rec.th_employee_id.name))
            if rec.th_attendance_type and rec.th_employee_id and any(self.env['hr.attendance'].search([('th_att_code', '=', 'WFH'), ('id', '!=', rec.id), ('th_attendance_date', '=', rec.th_date_req), ('employee_id', '=', rec.th_employee_id.id), ('state', '=', 'approved')])):
                raise ValidationError(_('Không thể điều chỉnh công với ngày chấm công online.'))

    @api.depends('th_attendance_ids', 'th_time')
    def _compute_all_time(self):
        for rec in self:
            if not rec.th_time or rec.th_status == 'confirm':
                time_list = []
            else:
                time_list = [rec.th_time]
            for attendance in rec.th_attendance_ids:
                attendance.check_in and time_list.append(time_to_float(attendance.check_in + relativedelta(hours=7)))
                attendance.check_out and time_list.append(time_to_float(attendance.check_out + relativedelta(hours=7)))
            time_list = list(dict.fromkeys(time_list))
            time_list.sort()
            diff_len = 9 - len(time_list)
            [time_list.append(0) for _ in range(diff_len)]
            rec.th_time1 = time_list[0]
            rec.th_time2 = time_list[1]
            rec.th_time3 = time_list[2]
            rec.th_time4 = time_list[3]
            rec.th_time5 = time_list[4]
            rec.th_time6 = time_list[5]
            rec.th_time7 = time_list[6]
            rec.th_time8 = time_list[7]

    @api.onchange('th_time1', 'th_time2', 'th_time3', 'th_time4', 'th_time5', 'th_time6', 'th_time7', 'th_time8')
    def _onchange_th_time(self):
        for rec in self:
            rec.th_select_time1 = bool(rec.th_time1)
            rec.th_select_time2 = bool(rec.th_time2)
            rec.th_select_time3 = bool(rec.th_time3)
            rec.th_select_time4 = bool(rec.th_time4)
            rec.th_select_time5 = bool(rec.th_time5)
            rec.th_select_time6 = bool(rec.th_time6)
            rec.th_select_time7 = bool(rec.th_time7)
            rec.th_select_time8 = bool(rec.th_time8)

    @api.depends('th_date_req', 'th_employee_id')
    def _compute_th_attendance_request_ids(self):
        for rec in self:
            date = rec.th_date_req.replace(day=1) if rec.th_date_req else fields.Date.today().repalce(day=1)
            attendance_request_ids = self.search([('th_employee_id', '=', rec.th_employee_id.id), ('th_date_req', '>=', date), ('th_date_req', '<', date + relativedelta(months=1))])
            rec.th_attendance_request_ids = [(6, 0, attendance_request_ids.ids)]

    @api.depends('th_date_req', 'th_employee_id')
    def _compute_th_attendance_ids(self):
        for rec in self:
            rec.th_attendance_ids = self.sudo().env['hr.attendance'].search([('th_attendance_date', '=', rec.th_date_req),
                                                                             ('employee_id', '=', rec.th_employee_id.id),
                                                                             ('th_att_code', '=', 'WORK100')])

    @api.constrains('th_type', 'th_date_req', 'th_employee_id')
    def _check_th_type_in_the_same_day(self):
        record = self.search([('id', '!=', self.id), ('th_employee_id', '=', self.th_employee_id.id), ('th_date_req', '=', self.th_date_req), ('th_type', '=', self.th_type), ('th_status', '!=', 'refuse')])
        if record:
            raise ValidationError(_('You already have an attendance request record of the same type on %s, you can not create another one!') % (datetime.strftime(self.th_date_req, '%d/%m/%Y')))

    # @api.constrains('th_date_req')
    # def _check_th_date_req(self):
    #     if self.th_date_req > fields.date.today():
    #         raise ValidationError(_('Requested date must be less than or equal to today.'))

    @api.onchange('th_type')
    def _onchange_th_type(self):
        for rec in self:
            default_time = {'morning_in': 8, 'morning_out': 12, 'afternoon_in': 13, 'afternoon_out': 17}
            rec.th_time = default_time.get(rec.th_type, 0)

    # @api.constrains('th_employee_id')
    # def _check_max_time_request(self):
    #     for rec in self:
    #         if len(rec.th_attendance_request_ids.filtered_domain([('th_status', '=', 'confirm')])) >= 3:
    #             raise ValidationError(_('You can only have a maximum of 3 requests per month!'))

    @api.model
    def default_get(self, fields):
        res = super().default_get(fields)
        if 'th_employee_id' in fields and not res.get('th_employee_id'):
            res['th_employee_id'] = self.env.user.employee_id if self.env.user.employee_id else False
        return res

    # Hàm gửi mail
    def action_notify_send_mail(self, partner_ids, record_id, message, mail_name, text='', note=''):
        template_id = self.env['ir.model.data']._xmlid_to_res_id('th_attendance.template_attendance_request_th', raise_if_not_found=False)
        if not template_id:
            return
        values = {
            'manager': ", ".join(partner_ids.mapped("name")),
            'message': text,
            'object': self,
            'access_link': self._notify_get_action_link('view'),
        }
        assignation_msg = self.env['ir.qweb']._render('th_attendance.template_attendance_request_th', values, minimal_qcontext=True)
        assignation_msg = self.env['mail.render.mixin']._replace_local_links(assignation_msg)
        try:
            self.sudo().message_notify(
                subject=mail_name,
                body=assignation_msg,
                partner_ids=partner_ids.user_id.partner_id.ids,
                record_name=record_id.display_name,
                email_layout_xmlid='mail.mail_notification_light',
                mail_auto_delete=False,
            )
        except Exception as e:
            raise UserError(e)

    def _compute_th_is_visible_button(self):
        env = self.env
        for rec in self:
            condition = rec.th_status in ['waiting', 'confirm1'] and (rec.sudo().th_employee_id.parent_id.user_id.id == env.uid or env.user.has_group('hr_attendance.group_hr_attendance_manager'))
            if condition:
                rec.th_is_visible_button = True
            else:
                rec.th_is_visible_button = False

    # @api.model
    # def create(self, vals):
    #     if not vals.get('name'):
    #         date = datetime.strftime(fields.Datetime.to_datetime(vals.get('th_date_req')), '%d/%m/%Y')
    #         vals['name'] = _("%s's Attendance Request on %s") % (self.env['hr.employee'].browse(vals.get('th_employee_id')).name, date)
    #     return super().create(vals)

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if not vals.get('name'):
                date = datetime.strftime(fields.Datetime.to_datetime(vals.get('th_date_req')), '%d/%m/%Y')
                vals['name'] = _("%s's Attendance Request on %s") % (self.env['hr.employee'].browse(vals.get('th_employee_id')).name, date)
        return super().create(vals_list)

    def action_send_to_approver(self):
        self.write({'th_status': 'waiting'})
        manager_id = self.sudo().th_employee_id.leave_manager_id.employee_id
        mail_name = f'Phiếu Điều chỉnh chấm công'
        message = 'Bạn có phiếu điều chỉnh chấm công cần xác nhận'
        self.action_notify_send_mail(manager_id, self, message, mail_name, f'Bạn có yêu cầu điều chỉnh chấm công ngày {datetime.strftime(self.th_date_req, "%d/%m/%Y")}')

    def _create_record(self):
        time_to_create = []
        self.th_select_time1 and self.th_time1 and time_to_create.append(self.th_time1)
        self.th_select_time2 and self.th_time2 and time_to_create.append(self.th_time2)
        self.th_select_time3 and self.th_time3 and time_to_create.append(self.th_time3)
        self.th_select_time4 and self.th_time4 and time_to_create.append(self.th_time4)
        self.th_select_time5 and self.th_time5 and time_to_create.append(self.th_time5)
        self.th_select_time6 and self.th_time6 and time_to_create.append(self.th_time6)
        self.th_select_time7 and self.th_time7 and time_to_create.append(self.th_time7)
        self.th_select_time8 and self.th_time8 and time_to_create.append(self.th_time8)
        creation_time = []
        for time in time_to_create:
            ftime = float_to_time(time)
            check_time = fields.Datetime.to_datetime(self.th_date_req).replace(hour=ftime.hour - 7, minute=ftime.minute)
            if not creation_time:
                creation_time.append([check_time, False])
            else:
                if not creation_time[-1][-1]:
                    creation_time[-1][-1] = check_time
                else:
                    creation_time.append([check_time, False])
        self.sudo().env['hr.attendance'].search([('th_attendance_date', '=', self.th_date_req),
                                                 ('employee_id', '=', self.th_employee_id.id),
                                                 ('th_att_code', '=', 'WORK100')]).unlink()
        for time in creation_time:
            self.sudo().env['hr.attendance'].create({
                'employee_id': self.th_employee_id.id,
                'check_in': time[0],
                'check_out': time[1],
                'th_is_changed': True,
            })

    # Nút xác nhận điều chỉnh công
    def action_confirm(self):
        # add time to attendance
        att_online = self.env['hr.attendance'].search(
            [('th_att_code', '=', 'WFH'), ('th_attendance_date', '=', self.th_date_req),
             ('employee_id', '=', self.th_employee_id.id), ('state', '=', 'approved')])
        if self.th_attendance_type and self.th_employee_id and any(att_online):
            for att_o in att_online:
                if (self.th_type in ['morning_in', 'morning_out'] and att_o.th_is_morning_att) or (
                        self.th_type in ['afternoon_in', 'afternoon_out'] and not att_o.th_is_morning_att):
                    raise ValidationError('Đã có công online đã được duyệt. Vui lòng xem lại!')

        # Lấy bản ghi những nhân sự duyệt nghỉ phép và chấm công bù lần 2 (cấu hình trong Config module Nhân viên)
        hr_approve = self.env['res.users'].sudo().search(
            [('id', 'in', eval(self.env['ir.config_parameter'].sudo().get_param('th_l_a_manager_ids')))])
        # Logic nếu user hiện tại là người duyệt nghỉ phép và chấm công bù lần 2
        if self.env.user.id in hr_approve.ids:
            view_id = self.env.ref('th_attendance.attendance_request_approve_excess_work_view_tree').id
            excess_work = True
            time_list = []
            dict_attendance_time = {}
            attendance_ids = self.env['hr.attendance'].search([('th_attendance_date', '=', self.th_date_req),
                                                               ('employee_id', '=', self.th_employee_id.id),
                                                               ('th_att_code', '=', 'WORK100')])
            self.env['th.attendance.request.approve'].search([('th_attendance_request_id', '=', self.id)]).unlink()
            for attendance in attendance_ids:
                if attendance.check_in:
                    time_list.append(time_to_float(attendance.check_in + relativedelta(hours=7)))
                    dict_attendance_time[(time_to_float(attendance.check_in + relativedelta(hours=7)))] = attendance.id
                if attendance.check_out:
                    time_list.append(time_to_float(attendance.check_out + relativedelta(hours=7)))
                    dict_attendance_time[(time_to_float(attendance.check_out + relativedelta(hours=7)))] = attendance.id
            if self.th_attendance_type == 'compensation':
                view_id = self.env.ref('th_attendance.attendance_request_approve_compensation_view_tree').id
                excess_work = False
                time_list.append(self.th_time)
            time_list = list(dict.fromkeys(time_list))
            time_list.sort()
            vals_list = []
            th_is_request_morning = True

            # if self.th_type in ['afternoon_in', 'afternoon_out'] and len(time_list) > 2:
            if self.th_type in ['afternoon_in', 'afternoon_out']:
                th_is_request_morning = False

            for time in time_list:
                val = {'th_employee_id': self.th_employee_id.id,
                       'th_date_req': self.th_date_req,
                       'th_time': time,
                       'th_state': 'new' if self.th_time == time else 'old',
                       'th_attendance_request_id': self.id,
                       'th_attendance_id': dict_attendance_time.get(time) if self.th_time != time else False,
                       'th_is_request_morning': th_is_request_morning
                       }
                vals_list.append(val)
            self.env['th.attendance.request.approve'].create(vals_list)
            return {
                'type': 'ir.actions.act_window',
                'name': _('Chỉnh sửa công'),
                'view_mode': 'tree',
                'res_model': 'th.attendance.request.approve',
                'views': [[view_id, 'list']],
                'target': 'new',
                'context': {'default_th_employee_id': self.th_employee_id.id,
                            'default_th_check_in': self.th_date_req,
                            'default_th_check_out': self.th_date_req,
                            'default_th_attendance_request_id': self.id,
                            'excess_work': excess_work,
                            'view_approve': True},
                'domain': [('th_attendance_request_id', '=', self.id)],
                'help': _('''<p class="o_view_nocontent">
                    Nhân viên chưa có dữ liệu chấm công
                </p>'''),
            }
        # Logic nếu user hiện tại không phải là người duyệt nghỉ phép và chấm công bù lần 2
        else:
            # Lấy kiểu phê duyệt (cấu hình trong Config module Chuyên cần)
            th_validation_type = self.env['ir.config_parameter'].sudo().get_param('th_validation_type')
            # Logic nếu kiểu phê duyệt là 'Quản lý và HR' và trạng thái phiếu đang Chờ xác nhận
            if th_validation_type == 'twice' and self.th_status == 'waiting':
                mail_name = f'Phiếu Điều chỉnh chấm công'
                message = 'Bạn có phiếu điều chỉnh chấm công cần xác nhận'
                for rec in self:
                    rec.sudo().write({'th_status': 'confirm1'})
                    # Lấy HCNS duyệt nghỉ phép và chấm công bù lần 2
                    if hr_approve:
                        manager_ids = hr_approve
                    # Lấy quản lý của nhân sự nếu không có HCNS duyệt nghỉ phép và chấm công bù lần 2
                    else:
                        manager_ids = self.sudo().th_employee_id.leave_manager_id
                    if manager_ids:
                        self.action_notify_send_mail(manager_ids.employee_id, self, message, mail_name,
                                                     f'Bạn có yêu cầu duyệt lần 2 cho một bản ghi điều chỉnh chấm công ngày {datetime.strftime(self.th_date_req, "%d/%m/%Y")}')
            else:
                raise ValidationError(
                    "Phiếu chấm công đã duyệt lần 1. Vui lòng chờ hành chính nhân sự hoặc quản lý nhân sự duyệt lần 2.")

    def check_second_approve(self):
        th_l_a_manager_ids = self.env['ir.config_parameter'].sudo().get_param('th_l_a_manager_ids')
        if th_l_a_manager_ids:
            return eval(th_l_a_manager_ids)
        else:
            return self.th_employee_id.leave_manager_id.ids

    def action_refuse_open_view(self):
        self.ensure_one()
        return {
            'name': _('Reason'),
            'view_mode': 'form',
            'res_model': 'th.attendance.request',
            'views': [(self.env.ref('th_attendance.th_attendance_request_view_form_refuse').id, "form")],
            'type': 'ir.actions.act_window',
            'res_id': self.id,
            'target': 'new',
        }

    def action_refuse(self):
        self.write({'th_status': 'refuse'})
        mail_name = f'Phiếu Chấm công bù'
        message = 'Phiếu chấm công bù của đã bị từ chối'
        self.action_notify_send_mail(self.th_employee_id, self, message, mail_name, f'Phiếu chấm công bù ngày {datetime.strftime(self.th_date_req, "%d/%m/%Y")} của bạn đã bị từ chối với lí do "{self.th_reason}"')

    def action_back_to_draft(self):
        if self.th_status != 'refuse':
            return
        self.th_status = 'draft'
        self.th_reason = False


    def write(self, vals):
        if vals.get('th_date_req') or vals.get('th_employee_id'):
            date = fields.Datetime.to_datetime(vals.get('th_date_req')) if vals.get('th_date_req') else self.th_date_req
            employee = self.env['hr.employee'].browse(vals.get('th_employee_id')).name if vals.get('th_employee_id') else self.th_employee_id.name
            vals['name'] = _("%s's Attendance Request on %s") % (employee, datetime.strftime(date, '%d/%m/%Y'))
        if vals.get('th_status') and vals.get('th_status') == 'confirm':
            for rec in self:
                mail_name = f'Phiếu Điều chỉnh chấm công'
                message = 'Bạn có phiếu điều chỉnh chấm công cần xác nhận'
                self.action_notify_send_mail(rec.th_employee_id, self, message, mail_name,
                    f'Phiếu điều chỉnh chấm công ngày {datetime.strftime(rec.th_date_req, "%d/%m/%Y")} '
                    f'đã được xác nhận bởi {self.env.user.employee_id.name}'
            )
        return super().write(vals)

    def unlink(self):
        for rec in self:
            if rec.th_status != 'draft':
                raise ValidationError(_('You only can delete request in Draft state!'))
        return super(AttendanceRequest, self).unlink()
