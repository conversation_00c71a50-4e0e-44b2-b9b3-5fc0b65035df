from odoo import fields, models


class AcademicQualifications(models.Model):
    _name = "th.qualifications"
    _description = "Academic Qualification"

    th_employee_id = fields.Many2one('hr.employee', string='Employee')
    th_institution = fields.Many2one('th.academic', string='Academic Institution')
    th_name_qlf = fields.Char('Name of Qualification')
    th_date_qlf = fields.Date('Date of Qualification')
    th_major = fields.<PERSON><PERSON>('Major')
