import json

from odoo import fields, models, api
from odoo.exceptions import ValidationError
ORIGIN_LINE = ['th_setup_parameters.th_origin_vmc', 'th_setup_parameters.th_origin_vstep', ]
class APMContactTrait(models.Model):
    _name = "th.apm.contact.trait"
    _description = "Đặc điểm liên hệ"

    # def _domain_th_origin(self):
    #     rec = self.env.ref('th_setup_parameters.th_apm_module')
    #     return [('th_module_ids', 'in', rec.ids)]


    # th_product_line_id = fields.Many2one('th.apm.product.line', string="Dòng sản phẩm", required=True)
    th_origin_id = fields.Many2one('th.origin', string="Dòng sản phẩm", required=True, domain="[('th_module_ids', '=','APM')]")
    th_apm_trait_id = fields.Many2one(comodel_name="th.apm.trait", string="Đặc điểm")
    th_apm_trait_value_ids = fields.Many2many("th.apm.trait.value", string="Giá trị", domain="[('th_apm_trait_id', '=', th_apm_trait_id)]")
    th_partner_id = fields.Many2one(comodel_name="res.partner", index=True, string="Liên hệ")
    th_trait_domain = fields.Char(compute='_compute_th_trait_domain')
    th_trait_value_domain = fields.Char(compute='_compute_th_trait_value_domain')
    th_apm_contact_lead_id = fields.Many2one(comodel_name="th.apm")
    # th_product_line_domain = fields.Char(compute='_compute_th_product_category_domain', default="[]")

    # @api.depends('th_origin_id')
    # def _compute_th_product_category_domain(self):
    #     for rec in self:
    #         domain = []
    #         domain.append(('id', 'in', self.env['th.origin'].search([('th_module_ids', '=','APM')]).ids))
    #         rec.th_product_line_domain = json.dumps(domain)

    @api.constrains('th_origin_id', 'th_apm_trait_id')
    def _check_duplicate_values(self):
        for rec in self:
            if rec.th_partner_id and len(self.search([('th_origin_id', '=', rec.th_origin_id.id),
                                                      ('th_apm_trait_id', '=', rec.th_apm_trait_id.id),
                                                      ('id', '!=', rec.id),
                                                      ('th_partner_id', '=', rec.th_partner_id.id)
                                                      ])) > 0:
                raise ValidationError('Không thể tồn tại 2 bản ghi có "dòng sản phẩm" và "đặc điểm" giống nhau trên cùng một liên hệ!')
    @api.model
    def create(self, values):
        rec = super(APMContactTrait, self).create(values)
        # Add code here
        return rec
    @api.depends('th_origin_id')
    def _compute_th_trait_domain(self):
        for rec in self:
            domain = []
            if rec.th_origin_id:
                domain.append(('th_origin_id', 'in', rec.th_origin_id.ids))
                if rec.th_apm_trait_id and rec.th_apm_trait_id.th_origin_id.id != rec.th_origin_id.id:
                    rec.th_apm_trait_value_ids = False
                    rec.th_apm_trait_id = False
            else:
                rec.th_apm_trait_id = False
                rec.th_apm_trait_value_ids = False
            rec.th_trait_domain = json.dumps(domain)

    @api.depends('th_apm_trait_id', 'th_apm_trait_value_ids', 'th_origin_id')
    def _compute_th_trait_value_domain(self):
        for rec in self:
            rec = rec.sudo()
            domain = []
            if rec.th_origin_id:
                domain.append(('id', 'in', self.env['th.apm.trait'].search([('th_origin_id', '=', rec.th_origin_id.id)]).mapped('th_apm_trait_value_ids').ids))
                if rec.th_apm_trait_id:
                    domain.append(('th_apm_trait_id', 'in', rec.th_apm_trait_id.ids))
                    if rec.th_apm_trait_value_ids and rec.th_apm_trait_value_ids.th_apm_trait_id.id != rec.th_apm_trait_id.id:
                        rec.th_apm_trait_value_ids = False
                else:
                    rec.th_apm_trait_value_ids = False
            elif rec.th_origin_id and not rec.th_apm_trait_id:
                domain.append(('th_apm_trait_id', 'in', self.env['th.apm.trait'].search([('th_origin_id', '=', rec.th_origin_id.id)]).ids))
            else:
                rec.th_apm_trait_value_ids = False
                domain.append(('id', 'in', self.env['th.apm.trait'].search([('th_origin_id', 'in', [self.env.ref(xml_id).id for xml_id in ORIGIN_LINE])]).mapped('th_apm_trait_value_ids').ids))
            rec.th_trait_value_domain = json.dumps(domain)

    @api.onchange('th_apm_trait_value_ids')
    def _onchange_th_apm_trait_value_ids(self):
        if self.th_apm_trait_value_ids:
            for rec in self.th_apm_trait_value_ids:
                th_apm_trait_id = self.env['th.apm.trait.value'].search([('id', '=', rec.ids)]).th_apm_trait_id
                if th_apm_trait_id:
                    self.th_apm_trait_id = th_apm_trait_id
        else:
            self.th_apm_trait_id = False