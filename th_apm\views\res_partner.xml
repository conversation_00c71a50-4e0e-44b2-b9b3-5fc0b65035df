<odoo>
    <record id="th_view_partner_tree" model="ir.ui.view">
            <field name="name">res.partner.tree</field>
            <field name="model">res.partner</field>
            <field eval="8" name="priority"/>
            <field name="arch" type="xml">
                <tree string="Contacts" sample="1" multi_edit="1">
                    <header>
                        <button name="th_create_lead_apm_after_sales_care" string="Tạo cơ hội" type="object" class="btn btn-primary" invisible="1"/>
                        <button name="th_delete_lead_apm_after_sales_care" string="Xóa c<PERSON> hội" type="object" class="btn btn-secondary"/>
                    </header>
                    <field name="display_name" string="Tên"/>
                    <field name="function" invisible="1"/>
                    <field name="phone" class="o_force_ltr" optional="show"/>
                    <field name="email" optional="show"/>
                    <field name="user_id" optional="hide" domain="[('share', '=', False)]"/>
                    <field name="city" optional="show"/>
                    <field name="state_id" optional="hide" readonly="1"/>
                    <field name="country_id" optional="show" readonly="1"/>
                    <field name="vat" optional="hide" readonly="1"/>
                    <field name="category_id" optional="hide" widget="many2many_tags" options="{'color_field': 'color'}"/>
                    <field name="company_id" groups="base.group_multi_company" readonly="1"/>
                    <field name="is_company" invisible="1"/>
                    <field name="parent_id" invisible="1" readonly="1"/>
                    <field name="active" invisible="1"/>
                    <field name="th_origin_ids" widget="many2many_tags"/>
                </tree>
            </field>
        </record>
    <record id="th_partner_view_form_after_sale" model="ir.ui.view">
        <field name="name">th_partner_view_form_ttvh</field>
        <field name="model">res.partner</field>
        <field name="arch" type="xml">
            <form string="Partners">
                <header>
                    <button name="th_create_lead_apm_after_sales_care" type="object" class="btn btn-primary" string="Tạo chăm sóc sau bán"/>
                </header>
                <div class="alert alert-warning oe_edit_only" role="alert" attrs="{'invisible': [('same_vat_partner_id', '=', False)]}">
                  A partner with the same <span><span class="o_vat_label">Tax ID</span></span> already exists (<field name="same_vat_partner_id"/>), are you sure to create a new one?
                </div>
                <div class="alert alert-warning oe_edit_only" role="alert" attrs="{'invisible': [('same_company_registry_partner_id', '=', False)]}">
                  A partner with the same <span><span class="o_vat_label">Company Registry</span></span> already exists (<field name="same_company_registry_partner_id"/>), are you sure to create a new one?
                </div>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" name="th_action_open_order_apm"
                                type="object" icon="fa-pencil-square-o">
                            <div class="o_stat_info">
                                <span class="o_stat_text">
                                    Đơn hàng
                                </span>
                            </div>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Đã lưu trữ" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <field name="avatar_128" invisible="1"/>
                    <field name="image_1920" widget="image" class="oe_avatar" options="{&quot;preview_image&quot;: &quot;avatar_128&quot;}"/>
                    <div class="oe_title mb24">
                        <field name="is_company" invisible="1"/>
                        <field name="commercial_partner_id" invisible="1"/>
                        <field name="active" invisible="1"/>
                        <field name="company_id" invisible="1"/>
                        <field name="country_code" invisible="1"/>
                        <field name="company_type" widget="radio" options="{'horizontal': true}"/>
                        <h1>
                            <field id="company" class="text-break" name="name" default_focus="1" placeholder="ví dụ: AUM" attrs="{'required' : [('type', '=', 'contact')], 'invisible': [('is_company','=', False)]}"/>
                            <field id="individual" class="text-break" name="name" default_focus="1" placeholder="ví dụ: Nguyễn Vắn A" attrs="{'required' : [('type', '=', 'contact')], 'invisible': [('is_company','=', True)]}"/>
                        </h1>
                        <div class="o_row">
                            <field name="parent_id" widget="res_partner_many2one" placeholder="Tên công ty..." domain="[('is_company', '=', True)]" context="{'default_is_company': True, 'show_vat': True, 'default_user_id': user_id}" attrs="{'invisible': ['|', '&amp;', ('is_company','=', True),('parent_id', '=', False),('company_name', '!=', False),('company_name', '!=', '')]}"/>
                                <field name="company_name" attrs="{'invisible': ['|', '|', ('company_name', '=', False), ('company_name', '=', ''), ('is_company', '=', True)]}"/>
                                <button name="create_company" icon="fa-plus-square" string="Create company" type="object" class="oe_edit_only btn-link" attrs="{'invisible': ['|', '|', ('is_company','=', True), ('company_name', '=', ''), ('company_name', '=', False)]}"/>
                        </div>
                    </div>

                    <group>
                        <group>
                            <span class="o_form_label o_td_label" name="address_name">
                                <field name="type" attrs="{'invisible': [('is_company','=', True)], 'required': [('is_company','!=', True)], 'readonly': [('user_ids', '!=', [])]}" class="fw-bold"/>
                                <b attrs="{'invisible': [('is_company', '=', False)]}">Address</b>
                            </span>
                            <div class="o_address_format">
                                <field name="street" placeholder="Địa chỉ..." class="o_address_street" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                <field name="street2" placeholder="Street 2..." class="o_address_street" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}" invisible="1"/>
                                <field name="city" placeholder="City" class="o_address_city" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}" invisible="1"/>
                                <field name="th_ward_id" placeholder="Xã / Phường" class="o_address_ward" style="width:50%"
                                       options="{'no_create': True, 'no_open': True}"
                                       attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                <field name="th_district_id" placeholder="Quận / Huyện" class="o_address_district" style="width:50%"
                                       options="{'no_create': True, 'no_open': True}"
                                       attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                <field name="zip" placeholder="ZIP" class="o_address_zip" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}" invisible="1"/>
                                <field name="state_id" class="o_address_state" placeholder="Tỉnh / Thành phố" options="{'no_open': True, 'no_quick_create': True}" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}" context="{'country_id': country_id, 'default_country_id': country_id, 'zip': zip}"/>
                                <div name="partner_address_country" class="d-flex justify-content-between">
                                    <field name="country_id" placeholder="Quốc gia" class="o_address_country" options="{&quot;no_open&quot;: True, &quot;no_create&quot;: True}" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                </div>
                            </div>
                            <label for="th_place_of_birth_id"/>
                            <div class="o_row">
                                <field name="th_place_of_birth_id" options='{"no_open": True}'/>
                                <label for="th_birthday"/>
                                <field name="th_birthday"/>
                            </div>
                            <field name="th_gender"/>
                            <field name="phone" widget="phone"/>
                            <field name="th_phone2" widget="phone"/>
                            <field name="email" widget="email" context="{'gravatar_image': True}" attrs="{'required': [('user_ids','!=', [])]}"/>
                            <field name="vat" placeholder="ví dụ: BE0477472701" attrs="{'readonly': [('parent_id','!=',False)]}"/>
                            <field name="lang" attrs="{'invisible': [('active_lang_count', '&lt;=', 1)]}"/>
                            <field name="th_module_ids" widget="many2many_tags" required="1" options="{'no_create': True, 'no_open': True}"/>
                        </group>
                        <group>
                            <span class="o_form_label o_td_label" name="address_name">
                                <b attrs="{'invisible': [('is_company','=', True)]}">Địa chỉ thường trú</b>
                                <b attrs="{'invisible': [('is_company','=', False)]}">Địa chỉ</b>
                            </span>
                            <div class="o_address_format">
                                <field name="th_street" placeholder="Địa chỉ..." class="o_address_street" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                <field name="th_ward_permanent_id" placeholder="Xã / Phường" class="o_address_ward" style="width:50%" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                <field name="th_district_permanent_id" placeholder="Quận / Huyện" class="o_address_district" style="width:50%" options="{'no_create': True, 'no_open': True}" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                                <field name="th_state_id" class="o_address_state" style="width:100%" placeholder="Tỉnh / Thành phố" options="{'no_open': True, 'no_quick_create': True}" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}" context="{'country_id': country_id, 'default_country_id': country_id, 'zip': zip}"/>
                                <field name="th_country_id" placeholder="Quốc gia" class="o_address_country" options="{&quot;no_open&quot;: True, &quot;no_create&quot;: True}" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                            </div>
                            <label for="th_ethnicity_id"/>
                            <div class="o_row">
                                <field name="th_ethnicity_id" options='{"no_open": True}'/>
                                <label for="th_religion_id"/>
                                <field name="th_religion_id" options='{"no_open": True}'/>
                            </div>
                            <field name="title" options="{&quot;no_open&quot;: True}" placeholder="ví dụ: Ông" attrs="{'invisible': [('is_company', '=', True)]}"/>
                            <field name="function" placeholder="ví dụ: Giám đốc kinh doanh" attrs="{'invisible': [('is_company','=', True)]}"/>
                            <field name="th_citizen_identification"/>
                            <label for="th_date_identification"/>
                            <div class="o_row">
                                <field name="th_date_identification"/>
                                <label for="th_place_identification"/>
                                <field name="th_place_identification"/>
                            </div>
                            <field name="th_customer_code" readonly="1"/>
                            <field name="th_affiliate_code" readonly="1"/>
                            <field name="user_ids" invisible="1"/>
                            <field name="website" string="Website" widget="url" placeholder="e.g. https://www.odoo.com"  invisible="1"/>
                            <field name="active_lang_count" invisible="1"/>
                            <field name="category_id" widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True}" placeholder="e.g. &quot;B2B&quot;, &quot;VIP&quot;, &quot;Consulting&quot;, ..." invisible="1"/>
                        </group>
                    </group>

                    <notebook colspan="4">
                        <page string="Đặc điểm" autofocus="autofocus" attrs="{'invisible': [('th_check_module_apm', '!=', True)]}">
                            <field name="th_check_module_apm" invisible="1"/>
                            <field name="th_apm_contact_trait_ids" >
                                <tree editable="bottom">
        <!--                            <field name="th_product_line_id" options="{'no_edit': True, 'no_create': True, 'no_open': True}"/>-->
                                    <field name="th_origin_id" options="{'no_edit': True, 'no_create': True, 'no_open': True}" domain="[('th_module_ids.name', 'in', ['APM'])]"/>
                                    <field name="th_apm_trait_id" domain="th_trait_domain" options="{'no_edit': True, 'no_create': True, 'no_open': True}"/>
                                    <field name="th_apm_trait_value_ids" widget="many2many_tags" domain="th_trait_value_domain" options="{'no_edit': True, 'no_create': True, 'no_open': True}"/>
                                    <field name="th_trait_domain" invisible="1"/>
                                    <field name="th_trait_value_domain" invisible="1"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Liên hệ &amp; Địa chỉ" name="contact_addresses" autofocus="autofocus">
                            <field name="child_ids" mode="kanban" context="{'default_parent_id': active_id, 'default_street': street, 'default_street2': street2, 'default_city': city, 'default_state_id': state_id, 'default_zip': zip, 'default_country_id': country_id, 'default_lang': lang, 'default_user_id': user_id, 'default_type': 'other'}">
                                <kanban>
                                    <field name="id"/>
                                    <field name="color"/>
                                    <field name="name"/>
                                    <field name="title"/>
                                    <field name="type"/>
                                    <field name="email"/>
                                    <field name="parent_id"/>
                                    <field name="is_company"/>
                                    <field name="function"/>
                                    <field name="phone"/>
                                    <field name="street"/>
                                    <field name="street2"/>
                                    <field name="zip"/>
                                    <field name="city"/>
                                    <field name="country_id"/>
                                    <field name="mobile"/>
                                    <field name="state_id"/>
                                    <field name="image_128"/>
                                    <field name="avatar_128"/>
                                    <field name="lang"/>
                                    <!-- fields in form x2many view to diminish requests -->
                                    <field name="comment"/>
                                    <field name="display_name"/>
                                    <templates>
                                        <t t-name="kanban-box">
                                            <t t-set="color" t-value="kanban_color(record.color.raw_value)"/>
                                            <div t-att-class="color + (record.title.raw_value == 1 ? ' oe_kanban_color_alert' : '') + ' oe_kanban_global_click'">
                                                <div class="o_kanban_image">
                                                    <img alt="Contact image" t-att-src="kanban_image('res.partner', 'avatar_128', record.id.raw_value)"/>
                                                </div>
                                                <div class="oe_kanban_details">
                                                    <field name="name"/>
                                                    <div t-if="record.function.raw_value"><field name="function"/></div>
                                                    <div t-if="record.email.raw_value"><field name="email" widget="email"/></div>
                                                    <div t-if="record.type.raw_value != 'contact'">
                                                        <div>
                                                            <field name="zip"/><t t-if="record.city"> </t>
                                                            <field name="city"/>
                                                        </div>
                                                        <field t-if="record.state_id.raw_value" name="state_id"/><t t-if="record.country_id"> </t>
                                                        <field name="country_id"/>
                                                    </div>
                                                    <div t-if="record.phone.raw_value">Phone: <t t-esc="record.phone.value"/></div>
                                                    <div t-if="record.mobile.raw_value">Mobile: <t t-esc="record.mobile.value"/></div>
                                                </div>
                                            </div>
                                        </t>
                                    </templates>
                                </kanban>
                                <form string="Contact / Address">
                                    <sheet>
                                        <field name="type" required="1" widget="radio" options="{'horizontal': true}"/>
                                        <field name="parent_id" invisible="1"/>
                                        <div class="text-muted oe_edit_only">
                                            <p class="mb-0" attrs="{'invisible': [('type', '!=', 'contact')]}">
                                                <span>Use this to organize the contact details of employees of a given company (e.g. CEO, CFO, ...).</span>
                                            </p>
                                            <p class="mb-0" attrs="{'invisible': [('type', '!=', 'invoice')]}">
                                                <span>Preferred address for all invoices. Selected by default when you invoice an order that belongs to this company.</span>
                                            </p>
                                            <p class="mb-0" attrs="{'invisible': [('type', '!=', 'delivery')]}">
                                                <span>Preferred address for all deliveries. Selected by default when you deliver an order that belongs to this company.</span>
                                            </p>
                                            <p class="mb-0" attrs="{'invisible': [('type', '!=', 'private')]}">
                                                <span>Private addresses are only visible by authorized users and contain sensitive data (employee home addresses, ...).</span>
                                            </p>
                                            <p class="mb-0" attrs="{'invisible': [('type', '!=', 'other')]}">
                                                <span>Other address for the company (e.g. subsidiary, ...)</span>
                                            </p>
                                        </div>
                                        <hr/>
                                        <group>
                                            <group>
                                                <field name="name" string="Contact Name" attrs="{'required' : [('type', '=', 'contact')]}"/>
                                                <field name="title" options="{'no_open': True}" placeholder="ví dụ: Mr." attrs="{'invisible': [('type','!=', 'contact')]}"/>
                                                <field name="function" placeholder="ví dụ: Giám đốc kinh doanh" attrs="{'invisible': [('type','!=', 'contact')]}"/>
                                                <label for="street" string="Address" attrs="{'invisible': [('type','=', 'contact')]}"/>
                                                <div attrs="{'invisible': [('type','=', 'contact')]}">
                                                    <div class="o_address_format" name="div_address">
                                                        <field name="street" placeholder="Địa chỉ..." class="o_address_street"/>
                                                        <field name="street2" placeholder="Street 2..." class="o_address_street" invisible="1"/>
                                                        <field name="city" placeholder="City" class="o_address_city" invisible="1"/>
                                                        <field name="th_ward_id" placeholder="Xã / Phường" class="o_address_ward" style="width:50%"
                                                               options="{'no_create': True, 'no_open': True}"/>
                                                        <field name="th_district_id" placeholder="Quận / Huyện" class="o_address_district" style="width:50%"
                                                               options="{'no_create': True, 'no_open': True}"/>
                                                        <field name="state_id" class="o_address_state" placeholder="Tỉnh / Thành phố" options="{'no_open': True, 'no_quick_create': True}" context="{'country_id': country_id, 'default_country_id': country_id, 'zip': zip}"/>
                                                        <field name="zip" placeholder="ZIP" class="o_address_zip" invisible="1"/>
                                                        <field name="country_id" placeholder="Quốc gia" class="o_address_country" options="{&quot;no_open&quot;: True, &quot;no_create&quot;: True}"/>
                                                    </div>
                                                </div>
                                            </group>
                                            <group>
                                                <field name="email" widget="email"/>
                                                <field name="phone" widget="phone"/>
                                                <field name="mobile" widget="phone"/>
                                                <field name="company_id" invisible="1"/>
                                            </group>
                                        </group>
                                        <group>
                                            <field name="comment" placeholder="Ghi chú nội bộ..." nolabel="1" colspan="2"/>
                                        </group>
                                        <field name="lang" invisible="True"/>
                                        <field name="user_id" invisible="True"/>
                                    </sheet>
                                </form>
                            </field>
                        </page>
                        <page name="sales_purchases" string="Bán hàng &amp; Mua hàng">
                            <group name="container_row_2">
                                <group string="Sales" name="sale" priority="1">
                                    <field name="user_id" domain="[('share', '=', False)]"/>
                                </group>
                                <group string="Purchase" name="purchase" priority="2">
                                </group>
                                <group name="misc" string="Misc">
                                    <field name="company_registry" attrs="{'invisible': [('parent_id','!=',False)]}"/>
                                    <field name="ref" string="Reference"/>
                                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" attrs="{'readonly': [('parent_id', '!=', False)]}" force_save="1"/>
                                    <field name="industry_id" attrs="{'invisible': [('is_company', '=', False)]}" options="{'no_create': True}"/>
                                </group>
                            </group>
                        </page>
                        <page name="internal_notes" string="Ghi chú nội bộ">
                            <field name="comment" placeholder="Ghi chú nội bộ..."/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="th_view_partner_form_inherit1" model="ir.ui.view">
        <field name="name">th_view_partner_form_inherit1</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <button class="oe_stat_button o_res_partner_tip_opp" type="object"
                    name="action_view_apm_lead"
                    icon="fa-star"
                    context="{'default_partner_id': active_id}">
                    <field string="Cơ hội APM" name="th_lead_apm_count" widget="statinfo"/>

                </button>
            </xpath>
            <xpath expr="//page[@name='contact_addresses']" position="before">

                <page string="Đặc điểm" autofocus="autofocus" attrs="{'invisible': [('th_check_module_apm', '!=', True)]}">
                    <field name="th_check_module_apm" invisible="1"/>
                    <field name="th_apm_contact_trait_ids" >
                        <tree editable="bottom" no_open="1">
<!--                            <field name="th_product_line_id" options="{'no_edit': True, 'no_create': True, 'no_open': True}"/>-->
                            <field name="th_origin_id" options="{'no_edit': True, 'no_create': True, 'no_open': True}" domain="[('th_module_ids.name', 'in', ['APM'])]"/>
                            <field name="th_apm_trait_id" domain="th_trait_domain" options="{'no_edit': True, 'no_create': True, 'no_open': True}"/>
                            <field name="th_apm_trait_value_ids" widget="many2many_tags" domain="th_trait_value_domain" options="{'no_edit': True, 'no_create': True, 'no_open': True}"/>
                            <field name="th_trait_domain" invisible="1"/>
                            <field name="th_trait_value_domain" invisible="1"/>
                        </tree>
                    </field>
                </page>
            </xpath>
            <xpath expr="//page[@name='contact_addresses']" position="attributes">
                <attribute name="autofocus">False</attribute>
            </xpath>
        </field>
    </record>

    <record id="th_view_res_partner_filter" model="ir.ui.view">
        <field name="name">th_view_res_partner_filter</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_res_partner_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='inactive']" position="before">
                <filter string="Liên hệ tiềm năng" name="th_potential_customers_groupby"
                        domain="['|', ('th_module_ids.name', '=', 'APM'), ('th_apm_contact_trait_ids', '!=', False)]"/>
            </xpath>
        </field>
    </record>

    <record id="th_action_partner_form" model="ir.actions.act_window">
        <field name="name">Liên hệ</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.partner</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_th_potential_customers_groupby': 1, 'apm_contact': True, 'create':0}</field>
    </record>

    <record id="th_apm_customers_after_sale" model="ir.actions.act_window">
        <field name="name">Liên hệ sau bán</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.partner</field>
        <field name="view_mode">tree</field>
        <field name="domain">[('th_is_order_apm', '=', True)]</field>
        <field name="context">{'apm_contact': True, 'create': 0, 'edit': 0, 'delete':0}</field>
        <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('th_view_partner_tree')})]"/>

    </record>
</odoo>
