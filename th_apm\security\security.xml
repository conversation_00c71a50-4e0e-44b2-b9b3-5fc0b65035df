<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="group_apm_manager" model="res.groups">
        <field name="name">APM: Quản lý kho</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="module_category_apm_management" model="ir.module.category">
        <field name="name">Quản lý cơ hội</field>
        <field name="description"><PERSON>ân quyền quản lý APM</field>
        <field name="sequence">20</field>
    </record>

    <record id="module_category_apm" model="ir.module.category">
        <field name="parent_id" ref="module_category_apm_management"/>
        <field name="name">APM</field>
        <field name="sequence">1</field>
    </record>

    <record id="group_apm_user" model="res.groups">
        <field name="name">Nhân viên</field>
        <field name="category_id" ref="module_category_apm"/>
        <field name="implied_ids" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
    </record>

    <record id="group_apm_after_order" model="res.groups">
        <field name="name">APM: Chăm sóc sau bán</field>
        <field name="category_id" ref="base.module_category_hidden"/>
        <field name="implied_ids" eval="[(4, ref('sales_team.group_sale_salesman'),ref('th_apm.group_apm_user'))]"/>
    </record>

    <record id="group_leader_apm_after_order" model="res.groups">
        <field name="name">APM: QL Chăm sóc sau bán</field>
        <field name="category_id" ref="base.module_category_hidden"/>
        <field name="implied_ids" eval="[(4, ref('group_apm_after_order'))]"/>
    </record>

    <record id="group_apm_leader" model="res.groups">
        <field name="name">Quản lý</field>
        <field name="category_id" ref="module_category_apm"/>
        <field name="implied_ids" eval="[(4, ref('group_apm_user')), (3, ref('group_leader_apm_after_order'))]"/>
    </record>

    <record id="group_apm_administrator" model="res.groups">
        <field name="name">Quản trị viên</field>
        <field name="category_id" ref="module_category_apm"/>
        <field name="implied_ids" eval="[(4, ref('group_apm_leader'))]"/>
    </record>

    <record id="apm_lead_group_user_rule" model="ir.rule">
        <field name="name">apm_lead_group_user_rule</field>
        <field name="model_id" ref="model_th_apm"/>
        <field name="groups" eval="[(4, ref('group_apm_user'))]"/>
        <field name="domain_force">['|' ,('th_user_id', '=', user.id),
            ('create_uid', '=', user.id),]
        </field>
    </record>

<!--    <record id="apm_lead_group_TTVH_rule" model="ir.rule">-->
<!--        <field name="name">apm_lead_group_TTVH_rule</field>-->
<!--        <field name="model_id" ref="model_th_apm"/>-->
<!--        <field name="groups" eval="[(4, ref('group_apm_after_order'))]"/>-->
<!--        <field name="domain_force">[(1,'=',1)]</field>-->
<!--    </record>-->

    <record id="apm_lead_group_user_rule_sale_order" model="ir.rule">
        <field name="name">apm_lead_group_user_rule_sale_order</field>
        <field name="model_id" ref="model_sale_order"/>
        <field name="groups" eval="[(4, ref('group_apm_user'))]"/>
        <field name="domain_force">['|', '|', '|', '|', '|', '|', '|','|',
            ('create_uid.th_manager_apm_ids', 'in', user.ids),
            ('create_uid.partner_id.th_apm_team_id.th_member_ids', 'in', user.ids),
            ('create_uid', '=', user.id), ('user_id.th_manager_apm_ids', 'in', user.ids),('th_apm_id.create_uid', '=', user.id),
            ('th_apm_id.create_uid.th_manager_apm_ids', '=', user.id),('th_apm_id.th_user_id', '=', user.id),
            ('th_apm_id.th_user_id.th_manager_apm_ids', '=', user.id),('th_apm_id.th_campaign_id.is_campaign_auto', '=', True)]
        </field>
    </record>

    <record id="apm_lead_group_leader_rule_sale_order" model="ir.rule">
        <field name="name">apm_lead_group_leader_rule_sale_order</field>
        <field name="model_id" ref="model_sale_order"/>
        <field name="groups" eval="[(4, ref('group_apm_leader'))]"/>
        <field name="domain_force">['|', '|', '|',
            ('create_uid.th_manager_apm_ids', 'in', user.ids),
            ('create_uid.partner_id.th_apm_team_id.th_member_ids', 'in', user.ids),
            ('create_uid', '=', user.id), ('user_id.th_manager_apm_ids', 'in', user.ids)]
        </field>
    </record>

    <record id="apm_lead_group_lead_rule" model="ir.rule">
        <field name="name">apm_lead_group_lead_rule</field>
        <field name="model_id" ref="model_th_apm"/>
        <field name="groups" eval="[(4, ref('group_apm_leader'))]"/>
        <field name="domain_force">['|', '|',
            ('th_user_id.th_manager_apm_ids', 'in', user.ids),
            ('create_uid.th_manager_apm_ids', 'in', user.ids),
            ('create_uid', '=', user.id)]
        </field>
    </record>

    <record id="apm_opportunity_leader_rule" model="ir.rule">
        <field name="name">apm_opportunity_leader_rule</field>
        <field name="model_id" ref="model_th_apm"/>
        <field name="groups" eval="[(4, ref('group_leader_apm_after_order'))]"/>
        <field name="domain_force">[ ('th_after_sales_care', '=', True),]
        </field>
    </record>
    <record id="apm_sale_order_leader_rule" model="ir.rule">
        <field name="name">apm_opportunity_leader_rule</field>
        <field name="model_id" ref="model_sale_order"/>
        <field name="groups" eval="[(4, ref('group_leader_apm_after_order'))]"/>
        <field name="domain_force">[ ('th_apm_id.th_after_sales_care', '=', True),]
        </field>
    </record>

    <record id="apm_after_order_opportunity_rule" model="ir.rule">
        <field name="name">apm_after_order_opportunity_rule</field>
        <field name="model_id" ref="model_th_apm"/>
        <field name="groups" eval="[(4, ref('group_apm_after_order'))]"/>
        <field name="domain_force">['|',
            ('create_uid', '=', user.id),
             ('create_uid.th_manager_apm_ids', 'in', user.ids),]

        </field>
    </record>

    <record id="apm_group_partner_manager" model="res.groups">
        <field name="name">APM: Theo dõi cơ hội của đối tác</field>
        <field name="category_id" ref="base.module_category_hidden"/>
        <field name="implied_ids" eval="[(4, ref('group_apm_leader'))]"/>
    </record>

    <record id="apm_group_partner_manager_rule" model="ir.rule">
        <field name="name">APM: Được phép xem cơ hội thuộc sở hữu đối tác</field>
        <field name="model_id" ref="model_th_apm"/>
        <field name="groups" eval="[(4, ref('apm_group_partner_manager'))]"/>
        <field name="domain_force">[('th_ownership_unit_id.th_type', '=', 'other')]</field>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_create" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    <record id="apm_group_partner_manager_rule_sale_order" model="ir.rule">
        <field name="name">APM: Được phép xem đơn hàng thuộc sở hữu đối tác</field>
        <field name="model_id" ref="model_sale_order"/>
        <field name="groups" eval="[(4, ref('apm_group_partner_manager'))]"/>
        <field name="domain_force">[('th_ownership_unit_id.th_type', '=', 'other')]</field>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_create" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

    <record id="apm_lead_group_admin_rule" model="ir.rule">
        <field name="name">apm_lead_group_admin_rule</field>
        <field name="model_id" ref="model_th_apm"/>
        <field name="groups" eval="[(4, ref('group_apm_administrator'))]"/>
    </record>

    <record id="apm_group_self_latchingg_rule" model="ir.rule">
        <field name="name">APM: Được phép xem cơ hội tự chốt</field>
        <field name="model_id" ref="model_th_apm"/>
        <field name="groups" eval="[(4, ref('group_apm_user'))]"/>
        <field name="domain_force">[('th_campaign_id.is_campaign_auto','=', True),('th_user_id', '=', False)]</field>
        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="0"/>
        <field name="perm_create" eval="0"/>
        <field name="perm_unlink" eval="0"/>
    </record>

<!--    <record id="apm_sale_order_group_apm_after_order_rule" model="ir.rule">-->
<!--        <field name="name">APM Chăm sóc sau bán</field>-->
<!--        <field name="model_id" ref="model_sale_order"/>-->
<!--        <field name="groups" eval="[(4, ref('group_apm_after_order'))]"/>-->
<!--    </record>-->

    <record id="apm_sale_order_line_group_apm_user" model="ir.rule">
        <field name="name">apm_sale_order_line_group_apm_user</field>
        <field name="model_id" ref="model_sale_order_line"/>
        <field name="groups" eval="[(4, ref('group_apm_user'))]"/>
        <field name="domain_force">['|', '|', '|', '|', '|','|',
            ('order_id.create_uid.th_manager_apm_ids', 'in', user.ids),
            ('order_id.create_uid.partner_id.th_apm_team_id.th_member_ids', 'in', user.ids),
            ('order_id.create_uid', '=', user.id), ('order_id.user_id.th_manager_apm_ids', 'in', user.ids),('order_id.th_apm_id.create_uid', '=', user.id),
            ('order_id.th_apm_id.create_uid.th_manager_apm_ids', '=', user.id),('order_id.th_apm_id.th_campaign_id.is_campaign_auto', '=', True)]
        </field>
    </record>

    <!--    Phân quyền menu trạng thái kích hoạt tài khoản-->
    <record id="apm_active_account_group_user_rule" model="ir.rule">
        <field name="name">apm_active_account_group_user_rule</field>
        <field name="model_id" ref="model_th_apm_active_account"/>
        <field name="groups" eval="[(4, ref('group_apm_user'))]"/>
        <field name="domain_force">['|' ,('th_sale_order_id.user_id', '=', user.id),('th_sale_order_id.create_uid', '=', user.id)]
        </field>
    </record>

    <record id="apm_active_account_group_leader_rule" model="ir.rule">
        <field name="name">apm_active_account_group_leader_rule</field>
        <field name="model_id" ref="model_th_apm_active_account"/>
        <field name="groups" eval="[(4, ref('group_apm_leader'))]"/>
        <field name="domain_force">['|', '|', '|',
            ('th_sale_order_id.create_uid.th_manager_apm_ids', 'in', user.ids),
            ('th_sale_order_id.create_uid.partner_id.th_apm_team_id.th_member_ids', 'in', user.ids),
            ('th_sale_order_id.create_uid', '=', user.id),('th_sale_order_id.user_id.th_manager_apm_ids', 'in', user.ids)]
        </field>
    </record>

    <record id="apm_active_account_group_admin_rule" model="ir.rule">
        <field name="name">apm_active_account_group_admin_rule</field>
        <field name="model_id" ref="model_th_apm_active_account"/>
        <field name="groups" eval="[(4, ref('group_apm_administrator'))]"/>
        <field name="domain_force">[]
        </field>
    </record>
</odoo>