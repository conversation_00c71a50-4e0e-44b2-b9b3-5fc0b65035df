# -*- coding: utf-8 -*-
{
    'name': "ABS APM",
    'summary': """ABS APM""",
    'category': 'AUM Business System/ APM',
    'description': """Functional group""",
    'author': "ABS",
    'website': "http://www.yourcompany.com",
    'version': '16.0.280325',
    'application': True,
    'depends': [
        'base',
        'mail',
        'th_contact',
        'product',
        'sale_management',
        'th_setup_parameters',
        'account',
        'web_domain_field',
        'th_select_module',
        'data_merge',
        'account_accountant',
        'sale_loyalty',
        'form_readonly',
        'queue_job',
    ],
    'data': [

        'data/th_data_module.xml',
        'data/ir_cron_data.xml',
        'data/th_data_campaign.xml',
        'data/th_data_call_category.xml',
        'security/security.xml',
        'security/ir.model.access.csv',
        'report/th_campaign_email_template.xml',
        'report/sample_import_student_status.xml',
        'wizard/apm_assign_leads_view_wizard.xml',
        'wizard/th_refund_invoice_apm.xml',
        'wizard/check_interact_views_wizard.xml',
        'wizard/import_student_status_wizard.xml',
        'wizard/th_apm_duplicate_complaint_wizard_view.xml',
        'views/th_apm_view.xml',
        'views/data_merge_record_views.xml',
        'views/th_apm_team_view.xml',
        'views/th_apm_level_view.xml',
        'views/th_apm_need_view.xml',
        'views/th_apm_reason_view.xml',
        'views/th_apm_status_detail.xml',
        'views/th_apm_trait.xml',
        'views/res_partner.xml',
        'views/res_config_setting_view.xml',
        'views/th_apm_campaign.xml',
        'views/th_apm_sale_order.xml',
        'views/th_apm_trait_value_view.xml',
        'views/res_partner_view.xml',
        'views/th_apm_invoice_view.xml',
        'views/view_mail_message.xml',
        'views/th_apm_product_template_views.xml',
        'views/th_apm_re_sign.xml',
        'views/th_apm_processing_status.xml',
        'views/th_apm_active_account_view.xml',
        'views/loyalty_program_view.xml',
        'views/th_apm_dividing_ring_view.xml',
        'views/th_cohort_view.xml',
        'views/th_formio_builder_field_default_view.xml',
        'views/th_apm_check_condition_view.xml',
        'views/th_apm_duplicate_views.xml',
        'views/th_apm_complaint_duplicate_view.xml',
        'views/th_apm_origin_view.xml',
        'views/apm_menu.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'th_apm/static/src/**/*',
        ],
    },

    # 'assets': {
    #     'web.assets_backend': [
    #         # 'th_apm/static/src/xml/view_button_apm_approve.xml',
    #         # 'th_apm/static/src/js/list_button_approve.js',
    #     ],
    # },
    'license': 'LGPL-3',
}
