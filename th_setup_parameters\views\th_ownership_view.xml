<odoo>
    <record id="th_ownership_view_tree" model="ir.ui.view">
        <field name="name">th_ownership_view_tree</field>
        <field name="model">th.ownership.unit</field>
        <field name="priority">5</field>
        <field name="arch" type="xml">
            <tree string="" editable="bottom">
                <field name="name"/>
                <field name="th_type"/>
                <field name="th_code"/>
                <field name="th_partner_id"/>
                <field name="th_description"/>
                <field name="th_is_sync"/>
            </tree>
        </field>
    </record>
    
    <record id="th_ownership_view_kanban_mobile" model="ir.ui.view">
        <field name="name">th_ownership_view_kanban_mobile</field>
        <field name="model">th.ownership.unit</field>
        <field name="priority">15</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <field name="name"/>
                <field name="th_description"/>
                <field name="color"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''} oe_kanban_global_click">
                            <span class="oe_kanban_color_help"
                                  t-attf-title="In #{kanban_getcolorname(record.color.raw_value)}" role="img"
                                  t-attf-aria-label="In #{kanban_getcolorname(record.color.raw_value)}"/>
                            <div t-attf-class="o_kanban_card_header">
                                <div class="o_kanban_card_header_title">
                                    <div class="o_kanban_record_title oe_kanban_details">
                                        <strong><h4><field name="name"/></h4></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="th_ownership_view_act" model="ir.actions.act_window">
        <field name="name">Đơn vị sở hữu</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.ownership.unit</field>
        <field name="view_mode">tree</field>
    </record>

</odoo>