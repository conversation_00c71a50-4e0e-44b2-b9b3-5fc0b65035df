from odoo import fields, models, api, exceptions, _
import json


class ThFormioBuilderFieldFefault(models.Model):
    _inherit = 'th.formio.builder.field.aff.default'
    _description = ''

    th_apm_team_ids = fields.Many2many(comodel_name='th.apm.team', string='Đội chăm sóc APM', copy=True)
    th_user_id_domain = fields.Char(compute='_compute_th_user_id_domain', default='[]')
    th_apm_dividing_ring_id = fields.Many2one(comodel_name="th.apm.dividing.ring", domain="[('th_is_opportunity_dividing', '=', True)]", string="Vòng chia")


    def write(self, values):
        res = super(ThFormioBuilderFieldFefault, self).write(values)
        for rec in self:
            if values.get('th_apm_team_ids'):
                team_ids = rec.th_apm_team_ids.ids
                team_ids.sort()
                th_flag = json.loads(rec.th_flag)
                team_id = th_flag[1]
                if not team_id:
                    return res
                if team_id in team_ids:
                    rec.th_flag = json.dumps([team_ids.index(team_id), team_id])
                    return res
                if len(team_ids) == 0 or max(team_ids) < team_id:
                    flag = 0
                    rec.th_flag = json.dumps([flag, False])
                    return res
                for index, team in enumerate(team_ids):
                    if team > team_id:
                        rec.th_flag = json.dumps([index, team])
                        return res

        return res

    def action_assign_leads_apm(self):
        for rec in self:
            if rec.th_apm_team_ids:
                team_ids = rec.th_apm_team_ids.ids
                team_ids.sort()
                th_flag = json.loads(rec.th_flag)
                flag = th_flag[0]
                result = team_ids[flag]
                flag = (flag + 1) % len(team_ids)
                rec.sudo().th_flag = json.dumps([flag, team_ids[flag]])
                return result


    @api.depends('th_apm_team_ids')
    def _compute_th_user_id_domain(self):
        for rec in self:
            domain = []
            if rec.th_apm_team_ids and rec.th_caregiver_ids.ids not in rec.th_apm_team_ids.th_member_ids.ids + rec.th_apm_team_ids.manager_id.ids:
                # self.th_caregiver_ids = False
                user_ids = self.env['th.apm.team'].search([('id', '=', rec.th_apm_team_ids.ids)]).th_member_ids.ids
                domain.append(('id', 'in', user_ids))
                rec.th_user_id_domain = json.dumps(domain)
            elif rec.th_apm_team_ids and rec.th_caregiver_ids.id in rec.th_apm_team_ids.th_member_ids.ids:
                user_ids = self.env['th.apm.team'].search([('id', '=', rec.th_apm_team_ids.id)]).th_member_ids.ids
                domain.append(('id', 'in', user_ids))
                rec.th_user_id_domain = json.dumps(domain)
            else:
                rec.th_user_id_domain = json.dumps(domain)

