<odoo>
    <record id="th_ir_cron_sync_warehouse" model="ir.cron">
        <field name="name">Đ<PERSON><PERSON> bộ xuất xứ</field>
        <field name="model_id" ref="model_th_origin"/>
        <field name="state">code</field>
        <field name="code">model.search([]).update_th_warehouse([], state='write')</field>
        <field name="nextcall" eval="DateTime.now().strftime('%Y-%m-%d 16:00:00')"/>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False"/>
    </record>

<!--    <record id="th_ir_cron_sync_message" model="ir.cron">-->
<!--        <field name="name">Đ<PERSON>ng bộ log note 1/1 ngày</field>-->
<!--        <field name="model_id" ref="model_mail_message"/>-->
<!--        <field name="state">code</field>-->
<!--        <field name="code">model.env['mail.message'].th_sync_message(create=True)</field>-->
<!--        <field name="nextcall" eval="DateTime.now().strftime('%Y-%m-%d 16:00:00')"/>-->
<!--        <field name="interval_number">1</field>-->
<!--        <field name="interval_type">days</field>-->
<!--        <field name="numbercall">-1</field>-->
<!--        <field name="doall" eval="False"/>-->
<!--    </record>-->

    <record id="th_schedule_update_customer_code" model="ir.cron">
        <field name="name">schedule lưu mã KH của liên hệ vào các bảng cơ hội và kế toán</field>
        <field name="model_id" ref="base.model_res_partner"/>
        <field name="state">code</field>
        <field name="code">
            model.th_update_customer_code_in_records(th_models=["crm.lead"
            ,"th.apm"
            ,"th.student"
            ,"prm.lead"
            ,"pom.lead"
            ,"sale.order"
            ,"account.move"
            ,"th.lecturer.profile"])
        </field>
        <field name="nextcall" eval="(DateTime.now().replace(hour=16, minute=0, second=0)).strftime('%Y-%m-%d %H:%M:%S')"/>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="active">True</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False"/>
    </record>
</odoo>

