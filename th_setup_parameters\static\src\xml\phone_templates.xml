<t t-name="th_setup_parameters.FormPhoneFieldAddon" t-inherit="web.FormPhoneField" t-inherit-mode="extension">
    <xpath expr="//t[@t-if='props.readonly']" position="inside">
        <t t-log="state"/>
        <t t-log="phoneSettingsState"/>
        <a
            t-if="props.value and phoneSettingsState.enableCallWidget or props.value and state.enableWidgetPhone "
            t-att-href="'tel:'+props.value"
            class="o_phone_form_link ms-3 d-inline-flex align-items-center"
        >
            <i class="fa fa-phone"></i><small class="fw-bold ms-1">Gọi1</small>
        </a>
<!--        <a-->
<!--            t-if="props.value and !phoneSettingsState.enableCallWidget"-->
<!--            t-att-href="'tel:'+props.value"-->
<!--            class="o_phone_form_link ms-3 d-inline-flex align-items-center"-->
<!--        >-->
<!--            <i class="fa fa-phone"></i><small class="fw-bold ms-1">Gọi2</small>-->
<!--        </a>-->
    </xpath>
    <xpath expr="//input/following-sibling::a[contains(@class, 'o_phone_form_link')]" position="attributes">
        <attribute name="t-if">props.value and state.enableWidgetPhone</attribute>
    </xpath>
</t>