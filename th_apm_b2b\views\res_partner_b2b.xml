<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
       <record id="res_partner_view_b2b_form" model="ir.ui.view">
        <field name="name">res.partner.view.form.b2b.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="product.view_partner_property_form"/>
        <field name="priority">10</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='property_product_pricelist']" position="attributes">
                 <attribute name="options">{'no_create_edit': True, 'no_create':True, 'no_open':True}</attribute>
            </xpath>
        </field>
    </record>
        <record id="res_partner_view_b2b_form_view" model="ir.ui.view">
        <field name="name">res.partner.view.form.b2b.view.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="th_apm.th_view_partner_form_inherit1"/>
        <field name="priority">10</field>
        <field name="arch" type="xml">

            <xpath expr="//tree" position="attributes">
                 <attribute name="no_open">1</attribute>
            </xpath>
        </field>
    </record>
    </data>
</odoo>