from odoo import models, fields, api

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    th_check_partner = fields.<PERSON><PERSON><PERSON>(string="Kiể<PERSON> tra khi tạo liên hệ")
    th_check_partner_module = fields.Bo<PERSON>an(string="Kiểm tra theo module")
    th_pass_user_ids = fields.Many2many('res.users', string="Chọn người dùng cần check", relation='th_pass_user_res_users_rel', default=[])
    th_enable_widget_phone = fields.<PERSON><PERSON><PERSON>(string="Hiển thị widget phone")

    def set_values(self):
        super().set_values()
        self.env['ir.config_parameter'].set_param('th_check_partner', self.th_check_partner)
        self.env['ir.config_parameter'].set_param('th_check_partner_module', self.th_check_partner_module)
        self.env['ir.config_parameter'].set_param('th_pass_user_ids', self.th_pass_user_ids.ids)
        self.env['ir.config_parameter'].set_param('th_enable_widget_phone', self.th_enable_widget_phone)

    @api.model
    def get_values(self):
        res = super().get_values()
        res.update(th_check_partner = self.env['ir.config_parameter'].sudo().get_param('th_check_partner'))
        res.update(th_check_partner_module = self.env['ir.config_parameter'].sudo().get_param('th_check_partner_module'))
        res.update(th_enable_widget_phone = self.env['ir.config_parameter'].sudo().get_param('th_enable_widget_phone'))
        users = self.env['ir.config_parameter'].sudo().get_param('th_pass_user_ids','[]')
        res.update({
            'th_pass_user_ids': [(6,0,eval(users))] if users else []
        })
        return res