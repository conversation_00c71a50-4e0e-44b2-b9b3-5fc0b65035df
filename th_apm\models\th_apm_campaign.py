import json

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.http import request

READONLY_STATES = {
    'approve': [('readonly', True)],
    'completed': [('readonly', True)],
    'refuse': [('readonly', True)],
}

ORIGIN_LINE = ['th_setup_parameters.th_origin_vmc', 'th_setup_parameters.th_origin_vstep', ]


class ThApmCampaign(models.Model):
    _name = 'th.apm.campaign'
    _description = 'Bảng chiến dịch'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    # def _domain_th_origin(self):
    #     rec = self.env.ref('th_setup_parameters.th_apm_module')
    #     return [('th_module_ids', 'in', rec.ids)]
    active = fields.Boolean('Active', default=True)
    name = fields.Char(string="Tên chiến dịch", tracking=True)
    state = fields.Selection(
        selection=[('draft', 'Nháp'),
                   ('pending', '<PERSON><PERSON> duyệt'),
                   ('approve', '<PERSON><PERSON> chạy'),
                   ('completed', '<PERSON>àn thành'),
                   ('refuse', 'Từ chối'), ],
        string="Trạng thái",
        default='draft', tracking=True
    )
    # th_product_line_id = fields.Many2one('th.apm.product.line', string="Dòng sản phẩm", tracking=True, states=READONLY_STATES)
    th_origin_id = fields.Many2one('th.origin', string="Dòng sản phẩm", tracking=True, states=READONLY_STATES)
    # th_contact_filter_condition = fields.Text(string="Điều kiện lọc liên hệ", tracking=True)
    th_apm_team_ids = fields.Many2many('th.apm.team', string="Đội tham gia", tracking=True, readonly=False)
    th_divide = fields.Selection(
        [('none', 'Không chia'),
         ('automatic', 'Tự động'),
         ('handmade', 'Thủ công'),
         ],
        string="Cách chia",default='none', tracking=True, states=READONLY_STATES
    )
    qty = fields.Integer(string="Số lượng cơ hội cần tạo", tracking=True, help="Nếu không điền thông tin trường này thì sẽ tự động lấy tất cả các liên hệ thỏa mãn điều kiện, còn nếu điền thì sẽ chỉ lấy theo số lượng yêu cầu", states=READONLY_STATES)
    # th_potential_customers = fields.Many2many('res.partner', string="Những khách hàng tiềm năng",
    #                                           domain="['|', ('th_contact_type_ids.name', '!=', 'Khách hàng tiềm năng'), ('th_contact_type_ids', '!=', False)]",tracking=True)
    th_apm_trait_ids = fields.Many2many('th.apm.trait', string="Đặc điểm", tracking=True, states=READONLY_STATES)
    th_apm_trait_value_ids = fields.Many2many('th.apm.trait.value', string="Giá trị đặc điểm", tracking=True, store=True, states=READONLY_STATES)
    th_trait_domain = fields.Char(compute='_compute_th_trait_domain')
    th_trait_value_domain = fields.Char(compute='_compute_th_trait_value_domain')
    th_number_contact = fields.Integer('Số liên hệ tìm được', compute="_compute_th_number_contact", store=True,)
    th_product_ids = fields.Many2many(comodel_name="product.product", string="Sản phẩm", states=READONLY_STATES)
    th_product_category_id = fields.Many2one(comodel_name="product.category", string="Nhóm sản phẩm", states=READONLY_STATES)
    th_product_domain = fields.Char(compute='_compute_th_product_domain')
    th_product_category_domain = fields.Char(compute='_compute_th_trait_domain')
    th_apm_lead_count = fields.Integer(compute="_compute_th_apm_lead_count")
    th_system_campaign = fields.Boolean(string="Là chiến dịch hệ thống", default=False)
    th_check_group = fields.Boolean(string="Là chiến dịch hệ thống", default=False)
    is_campaign_auto = fields.Boolean("Tất cả nhân viên được xem")
    is_no_lead = fields.Boolean('Không tạo cơ hội',states=READONLY_STATES)
    th_apm_divide_ring_ids = fields.Many2many('th.apm.dividing.ring', string="Vòng chia")
    th_apm_divide_ring_domain_ids = fields.Many2many(comodel_name='th.apm.dividing.ring', string="Vòng chia theo miền", compute='_compute_th_apm_dividing_ring_domain')
    @api.model
    def create(self, vals_list):
        res = super(ThApmCampaign, self).create(vals_list)
        if self.env.user.has_group('th_apm.group_leader_apm_after_order'):
            res.th_check_group = True

        return res

    @api.onchange('th_divide')
    def _onchange_th_divide(self):
        if self.th_divide == 'none':
            self.th_apm_team_ids = False
            self.qty = 0
            self.is_no_lead = True

    @api.onchange('is_no_lead')
    def onchange_is_no_lead(self):
        for rec in self:
            if rec.is_no_lead == True:
                rec.qty = 0

    @api.onchange('th_origin_id')
    def onchange_th_origin_id(self):
        if self.th_origin_id and self.th_product_category_id and self.th_origin_id.id not in self.th_product_category_id.th_origin_ids.ids:
            self.th_product_category_id = False

    @api.onchange('th_product_category_id')
    def onchange_th_product_category_id(self):
        if self.th_product_category_id and self.th_product_ids and self.th_product_ids.mapped('categ_id') != self.th_product_category_id:
            self.th_product_ids = False

    def _compute_th_apm_lead_count(self):
        for rec in self:
            rec.th_apm_lead_count = len(self.env['th.apm'].search([('th_campaign_id', '=', rec.id)]))

    def action_open_apm_lead(self):
        action = self.env['ir.actions.act_window']._for_xml_id('th_apm.apm_lead_view_act')
        action['domain'] = [('id', 'in', self.env['th.apm'].search([('th_campaign_id', '=', self.id)]).ids)]
        action['context'] = {'create': 0}
        return action

    def get_current_record_url(self):
        url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
        url += '/web?db=%s#id=%s&view_type=form&model=th.apm.campaign' % (request.env.cr.dbname, self.id)
        return url

    def action_send_for_approval(self):
        if self.is_no_lead == True:
            self.state = 'approve'
        else:
            self.state = 'pending'
            # if not self.th_number_contact:
            #     raise ValidationError('Không tìm thấy liên hệ phù hợp với chiến dịch!')
            # if self.qty == 0:
            #     raise ValidationError('Vui lòng điền số lượng cơ hội cần tạo khác 0!')
            # if len(self.th_apm_team_ids.mapped('th_member_ids')) <= 0 and self.th_divide == 'automatic':
            #     raise ValidationError('Đội nhóm được chọn không có thành viên!')
            url = self.get_current_record_url()
            template_id = self.env.ref('th_apm.th_apm_mail_template').id
            template = self.env['mail.template'].browse(template_id)
            th_manager_ids = self.env['ir.config_parameter'].sudo().get_param('th_manager_ids')
            th_manager_ids = eval(th_manager_ids or '[]')
            users = self.env['res.users'].browse(th_manager_ids)
            if not users:
                raise ValidationError(_("Chưa có 'Quản lý kho liên hệ tiềm năng'. Vui lòng liên hệ với Admin!"))
            for user in users:
                ctx = {
                    'email_from': self.env.user.partner_id.email if self.env.user.partner_id.email else '<EMAIL>',
                    'email_to': user.partner_id.email,
                    'receive_name': user.partner_id.name,
                    'url': url
                }
                template.with_context(ctx).send_mail(self.id, force_send=True)
        return True

    def action_approved(self):
        th_contact_type_id = self.env.ref('th_setup_parameters.th_apm_module')
        all_partner = self.env['res.partner'].sudo().search(self.th_action_check_users(self)) \
            if self.env['res.partner'].sudo().search(self.th_action_check_users(self)) \
            else self.env['res.partner'].sudo().search([('th_module_ids', 'in', th_contact_type_id.ids)])
        exist_partner = self.env['th.apm'].search([('th_partner_id', 'in', all_partner.ids), ('th_origin_id', '=', self.th_origin_id.id), ('th_reason', '=', False), ('th_order_id', '=', False)]).th_partner_id.ids
        res_partner = self.env['res.partner'].search([('id', 'in', all_partner.ids), ('id', 'not in', exist_partner)], limit=int(self.qty))
        if self.qty==0:
            res_partner = self.env['res.partner']
        data = res_partner.ids
        if self.th_number_contact == 0 and self.qty > 0 or self.th_number_contact < self.qty:
            raise ValidationError("Số lượng cần tạo không được lớn hơn số lượng liên hệ tìm đuợc")
        # if not data:
        #     raise ValidationError("Các liên hệ đều đã có cơ hội!")
        # context = {'id': self.id}
        # if context:
        stt_emp = stt_manager = stt_team = 0
        self.state = 'approve'
        th_managers = self.th_apm_team_ids.mapped('manager_id').ids
        th_team_ids = self.th_apm_team_ids.ids
        list_of_participants = self.th_apm_team_ids.mapped('th_member_ids').ids
        new = 0
        if data:
            for rec in data:
                self.env['th.apm'].sudo().create({
                    'th_partner_id': rec,
                    'th_campaign_id': self.id,
                    'th_user_id': th_managers[stt_manager] if self.th_divide == "handmade" else list_of_participants[stt_emp],
                    'th_apm_team_id': th_team_ids[stt_team] if self.th_divide == "handmade" else self.th_apm_team_ids.search([('th_member_ids', 'in', list_of_participants[stt_emp]), ('id', 'in', self.th_apm_team_ids.ids)], limit=1).id,
                    'th_origin_id': self.th_origin_id.id,
                    'th_product_category_id': self.th_product_category_id.id,
                    'th_product_ids': [(6, 0, self.th_product_ids.ids)],
                })
                new += 1
                if self.th_divide != "handmade":
                    stt_emp = (stt_emp + 1) % len(list_of_participants)
                stt_manager = (stt_manager + 1) % len(th_managers)
                stt_team = (stt_team + 1) % len(th_team_ids) if ((stt_team + 1) % len(th_team_ids)) <= len(th_team_ids)-1 else 0

        return self.th_notifications(new)

    def th_notifications(self, e):
        message = "Thành công: %s cơ hội được tạo " % e
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Success!',
                'message': message,
                'type': 'info',
                'sticky': True,
                'next': {'type': 'ir.actions.act_window_close'}
            }
        }

    def action_completed(self, campaign=None):
        self.state = 'completed'
        opportunity_of_campaign = self.env['th.apm'].search([('th_campaign_id.id', '=', self.id)])
        for rec in opportunity_of_campaign:
            rec.active = False
        data = self.env['th.apm'].run_opportunity_sync(res=True, campaign=self)
        return True, data

    def action_refuse(self):
        self.state = 'refuse'
        return True

    def th_action_check_users(self, data):
            domain = []
            if data.th_origin_id:
                domain.append(('th_apm_contact_trait_ids.th_origin_id', '=', data.th_origin_id.id))

            if data.th_apm_trait_ids:
                domain.append(('th_apm_contact_trait_ids.th_apm_trait_id', 'in', data.th_apm_trait_ids.ids))

            if data.th_apm_trait_value_ids:
                domain.append(('th_apm_contact_trait_ids.th_apm_trait_value_ids', 'in', data.th_apm_trait_value_ids.ids))

            return domain

    @api.depends('th_origin_id', 'th_apm_trait_ids', 'th_apm_trait_value_ids')
    def _compute_th_number_contact(self):
        for rec in self:
            rec.th_number_contact = 0
            if rec.state in ['draft', 'pending'] and rec.th_origin_id:
                th_contact_type_id = self.env.ref('th_setup_parameters.th_apm_module')
                all_partner = self.env['res.partner'].sudo().search(self.th_action_check_users(rec)) \
                    if self.env['res.partner'].sudo().search(self.th_action_check_users(rec)) \
                    else self.env['res.partner'].sudo().search([('th_module_ids', 'in', th_contact_type_id.ids)])
                exist_partner = self.env['th.apm'].search([
                    ('th_partner_id', 'in', all_partner.ids), ('th_origin_id', '=', rec.th_origin_id.id),
                    ('th_reason', '=', False), ('th_order_id', '=', False)]).th_partner_id.ids
                res_partner = self.env['res.partner'].search([
                    ('id', 'in', all_partner.ids), ('id', 'not in', exist_partner)
                ])
                rec.th_number_contact = len(res_partner)


    @api.depends('th_apm_trait_ids')
    def _compute_th_trait_value_domain(self):
        for rec in self:
            domain = []
            if rec.th_apm_trait_ids:
                domain.append(('th_apm_trait_id', 'in', self.th_apm_trait_ids.ids))
                for th_apm_trait in rec.th_apm_trait_value_ids.mapped('th_apm_trait_id').ids:
                    if th_apm_trait not in rec.th_apm_trait_ids.ids:
                        rec.th_apm_trait_value_ids = False
            else:
                rec.th_apm_trait_value_ids = False
            rec.th_trait_value_domain = json.dumps(domain)

    @api.depends('th_origin_id')
    def _compute_th_trait_domain(self):
        for rec in self:
            domain = []
            domain_category = []
            if rec.th_origin_id:
                domain.append(('th_origin_id', 'in', rec.th_origin_id.ids))
                category_ids = rec.env['product.category'].search([]).filtered(lambda lam: self.env.ref('th_setup_parameters.th_apm_module').id in lam.th_module_ids.ids and rec.th_origin_id.id in lam.th_origin_ids.ids).ids
                domain_category.append(('id', 'in', category_ids))
                if rec.th_apm_trait_ids and rec.th_origin_id.id not in rec.th_apm_trait_ids.mapped('th_origin_id').ids:
                    rec.th_apm_trait_value_ids = False
                    rec.th_apm_trait_ids = False
            else:
                rec.th_apm_trait_ids = False
                rec.th_apm_trait_value_ids = False
            rec.th_trait_domain = json.dumps(domain)
            rec.th_product_category_domain = json.dumps(domain_category)

    # @api.onchange('th_product_line_id')
    # def _onchange_th_product_line_id(self):
    #     if self.th_product_line_id:
    #         for rec in self.th_product_line_id:
    #             self.th_product_category_id = rec.th_product_category_id

    @api.depends('th_product_category_id')
    def _compute_th_product_domain(self):
        for rec in self:
            domain = []
            if rec.th_product_category_id:
                domain.append(('categ_id', '=',  rec.th_product_category_id.id))
            else:
                categ_ids = self.env['product.category'].search([]).filtered(
                    lambda lam: self.env.ref('th_setup_parameters.th_apm_module').id in
                                lam.th_module_ids.ids and rec.th_origin_id.id in lam.th_origin_ids.ids).ids
                domain.append(('categ_id', 'in', categ_ids))
                rec.th_product_ids = False
            rec.th_product_domain = json.dumps(domain)

    def unlink(self):
        for rec in self:
            if rec.state in ['approve', 'completed']:
                raise ValidationError('Không thể xóa chiến dịch này')
        return super().unlink()

    def write(self, values):
        for res in self:
            team_ids = res.th_apm_team_ids
        res = super(ThApmCampaign, self).write(values)
        for rec in self:
            if values.get('th_apm_team_ids', False) and rec.state == 'approve':
                after_th_apm_team_ids = values.get('th_apm_team_ids', False)
                for record in team_ids.ids :
                    if record not in after_th_apm_team_ids[0][2]:
                        raise ValidationError('Chỉ có thể thêm đội mới,không thể xóa đội cũ')
        return res
    
    @api.depends('th_origin_id')
    def _compute_th_apm_dividing_ring_domain(self):
        for rec in self:
            if rec.th_origin_id:
                rec.th_apm_divide_ring_domain_ids = self.env['th.apm.dividing.ring'].search(
                    [('th_origin_id', '=', rec.th_origin_id.id), ('th_is_opportunity_dividing', '=', True)])
            else:
                rec.th_apm_divide_ring_domain_ids = self.env['th.apm.dividing.ring'].search(
                    [('th_is_opportunity_dividing', '=', True)])