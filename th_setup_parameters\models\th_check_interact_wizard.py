from odoo import fields, models, api

class ThCheckAllInteractWizard(models.TransientModel):
    _name = "th.check.all.interact.wizard"
    _description = "<PERSON><PERSON><PERSON> tra tương tác của nhân sự"

    th_user_id = fields.Many2one('res.users', string='<PERSON>hân sự')
    th_start_date = fields.Date('<PERSON><PERSON><PERSON>')
    th_end_date = fields.Date('<PERSON><PERSON><PERSON>')
    th_message_ids = fields.One2many(comodel_name="th.all.message.wizard", inverse_name="th_check_interact_id", string="<PERSON><PERSON><PERSON> sử")
    th_number_of_interactive = fields.Integer('Số lượng tương tác')
    th_module = fields.Selection(string="Module", selection=[('crm', 'CRM'), ('srm', 'SRM'), ('apm', 'APM')], default='crm')

class ThAllMessageWizard(models.TransientModel):
    _name = "th.all.message.wizard"
    _description = "<PERSON><PERSON><PERSON> sử "

    name = fields.Char(string="<PERSON><PERSON> hội")
    th_description = fields.Char(string="<PERSON><PERSON> tả")
    th_check_interact_id = fields.Many2one(comodel_name="th.check.all.interact.wizard")
    th_date = fields.Datetime('Ngày')
    th_lognote_creator = fields.Many2one('res.users', string='Người Lognote')

    @api.model_create_multi
    def create(self, vals_list):
        # Add code here
        return super(ThAllMessageWizard, self).create(vals_list)
