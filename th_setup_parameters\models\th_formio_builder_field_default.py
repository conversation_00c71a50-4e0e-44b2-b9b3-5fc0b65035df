from odoo import fields, models, api, exceptions, _
import json
from odoo.exceptions import ValidationError

class ThFormioBuilderFieldFefault(models.Model):
    _name = 'th.formio.builder.field.aff.default'
    _description = 'Mặc định giá trị cho formio'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    th_ownership_unit_id = fields.Many2one(
        comodel_name="th.ownership.unit",
        string="Đơn vị sở hữu", copy=True
    )
    th_caregiver_ids = fields.Many2many(comodel_name="res.users", string="Người chăm sóc", copy=True)
    th_status_category_id = fields.Many2one("th.status.category", string="Tình trạng gọi", copy=True)
    th_flag = fields.Char(string="Cờ")
    th_origin_id = fields.Many2one(comodel_name="th.origin", string="Trường học", copy=True)
    th_uuid = fields.Char(string="UUID", copy=False)
    th_module = fields.Selection(
        selection=[('apm', 'APM'),
                   ('crm', 'CRM'),
                   ('prm', 'PRM')],string="Module", copy=True)

    @api.model
    def create(self, values):
        values['th_flag'] = json.dumps([0, False])
        return super(ThFormioBuilderFieldFefault, self).create(values)

    @api.constrains('th_uuid')
    def _constraint_th_code_origin(self):
        if any(self.search([('id', '!=', rec.id), ('th_uuid', '=', rec.th_uuid)]) for rec in self):
            raise ValidationError(_("Mã UUID đã tồn tại. Vui lòng kiểm tra lại!"))





