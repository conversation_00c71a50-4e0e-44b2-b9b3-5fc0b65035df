import xmlrpc

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

state = [('yes', 'Đã đẩy'), ('no', 'Chưa đẩy'), ('not_pull', 'Đẩy lỗi')]


class ThCountryWard(models.Model):
    _name = 'th.country.ward'
    _description = 'Phường/Xã'

    active = fields.Boolean('Active', default=True)
    name = fields.Char(string='Phường/ Xã', required=True)
    th_ref = fields.Char(string='Mã Phường/ Xã', required=True)
    th_district_id = fields.Many2one(comodel_name='th.country.district', string='Quận/ Huyện', required=True)
    th_c_ward_b2b_id = fields.Integer("Id Phường/Xã B2B", copy=False)

    _sql_constraints = [
        ('name_th_ref_uniq', 'unique(th_district_id, th_ref)', '<PERSON>ã của Phường/Xã không được trùng ở mỗi Quận/Huyện !')
    ]

    # @api.model
    # def create(self, values):
    #     res = super(ThCountryWard, self).create(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in res:
    #             rec.th_action_sync_b2b_c_ward(rec)
    #     return res
    #
    # def write(self, values):
    #     res = super(ThCountryWard, self).write(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in self:
    #             rec.th_action_sync_b2b_c_ward(rec)
    #     return res
    #
    # def unlink(self):
    #     for rec in self:
    #         rec.with_context(is_delete=True).th_action_sync_b2b_c_ward(rec)
    #     res = super(ThCountryWard, self).unlink()
    #     return res
    #
    # def th_action_sync_b2b_c_ward(self, res):
    #     vals = {}
    #     if not res:
    #         return
    #     server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1,
    #                                                   order='id desc')
    #     try:
    #         if not server_api:
    #             raise ValidationError('Không tìm thấy server!')
    #         result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         for rec in res:
    #             if not rec.th_district_id.th_c_district_b2b_id:
    #                 rec.th_district_id.th_action_sync_b2b_c_district(rec.th_district_id)
    #             vals = {'name': rec.name,
    #                     'th_ref': rec.th_ref,
    #                     'th_district_id': rec.th_district_id.th_c_district_b2b_id if rec.th_district_id.th_c_district_b2b_id else False,
    #                     'th_c_ward_samp_id': rec.id}
    #             if not rec.th_c_ward_b2b_id and not self._context.get('is_delete'):
    #                 c_ward = result_apis.execute_kw(db, uid_api, password, 'th.country.ward', 'create', [vals])
    #                 rec.with_context(th_test_import=True).write({'th_c_ward_b2b_id': c_ward})
    #             else:
    #                 if not self._context.get('is_delete'):
    #                     c_ward = result_apis.execute_kw(db, uid_api, password, 'th.country.ward', 'write',
    #                                                     [[rec.th_c_ward_b2b_id], vals])
    #                 else:
    #                     c_ward = result_apis.execute_kw(db, uid_api, password, 'th.country.ward', 'unlink',
    #                                                     [[rec.th_c_ward_b2b_id]])
    #     except Exception as e:
    #         print(e)
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(res.ids),
    #             'th_input_data': str(vals),
    #             'th_function_call': str('th_action_sync_b2b_c_ward'),
    #         })
    #         return