<odoo>
    <record id="th_apm_view_partner_tree" model="ir.ui.view">
        <field name="name">th.apm.res.partner.tree</field>
        <field name="model">res.partner</field>
        <field eval="8" name="priority"/>
        <field name="arch" type="xml">
            <tree string="Contacts" create="0" editable="bottom" delete="1" export_xlsx="0" js_class="apm_approve_list">
                <field name="display_name" string="Name"/>
                <field name="phone" class="o_force_ltr" optional="show"/>
                <field name="email" optional="show"/>
                <field name="th_contact_type_ids" widget="many2many_tags"/>
            </tree>
        </field>
    </record>
</odoo>