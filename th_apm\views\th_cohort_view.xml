<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="th_cohort_tree_view" model="ir.ui.view">
        <field name="name">th_cohort_tree_view</field>
        <field name="model">th.cohort</field>
        <field name="arch" type="xml">
            <tree string="Khóa tuyển sinh">
                <field name="name"/>
                <field name="th_cohort_code"/>
                <field name="th_start_date"/>
<!--                <field name="th_student_limit"/>-->
<!--                <field name="th_current_students" readonly="1"/>-->
            </tree>
        </field>
    </record>
    <record id="th_cohort_form_view" model="ir.ui.view">
    <field name="name">th.cohort.form</field>
    <field name="model">th.cohort</field>
        <field name="arch" type="xml">
            <form string="Khóa tuyển sinh">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="th_cohort_code"/>
                        <field name="th_start_date"/>
                        <field name="th_cohort_mode"/>
<!--                        <field name="th_student_limit"/>-->
                    </group>
                    <notebook>
                        <page string="Danh sách lớp">
                            <field name="th_class_ids">
                                <tree editable="bottom">
                                    <field name="th_product_domain" invisible="1"/>
                                    <field name="th_type"/>
                                    <field name="th_product_ids" widget="many2many_tags"  options="{'no_create': True, 'no_open':True}" domain="th_product_domain"/>
                                    <field name="th_class_code"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="th_cohort_action" model="ir.actions.act_window">
        <field name="name">Khóa tuyển sinh</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.cohort</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>