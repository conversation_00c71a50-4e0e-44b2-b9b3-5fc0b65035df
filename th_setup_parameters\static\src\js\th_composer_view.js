/** @odoo-module **/

import { registerPatch } from '@mail/model/model_core';

const modelList = ["prm.lead","th.apm", "pom.lead", "crm.lead", "ccs.lead","th.student", "th.student.profile"]

registerPatch({
    name: 'ComposerView',
    recordMethods: {
        /**
         * @override
         */
        onClickSend() {
            this._super.apply(this, arguments);

            const webRecord = this.chatter.webRecord;

            if (modelList.includes(webRecord.resModel)) {
                 this.env.services.orm.call(
                     'mail.message',
                     'update_th_last_check',
                     [webRecord.resModel, webRecord.resId]
                 ).then(() => {
                     if (!webRecord.isDirty) {
                         webRecord.model.__bm_load_params__.res_id = webRecord.resId
                         webRecord.model.load();
                     }
                 }).catch(error => {
                     console.error("Error updating th_last_check:", error);
                 });
             }
        },
    },
});

