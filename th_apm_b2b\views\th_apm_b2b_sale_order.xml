<odoo>
    <record id="sale_order_form_inherit_apm_b2b" model="ir.ui.view">
        <field name="name">sale.order.form.inherit.apm.b2b</field>
        <field name="model">sale.order</field>
        <field name="priority">1001</field>
        <field name="inherit_id" ref="th_apm.th_view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="sheet/group/group/field[@name='partner_id']" position="attributes">
                <attribute name="options">{'no_open':1}</attribute>
            </xpath>
            <xpath expr="sheet/group/group/field[@name='th_introducer_id']" position="attributes">
                <attribute name="options">{'no_open':1}</attribute>
            </xpath>
            <xpath expr="sheet/group/group/field[@name='partner_invoice_id']" position="attributes">
                <attribute name="options">{'no_open':1}</attribute>
            </xpath>
            <xpath expr="sheet/group/group/field[@name='partner_shipping_id']" position="attributes">
                <attribute name="options">{'no_open':1}</attribute>
            </xpath>
            <xpath expr="//button[@name='action_cancel']" position="attributes">
                <attribute name="invisible">context.get('th_is_apm_b2b')</attribute>
            </xpath>
            <xpath expr="//button[@name='action_confirm']" position="attributes">
                <attribute name="invisible">context.get('th_is_apm_b2b')</attribute>
            </xpath>
            <xpath expr="//button[@name='action_open_reward_wizard']" position="attributes">
                <attribute name="invisible">context.get('th_is_apm_b2b')</attribute>
            </xpath>
            <xpath expr="//button[@name='%(sale_loyalty.sale_loyalty_coupon_wizard_action)d']" position="attributes">
                <attribute name="invisible">context.get('th_is_apm_b2b')</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']//tree" position="attributes">
                <attribute name="no_open">1</attribute>
            </xpath>
            <xpath expr="//page[@name='order_lines']//field[@name='order_line']//tree//field[@name='product_id']" position="replace">
                <field
                        name="product_id"
                        attrs="{
                                            'readonly': [('product_updatable', '=', False)],
                                            'required': [('display_type', '=', False)],
                                        }"
                        force_save="1"
                        context="{
                                            'partner_id': parent.partner_id,
                                            'quantity': product_uom_qty,
                                            'pricelist': parent.pricelist_id,
                                            'uom': product_uom,
                                            'company_id': parent.company_id,
                                            'default_lst_price': price_unit,
                                            'default_description_sale': name,
                                        }"
                        options="{
                                            'no_open': True, 'no_create': True,
                                        }"
                        domain="th_domain_line"
                        invisible="not context.get('th_is_apm_b2b')"
                />
                <field
                        name="product_id"
                        attrs="{
                                            'readonly': [('product_updatable', '=', False)],
                                            'required': [('display_type', '=', False)],
                                        }"
                        force_save="1"
                        context="{
                                            'partner_id': parent.partner_id,
                                            'quantity': product_uom_qty,
                                            'pricelist': parent.pricelist_id,
                                            'uom': product_uom,
                                            'company_id': parent.company_id,
                                            'default_lst_price': price_unit,
                                            'default_description_sale': name,
                                        }"
                        options="{
                                            'no_open': True, 'no_create': True,
                                        }"
                        domain="th_domain_line"
                        widget="sol_product_many2one"
                        invisible="context.get('th_is_apm_b2b')"
                />
            </xpath>
        </field>
    </record>

    <record id="view_order_tree_apm_b2b" model="ir.ui.view">
        <field name="name">sale.order.tree.apm.b2b</field>
        <field name="model">sale.order</field>
        <field name="priority">2</field>
        <field name="arch" type="xml">
            <tree string="Sales Orders" sample="1"
                decoration-info="invoice_status == 'to invoice'"
                decoration-muted="state == 'cancel'">
                <field name="message_needaction" invisible="1"/>
                <field name="name" string="Mã" readonly="1" decoration-bf="1"/>
                <field name="date_order" string="Ngày đặt hàng" widget="date" optional="show" invisible="1"/>
                <field name="commitment_date" optional="hide"/>
                <field name="expected_date" optional="hide" string="Ngày tạo đơn"/>
                <field name="partner_id" readonly="1" string="Khách hàng"/>
                <field name="th_customer_code" readonly="1" string="Mã khách hàng"/>
                <field name="th_customer_code_aum" readonly="1" invisible="1"/>
                <field name="user_id" optional="show"/>
                <field name="team_id" optional="hide"/>
                <field name="th_introducer_id" optional="show"/>
                <field name="company_id" groups="base.group_multi_company" optional="show" readonly="1"/>
                <field name="amount_untaxed" sum="Total Tax Excluded" widget="monetary" optional="hide"/>
                <field name="amount_tax" sum="Tax Total" widget="monetary" optional="hide"/>
                <field name="amount_total" sum="Total Tax Included" widget="monetary" decoration-bf="1" optional="show"/>
                <field name="currency_id" invisible="1"/>
                <field name="invoice_status"
                    decoration-success="invoice_status == 'invoiced'"
                    decoration-info="invoice_status == 'to invoice'"
                    decoration-warning="invoice_status == 'upselling'"
                    widget="badge" optional="show" invisible="1"/>
                <field name="tag_ids" optional="hide" widget="many2many_tags" options="{'color_field': 'color'}"/>
                <field name="state" invisible="0"/>
                <field name="th_customer_phone" optional="hide"/>
                <field name="th_customer_email" optional="hide"/>
                <field name="th_apm_source_group_id" optional="hide"/>
                <field name="th_channel_id" optional="hide"/>
                <button class="oe_highlight" type="object" name="action_order_details" string="Chi tiết đơn hàng" invisible="context.get('invisible_button', False)"/>
            </tree>
        </field>
    </record>

    <record id="th_apm_b2b_sale_orders_action" model="ir.actions.act_window">
        <field name="name">Đơn hàng</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">sale.order</field>
        <field name="context">{'create':0, 'edit':0, 'invisible_button': True, 'view_apm': True, 'th_is_apm_b2b': True}</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('th_apm_id', '!=', False),('th_apm_id.th_ownership_unit_id.th_type', '=', 'other')]</field>
        <field name="search_view_id" ref="th_apm.th_view_orders_search"/>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_order_tree_apm_b2b')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_apm.th_view_order_form')}),
            ]"/>
    </record>
</odoo>