<odoo>
    <record id="th_attendance_request_view_form" model="ir.ui.view">
        <field name="name">th.attendance.request.view.form</field>
        <field name="model">th.attendance.request</field>
        <field name="arch" type="xml">
            <form string="Attendance Correction Request" duplicate="false">
                <header>
                    <field name="th_is_visible_button" invisible="1"/>
                    <button string="Send to Approver" name="action_send_to_approver" type="object" class="btn-primary" attrs="{'invisible': [('th_status', '!=', 'draft')]}"/>
                    <button string="Confirm" name="action_confirm" type="object" class="btn-primary" attrs="{'invisible': [('th_is_visible_button', '!=', True)]}"/>
                    <button string="Refuse" name="action_refuse_open_view" type="object" class="btn-secondary" attrs="{'invisible': [('th_is_visible_button', '!=', True)]}"/>
                    <button name="action_back_to_draft" string="Back to draft" type="object" class="btn-secondary" attrs="{'invisible': [('th_status', '!=', 'refuse')]}"/>
                    <field name="th_status" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="th_date_req" attrs="{'readonly': [('th_status', 'not in', 'draft')]}"/>
                            <field name="th_attendance_type" attrs="{'readonly': ['|', '|', ('th_status', '=', 'confirm'),('th_status', '=', 'confirm1'),('th_status', '=', 'refuse')]}" required="1"/>
                        </group>
                        <group>
                            <field name="th_employee_id" attrs="{'readonly': [('th_status', 'not in', 'draft')]}"/>
                            <field name="th_type" attrs="{'readonly': [('th_status', '!=', 'draft')], 'invisible': [('th_attendance_type', '=', 'excess_work')], 'required': [('th_attendance_type', '=', 'compensation')]}"/>
                            <field name="th_time" widget="float_time" attrs="{'readonly': [('th_status', 'not in', 'draft')], 'invisible': [('th_attendance_type', '=', 'excess_work')]}"/>
                            <field name="th_reason" attrs="{'invisible': [('th_status', '!=', 'refuse')], 'readonly': [('th_status', '=', 'refuse')]}"/>
                        </group>
                        <group invisible="1">
                            <field name="th_time1" invisible="1"/>
                            <field name="th_time2" invisible="1"/>
                            <field name="th_time3" invisible="1"/>
                            <field name="th_time4" invisible="1"/>
                            <field name="th_time5" invisible="1"/>
                            <field name="th_time6" invisible="1"/>
                            <field name="th_time7" invisible="1"/>
                            <field name="th_time8" invisible="1"/>
                            <table style="width:80%" colspan="2">
                                <tr>
                                    <th attrs="{'invisible': [('th_time1', '=', 0)]}"><field name="th_time1" widget="float_time"/></th>
                                    <th attrs="{'invisible': [('th_time2', '=', 0)]}"><field name="th_time2" widget="float_time"/></th>
                                    <th attrs="{'invisible': [('th_time3', '=', 0)]}"><field name="th_time3" widget="float_time"/></th>
                                    <th attrs="{'invisible': [('th_time4', '=', 0)]}"><field name="th_time4" widget="float_time"/></th>
                                    <th attrs="{'invisible': [('th_time5', '=', 0)]}"><field name="th_time5" widget="float_time"/></th>
                                    <th attrs="{'invisible': [('th_time6', '=', 0)]}"><field name="th_time6" widget="float_time"/></th>
                                    <th attrs="{'invisible': [('th_time7', '=', 0)]}"><field name="th_time7" widget="float_time"/></th>
                                    <th attrs="{'invisible': [('th_time8', '=', 0)]}"><field name="th_time8" widget="float_time"/></th>
                                </tr>
                                <tr>
                                    <td attrs="{'invisible': [('th_time1', '=', 0)]}"><field name="th_select_time1" attrs="{'readonly': [('th_status', '!=', 'draft')]}"/></td>
                                    <td attrs="{'invisible': [('th_time2', '=', 0)]}"><field name="th_select_time2" attrs="{'readonly': [('th_status', '!=', 'draft')]}"/></td>
                                    <td attrs="{'invisible': [('th_time3', '=', 0)]}"><field name="th_select_time3" attrs="{'readonly': [('th_status', '!=', 'draft')]}"/></td>
                                    <td attrs="{'invisible': [('th_time4', '=', 0)]}"><field name="th_select_time4" attrs="{'readonly': [('th_status', '!=', 'draft')]}"/></td>
                                    <td attrs="{'invisible': [('th_time5', '=', 0)]}"><field name="th_select_time5" attrs="{'readonly': [('th_status', '!=', 'draft')]}"/></td>
                                    <td attrs="{'invisible': [('th_time6', '=', 0)]}"><field name="th_select_time6" attrs="{'readonly': [('th_status', '!=', 'draft')]}"/></td>
                                    <td attrs="{'invisible': [('th_time7', '=', 0)]}"><field name="th_select_time7" attrs="{'readonly': [('th_status', '!=', 'draft')]}"/></td>
                                    <td attrs="{'invisible': [('th_time8', '=', 0)]}"><field name="th_select_time8" attrs="{'readonly': [('th_status', '!=', 'draft')]}"/></td>
                                </tr>
                            </table>
                        </group>
                    </group>
                    <notebook>
                        <page string="Detail attendance">
                            <field name="th_attendance_ids">
                                <tree no_open="1">
                                    <field name="employee_id" />
                                    <field name="check_in"/>
                                    <field name="check_out"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Request created in month">
                            <field name="th_attendance_request_ids">
                                <tree no_open="1">
                                    <field name="th_attendance_type"/>
                                    <field name="th_date_req" />
                                    <field name="th_type"/>
                                    <field name="th_time" widget="float_time"/>
                                    <field name="th_status" />
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="th_attendance_request_view_form_refuse" model="ir.ui.view">
        <field name="name">th_attendance_request_view_form_refuse</field>
        <field name="model">th.attendance.request</field>
        <field name="arch" type="xml">
            <form string="">
                <sheet>
                    <group>
                        <field name="th_reason" required="1"/>
                    </group>
                </sheet>
                <footer>
                    <button name="action_refuse" type="object" string="Save" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="th_attendance_request_view_tree" model="ir.ui.view">
        <field name="name">th.attendance.request.view.tree</field>
        <field name="model">th.attendance.request</field>
        <field name="arch" type="xml">
            <tree string="Attendance Correction Request" default_order="th_date_req desc">
                <field name="name"/>
                <field name="th_employee_id"/>
                <field name="th_date_req" optional="hide"/>
                <field name="th_attendance_type"/>
                <field name="th_type" optional="hide"/>
                <field name="th_time" widget="float_time" optional="hide"/>
                <field name="th_reason" optional="hide"/>
                <field name="th_status"/>
                <field name="th_is_visible_button" invisible="1"/>
                <button string="Confirm" name="action_confirm" type="object" icon="fa-check" attrs="{'invisible': [('th_is_visible_button', '!=', True)]}"/>
                <button string="Refuse" name="action_refuse_open_view" type="object" icon="fa-times" attrs="{'invisible': [('th_is_visible_button', '!=', True)]}"/>
            </tree>
        </field>
    </record>

    <record id="th_attendance_request_view_search" model="ir.ui.view">
        <field name="name">th.attendance.request.view.search</field>
        <field name="model">th.attendance.request</field>
        <field name="arch" type="xml">
            <search string="Fields Rules">
                <field name="name"/>
                <filter string="Need approve" name="state_waiting" domain="[('th_status', '=', 'waiting')]"/>
                <group string="Group By">
                    <filter string="Request Date" name="group_by_request_date" domain="[]" context="{'group_by': 'th_date_req'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="th_attendance_request_action" model="ir.actions.act_window">
       <field name="name">Attendance Correction Request</field>
       <field name="res_model">th.attendance.request</field>
       <field name="view_mode">tree,form</field>
       <field name="view_id" ref="th_attendance_request_view_tree"/>
   </record>

    <menuitem
        id="th_attendance_request_menu"
        name="Attendance Correction Request"
        action="th_attendance_request_action"
        parent="hr_attendance.menu_hr_attendance_root"
        sequence="11"/>
</odoo>