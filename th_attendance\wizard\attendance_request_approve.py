from dateutil.relativedelta import relativedelta

from odoo import fields, models, api, _, exceptions
from datetime import datetime, timedelta, date
from odoo.addons.resource.models.resource import float_to_time
from odoo.tools import float_round


def time_to_float(t):
    return float_round(t.hour + t.minute/60 + t.second/3600, precision_digits=2)


class AttendanceRequestApprove(models.TransientModel):
    _name = "th.attendance.request.approve"
    _description = "Phê duyệt điều chỉnh công"

    th_employee_id = fields.Many2one(comodel_name="hr.employee", string="Nhân viên")
    th_date_req = fields.Date(string="Request Date")
    th_time = fields.Float(string="Giờ")
    th_attendance_request_id = fields.Many2one(comodel_name="th.attendance.request", string="Yêu cầu điều chỉnh công")
    th_attendance_id = fields.Many2one(comodel_name="hr.attendance", string="Chấm công")
    th_state = fields.Selection(selection=[('old', 'Cũ'), ('edit', 'Sửa'), ('new', 'Mới')], string="Trạng thái", default="old")
    th_is_request_morning = fields.<PERSON>olean('Sáng hay chiều', default=True)

    def create_attendance(self, domain):
        # Lấy tất cả records cần thiết trong 1 lần query để tránh truy vấn nhiều lần
        domain_records = self.search([tuple(domain)])
        if not domain_records:
            return
        
        # Lấy thông tin cần thiết từ record đầu tiên
        first_record = domain_records[0]
        employee_id = first_record.th_employee_id.id
        date_req = first_record.th_date_req

        # Tạo danh sách thời gian và sắp xếp một lần
        time_to_create = sorted(domain_records.mapped('th_time'))
        
        # Xử lý tạo thời gian check in/out
        creation_time = []
        for time in time_to_create:
            ftime = float_to_time(time)
            check_time = fields.Datetime.to_datetime(date_req).replace(
                hour=ftime.hour, minute=ftime.minute) - relativedelta(hours=7)
            
            if not creation_time:
                creation_time.append([check_time, False])
            else:
                if not creation_time[-1][1]:
                    creation_time[-1][1] = check_time
                else:
                    creation_time.append([check_time, False])

        # Xóa attendance cũ trong 1 lần query
        self.env['hr.attendance'].sudo().search([
            ('th_attendance_date', '=', date_req),
            ('employee_id', '=', employee_id),
            ('th_att_code', '=', 'WORK100')
        ]).unlink()

        # Lấy request mới trong 1 lần query
        request_in_dates = self.search([
            ('th_state', '=', 'new'), 
            ('th_date_req', '=', date_req)
        ])

        # Chuẩn bị data để tạo attendance hàng loạt
        attendance_vals = []
        for time in creation_time:
            attendance_vals.append({
                'employee_id': employee_id,
                'check_in': time[0],
                'check_out': time[1],
            })

        # Tạo attendance hàng loạt thay vì từng cái một
        attendances = self.env['hr.attendance'].sudo().create(attendance_vals)

        # Xử lý cập nhật th_is_changed
        for attendance in attendances:
            time_check = [
                attendance.check_in + relativedelta(hours=7),
                attendance.check_out + relativedelta(hours=7) if attendance.check_out else False
            ]
            
            for request in request_in_dates:
                if request.th_time in time_check and request.th_is_request_morning == attendance.th_is_morning_att:
                    attendance.sudo().write({'th_is_changed': True})
                    break

        # Cập nhật trạng thái request một lần
        domain_records.mapped('th_attendance_request_id').write({'th_status': 'confirm'})

    @api.model_create_multi
    def create(self, vals_list):
        for values in vals_list:
            if not values.get('th_attendance_id'):
                values['th_state'] = 'new'
        return super().create(vals_list)

    def write(self, values):
        if values.get('th_time'):
            values['th_state'] = 'edit'
        return super(AttendanceRequestApprove, self).write(values)
