from odoo import models, fields, api
from odoo.tools.translate import _
from odoo.exceptions import UserError,ValidationError


class ThRefundInvoice(models.TransientModel):
    _name = 'th.refund.invoice.apm'
    _description = 'Hoàn tiền'

    move_ids = fields.Many2many('account.move', 'account_move_reversal_move_apm', 'reversal_id', 'move_id',
                                domain=[('state', '=', 'posted')])
    new_move_ids = fields.Many2many('account.move', 'account_move_reversal_new_move_apm', 'reversal_id', 'new_move_id')
    date_mode = fields.Selection(selection=[
        ('custom', 'Đặc thù'),
        ('entry', '<PERSON><PERSON>y bút toán')
    ], required=True, default='custom')
    date = fields.Date(string='Ngày hoàn tiền', default=fields.Date.context_today)
    reason = fields.Char(string='Lý do')
    refund_method = fields.Selection(selection=[
        ('refund', 'Hoàn tiền một phần'),
        ('cancel', 'Hoàn tiền toàn bộ'),
        ('modify', 'Hoàn tiền toàn bộ và hóa đơn nháp mới')

    ], string='Phương thức phát sinh giảm', required=True,
        help='Choose how you want to credit this invoice. You cannot "modify" nor "cancel" if the invoice is already reconciled.')

    th_refund_method = fields.Selection(selection=[
        ('refund_half_pay', 'Hoàn tiền một phần'),
        ('refund_excess_pay', 'Hoàn tiền dư'),
        ('refund_full_pay', 'Hoàn tiền toàn bộ')

    ], string='Phương thức hoàn tiền', required=True,default='refund_full_pay',
        help='Chọn phương thức cần hoàn tiền')

    journal_id = fields.Many2one(
        comodel_name='account.journal',
        string='Chỉ định nhật kí dùng',
        required=True,
        compute='_compute_journal_id',
        readonly=False,
        store=True,
        check_company=True,
        help='If empty, uses the journal of the journal entry to be reversed.',
    )
    company_id = fields.Many2one('res.company', required=True, readonly=True)
    available_journal_ids = fields.Many2many('account.journal', compute='_compute_available_journal_ids')
    country_code = fields.Char(related='company_id.country_id.code')

    # computed fields
    residual = fields.Monetary(compute="_compute_from_moves")
    currency_id = fields.Many2one('res.currency', compute="_compute_from_moves")
    order_id = fields.Many2one('sale.order')
    move_type = fields.Char(compute="_compute_from_moves")
    th_refund_money = fields.Monetary(string="Số tiền hoàn", compute="_compute_th_refund_money", store= True, readonly=False)

    @api.depends('move_ids')
    def _compute_journal_id(self):
        for record in self:
            if record.journal_id:
                record.journal_id = record.journal_id
            else:
                journals = record.move_ids.journal_id.filtered(lambda x: x.active)
                record.journal_id = journals[0] if journals else None

    @api.depends('order_id', 'th_refund_method')
    def _compute_th_refund_money(self):
        for rec in self:
            if rec.order_id:
                total_refund = 0
                paid_refund = sum(self.env['account.move'].search([('id', 'in', rec.order_id.invoice_ids.ids)]).mapped('th_refund_amount'))
                for res in rec.order_id.invoice_ids:
                    if rec.th_refund_method == 'refund_full_pay':
                        total_refund += res.th_receive_amount
                    if rec.th_refund_method == 'refund_excess_pay':
                        if res.th_receive_amount > res.amount_total:
                            total_refund += (res.th_receive_amount - res.amount_total)
                rec.th_refund_money = total_refund - paid_refund
                rec.th_refund_money = 0 if rec.th_refund_money < 0 else rec.th_refund_money

    @api.depends('move_ids')
    def _compute_available_journal_ids(self):
        for record in self:
            if record.move_ids:
                record.available_journal_ids = self.env['account.journal'].search([
                    ('company_id', '=', record.company_id.id),
                    ('type', 'in', record.move_ids.journal_id.mapped('type')),
                ])
            else:
                record.available_journal_ids = self.env['account.journal'].search(
                    [('company_id', '=', record.company_id.id)])

    @api.constrains('journal_id', 'move_ids')
    def _check_journal_type(self):
        for record in self:
            if record.journal_id.type not in record.move_ids.journal_id.mapped('type'):
                raise UserError(_('Journal should be the same type as the reversed entry.'))

    @api.model
    def default_get(self, fields):
        res = super(ThRefundInvoice, self).default_get(fields)
        # move_ids = self.env['account.move'].browse(self.env.context['active_ids']) if self.env.context.get(
        #     'active_model') == 'account.move' else self.env['account.move']
        move_ids = self.env['sale.order'].search([('id', 'in', self.env.context['active_ids'])]).invoice_ids if self.env.context.get('active_model') == 'sale.order' else self.env['account.move']

        # if any(move.state != "posted" for move in move_ids):
        #     raise UserError(_('You can only reverse posted moves.'))
        if 'company_id' in fields:
            res['company_id'] = move_ids.company_id.id or self.env.company.id
        if 'move_ids' in fields:
            res['move_ids'] = [(6, 0, move_ids.ids)]
        if 'refund_method' in fields:
            res['refund_method'] = (len(move_ids) > 1 or move_ids.move_type == 'entry') and 'cancel' or 'refund'
        return res

    @api.depends('move_ids')
    def _compute_from_moves(self):
        for record in self:
            move_ids = record.move_ids._origin
            record.residual = len(move_ids) == 1 and move_ids.amount_residual or 0
            record.currency_id = len(move_ids.currency_id) == 1 and move_ids.currency_id or False
            record.move_type = move_ids.move_type if len(move_ids) == 1 else (any(
                move.move_type in ('in_invoice', 'out_invoice') for move in move_ids) and 'some_invoice' or False)

    def _prepare_default_reversal(self, move):
        reverse_date = self.date if self.date_mode == 'custom' else move.date
        return {
            'ref': _('Reversal of: %(move_name)s, %(reason)s', move_name=move.name, reason=self.reason)
            if self.reason
            else _('Reversal of: %s', move.name),
            'date': reverse_date,
            'invoice_date_due': reverse_date,
            'invoice_date': move.is_invoice(include_receipts=True) and (self.date or move.date) or False,
            # 'journal_id': self.journal_id.id,
            'invoice_payment_term_id': None,
            'invoice_user_id': move.invoice_user_id.id,
            'auto_post': 'at_date' if reverse_date > fields.Date.context_today(self) else 'no',
        }

    def reverse_moves(self):
        # self.ensure_one()
        # for rec in self.order_id:
        # for res_moves in self.move_ids:
        for res_moves in self.env['account.move'].search([('id','in',self.order_id.invoice_ids.ids),('move_type','=','out_invoice')], limit=1):
            # if res_moves.th_is_refunded_tuition or res_moves.reversed_entry_id:
            #     continue
            if res_moves.th_payment_status == "not_paid":
                raise ValidationError("Không thể hoàn tiền hóa đơn chưa ghi nhận thanh toán!")
            if self.th_refund_money == 0:
                raise ValidationError("Không thể hoàn tiền với số tiền 0đ")
            pay_state = res_moves.th_payment_status
            # Create default values.
            default_values_list = []
            # res_moves.reversal_move_id = False
            for move in res_moves:
                default_values_list.append(self._prepare_default_reversal(move))

            batches = [
                [self.env['account.move'], [], True],  # Moves to be cancelled by the reverses.
                [self.env['account.move'], [], False],  # Others.
            ]
            for move, default_vals in zip(res_moves, default_values_list):
                is_auto_post = default_vals.get('auto_post') != 'no'
                is_cancel_needed = not is_auto_post and self.refund_method in ('cancel', 'modify')
                batch_index = 0 if is_cancel_needed else 1
                batches[batch_index][0] |= move
                batches[batch_index][1].append(default_vals)

            # Handle reverse method.
            moves_to_redirect = self.env['account.move']
            for moves, default_values_list, is_cancel_needed in batches:
                new_moves = moves.sudo()._reverse_moves(default_values_list, cancel=is_cancel_needed)

                if self.refund_method == 'modify':
                    moves_vals_list = []
                    for move in moves.with_context(include_business_fields=True):
                        moves_vals_list.append(
                            move.copy_data({'date': self.date if self.date_mode == 'custom' else move.date})[0])
                    new_moves = self.env['account.move'].sudo().create(moves_vals_list)
                if new_moves:
                    for records in new_moves.line_ids:
                        records.matched_debit_ids.unlink()
                        records.matched_credit_ids.unlink()
                    new_moves.state = 'draft'
                    for record in new_moves.invoice_line_ids:
                        if new_moves.state == 'draft':
                            record.price_subtotal = 0
                            record.price_unit = 0
                    refund_product_id = self.env.ref('th_select_module.th_refund_product')
                    refund_product_id.detailed_type = 'consu'
                    # refund_product_id.uom_id.category_id = False
                    new_refund_line = self.env['account.move.line'].sudo().create({
                        'product_id': refund_product_id.id,
                        'price_unit': self.th_refund_money,
                        'move_id': new_moves.id,
                        'product_uom_id': refund_product_id.uom_id.id,
                        'quantity': 1.0,
                    })
                    # self.order_id.order_line.invoice_lines = [(4, new_refund_line.id)]
                    # new_moves.invoice_line_ids =[(4, new_refund_line.id)]
                    # new_moves.state = 'posted'
                moves_to_redirect |= new_moves
            if moves_to_redirect.reversed_entry_id:
                moves_to_redirect.sudo().action_post()
                msg = ("Đã tạo hóa đơn hoàn tiền với mã <b>%s</b>" % moves_to_redirect.name)
                moves_to_redirect.th_apm_lead_id.sudo().message_post(body=msg)
                done_reversed_create = self.env['account.move'].sudo().search([('id', '=', moves_to_redirect.reversed_entry_id.id)])
                done_reversed_create.th_is_refunded_tuition = True
                done_reversed_create.th_payment_status = pay_state
                # if self.order_id.th_total_received_excessive and self.order_id.th_total_refund_excessive and (
                #         self.order_id.th_total_received_excessive <= self.order_id.th_total_refund_excessive):
                #     self.order_id.th_apm_id.th_partner_id.sudo().write({'th_is_order_apm': False})
            self.new_move_ids = moves_to_redirect
