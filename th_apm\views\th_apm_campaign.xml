<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_th_apm_campaign_tree" model="ir.ui.view">
        <field name="name">th.apm.campaign.tree</field>
        <field name="model">th.apm.campaign</field>
        <field name="arch" type="xml">
            <tree string="list campaign">
                <field name="name"/>
                <field name="th_origin_id"/>
                <field name="th_divide"/>
                <field name="qty"/>
                <field name="state"/>

            </tree>
        </field>
    </record>
    <record id="view_th_apm_campaign_form" model="ir.ui.view">
        <field name="name">th.apm.campaign.form</field>
        <field name="model">th.apm.campaign</field>
        <field name="arch" type="xml">
            <form string="add new">
                <header>
                    <button name="action_send_for_approval" type="object" string="Gửi duyệt" states="draft" class="btn-primary"/>
                    <button name="action_approved" type="object" string="Duyệt" states="pending" class="btn-primary" groups="th_apm.group_apm_manager" confirm="Bạn có chắc muốn duyệt chiến dịch này?"/>
                    <button name="action_refuse" type="object" string="Từ chối" states="pending" class="btn-primary" groups="th_apm.group_apm_manager" confirm="Bạn có chắc muốn từ chối chiến dịch này?"/>
                    <button name="action_completed" type="object" string="Hoàn thành" class="btn-primary" attrs="{'invisible': ['|', ('th_system_campaign', '=', True), ('state', 'not in', ['approve'])]}" confirm="Bạn có chắc muốn kết thúc chiến dịch này?"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,pending,approve,completed"/>
                    <field name="th_system_campaign" invisible="1"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_open_apm_lead" type="object" class="oe_stat_button" icon="fa-star">
<!--                            attrs="{'invisible': [('th_apm_lead_count', '=', 0)]}"-->
                            <field name="th_apm_lead_count" widget="statinfo" string="Cơ hội" />
                        </button>
                    </div>

                    <div class="oe_title">
                        <field name="th_system_campaign" invisible="1"/>
                        <h1>
                            <field class="text-break" name="name" placeholder="VD: Tên chiến dịch" attrs="{'required' : [('th_system_campaign', '=', False)]}"/>
                        </h1>
                    </div>
                    <group>
                        <group string="Điều kiện">
<!--                            <field name="th_product_line_id" required="1"  widget="selection"/>-->
                            <field name="th_apm_divide_ring_domain_ids" invisible="1"/>
                            <field name="th_origin_id" attrs="{'required' : [('th_system_campaign', '=', False)]}" widget="selection" domain="[('th_module_ids.name', 'in', ['APM'])]"/>
                            <field name="th_product_category_id" domain="th_product_category_domain" options="{'no_create': True, 'no_create_edit':True, 'no_open': True}"
                                   attrs="{'invisible': [('th_origin_id', '=', False)]}" invisible="1"/>
                            <field name="th_product_ids" widget="many2many_tags" domain="th_product_domain"
                                   attrs="{'invisible': [('th_product_category_id', '=', False)]}"/>
                            <field name="th_apm_trait_ids" widget="many2many_tags" attrs="{'invisible':[('th_trait_domain', '=', '[]')]}" domain="th_trait_domain"/>
                            <field name="th_apm_trait_value_ids" widget="many2many_tags" attrs="{'invisible':[('th_trait_value_domain', '=', '[]')]}" domain="th_trait_value_domain"/>
                            <field name="th_number_contact" readonly="1" attrs="{'invisible':[('th_system_campaign', '=', True)]}"/>
                            <field name="th_product_domain" invisible="1"/>
                            <field name="th_trait_domain" invisible="1"/>
                            <field name="th_trait_value_domain" invisible="1"/>
                            <field name="state" invisible="1"/>
                            <field name="th_product_category_domain" invisible="1"/>
                        </group>

                        <group string="Thông tin">
                            <field name="th_divide" attrs="{'required' : [('th_system_campaign', '=', False)]}"/>
                            <field name="th_apm_divide_ring_ids" widget="many2many_tags" domain="[('id', 'in', th_apm_divide_ring_domain_ids)]" options="{'no_create': True, 'no_create_edit':True, 'no_open': True}" attrs="{'required':[('th_divide', '!=', 'none')],'invisible':[('th_divide', '=', 'none')]}"/>
                            <!-- <field name="th_apm_team_ids" widget="many2many_tags"  options="{'no_create': True, 'no_create_edit':True, 'no_open': True}" attrs="{'required' : [('th_divide', '!=', 'none')],'invisible':[('th_divide', '=', 'none')]}"/> -->
                            <field name="is_no_lead"/>
                            <field name="qty" force_save="1" attrs="{'required' : [('th_system_campaign', '=', False)], 'invisible':[('th_divide', '=', 'none')],'readonly': [('is_no_lead', '=', True)]}"/>
                            <field name="is_campaign_auto"/>
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

<!--    <record id="view_th_apm_campaign_form_popup" model="ir.ui.view">-->
<!--        <field name="name">th.apm.campaign.form</field>-->
<!--        <field name="model">th.apm.campaign</field>-->
<!--        <field name="arch" type="xml">-->
<!--            <form string="add new">-->
<!--                <sheet>-->
<!--                    <group>-->
<!--                        <field name="th_potential_customers"/>-->
<!--                        -->
<!--                    </group>-->
<!--                    <footer>-->
<!--                        <button string="Tạo cơ hội" type="object" name="create_opportunities" class="btn btn-primary"/>-->
<!--                    </footer>-->
<!--                </sheet>-->
<!--            </form>-->
<!--        </field>-->
<!--    </record>-->

    <record id="th_apm_campaign_action" model="ir.actions.act_window">
        <field name="name">Chiến dịch</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.apm.campaign</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>
