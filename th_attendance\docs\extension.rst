.. _abs_attendances_module:

Mo<PERSON><PERSON> cô<PERSON> (th_attendance)
=====================

Tổng quan
---------

**Kế thừa module**:

- ``base``
- ``mail``
- ``report_xlsx``
- ``hr_attendance``

**Mục tiêu**:

<PERSON><PERSON><PERSON> ``th_attendance`` được kế thừa và mở rộng các chức năng quản lý chấm công trong module gốc ``hr_attendance`` của Odoo. Mo<PERSON><PERSON> nà<PERSON> bổ sung các tính năng như quản lý chấm công online, báo cáo đi muộn, yêu cầu điều chỉnh công, xử lý dữ liệu nhân viên không xác định, và cải tiến quy trình phê duyệt chấm công.

Chứ<PERSON> năng chỉnh sửa
------------------

1. <PERSON><PERSON><PERSON><PERSON> lý chấm công (HrAttendance)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Model kế thừa**:

- ``hr.attendance`` (từ module gốc ``hr_attendance``)

**Mục đích kế thừa**:

- Mở rộng model ``hr.attendance`` để thêm các trường và logic hỗ trợ quản lý chấm công online, chấm công muộn, theo dõi trạng thái phê duyệt, và tích hợp thông báo qua email.
- Tùy chỉnh các hàm xử lý chấm công để đáp ứng các yêu cầu đặc thù như kiểm tra thời gian chồng lấn, quản lý ca làm việc, và tính toán thời gian chênh lệch.

**Logic hoặc hàm thay đổi**:

- **Hàm ghi đè/thay đổi**:
  - ``create``: Ghi đè hàm gốc để hỗ trợ import file (gán `employee_id` từ `th_attendance_number`) và chế độ kiosk (gán `th_att_code` và trạng thái `wait`).
  - ``_check_validity``: Ghi đè để bổ sung kiểm tra chấm công đã phê duyệt thông qua hàm `check_attendance_approved`.
  - ``fields_view_get`` (kế thừa từ model ir_ui_view): Ghi đè để tùy chỉnh giao diện tree view của báo cáo chấm công, thay đổi nhãn các trường `th_diff_in` và `th_diff_out` dựa trên ngữ cảnh.

**View / Action / Menu**:

- **Views**:
  - ``th_view_attendance_tree``: Tree view cho chấm công thông thường.
  - ``th_view_attendance_online_tree``: Tree view cho chấm công online.
  - ``th_view_attendance_form``: Form view cho chấm công, giới hạn quyền chỉnh sửa theo nhóm `hr_attendance.group_hr_attendance_manager`.
  - ``th_hr_attendance_view_filter``: Bộ lọc tìm kiếm tùy chỉnh cho chấm công.
  - ``th_hr_attendance_report_view_tree``: Tree view cho báo cáo chấm công.

- **Actions**:
  - ``Normal Attendances``: Hiển thị chấm công với bộ lọc `th_att_code = 'WORK100'` hoặc `th_att_type = 'normal'`.
  - ``External Attendances``: Hiển thị chấm công online với bộ lọc `th_att_code = 'WFH'` hoặc `th_att_type = 'online'`.
  - ``Monthly Attendances``: Hiển thị chấm công tháng của nhân viên hiện tại, chỉ đọc.

- **Menus**:
  - ``Normal Attendances``, ``External Attendances``, ``Monthly Attendances``: Được định nghĩa trong ``menu.xml`` dưới mục cha ``External Attendance``.

2. Cấu hình hệ thống (ResConfigSettings)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Model kế thừa**:

- ``res.config.settings`` (từ module gốc ``hr_attendance``)

**Mục đích kế thừa**:

- Bổ sung các tham số cấu hình tùy chỉnh cho chấm công, như mã chấm công mặc định, mã kiosk, và quy trình phê duyệt, đồng thời giữ nguyên các cấu hình gốc như tính giờ làm thêm (`hr_attendance_overtime`).

**Logic hoặc hàm thay đổi**:

- **Hàm ghi đè**:
  - ``get_values``:
    - Kế thừa hàm gốc để lấy các giá trị cấu hình liên quan đến giờ làm thêm (`hr_attendance_overtime`, `overtime_start_date`, `overtime_company_threshold`, `overtime_employee_threshold`).
    - Bổ sung logic lấy các tham số tùy chỉnh (`th_normal_att`, `th_kiosk_att`, `th_validation_type`) từ `ir.config_parameter`.
    - Đặt giá trị mặc định cho `th_validation_type` là `twice` nếu chưa được cấu hình.
  - ``set_values``:
    - Kế thừa hàm gốc để lưu các giá trị cấu hình giờ làm thêm vào `res.company`.
    - Bổ sung logic lưu các tham số tùy chỉnh (`th_normal_att`, `th_kiosk_att`, `th_validation_type`) vào `ir.config_parameter`.

**View / Action / Menu**:

- **Views**:
  - ``th.res.config.settings.view.form``: Form view tùy chỉnh, thêm các trường `th_normal_att`, `th_kiosk_att`, `th_validation_type`, và `th_l_a_manager_ids`.
  - Kế thừa ``res.config.settings.view.form.inherit.hr.attendance`` từ module gốc để hiển thị các cấu hình liên quan đến giờ làm thêm và chế độ kiosk.

- **Actions**:
  - ``Settings``: Hành động mở form cấu hình, tích hợp các trường tùy chỉnh.

- **Menus**:
  - Tích hợp vào menu cấu hình của Odoo qua ``menu.xml``.

3. Quản lý nhân viên (HrEmployee)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Model kế thừa**:

- ``hr.employee`` (từ module gốc ``hr_attendance``)

**Mục đích kế thừa**:

- Mở rộng model ``hr.employee`` để hỗ trợ quản lý mã PIN duy nhất, theo dõi chấm công bị bỏ sót, xử lý dữ liệu chấm công không xác định, và tích hợp chế độ kiosk.

**Logic hoặc hàm thay đổi**:

- **Hàm ghi đè/thay đổi**:
  - ``create``: Ghi đè để xử lý dữ liệu chấm công không xác định (`th.att.unknown.employee`) khi tạo nhân viên mới, liên kết bản ghi chấm công dựa trên mã PIN.
  - ``write``: Ghi đè để xử lý dữ liệu chấm công không xác định khi cập nhật mã PIN, vô hiệu hóa bản ghi chấm công cũ.
  - ``_attendance_action_change``: Ghi đè để hỗ trợ chế độ kiosk, lưu địa chỉ IP (`th_checkin_ip`, `th_checkout_ip`) và sử dụng mã chấm công kiosk (`th_kiosk_att`).
  - ``_compute_last_attendance_id``: Ghi đè để giới hạn bản ghi chấm công mới nhất trong ngày hiện tại với mã `th_kiosk_att`.
  - ``_compute_hours_today``: Ghi đè để tính giờ làm việc trong ngày dựa trên mã `th_kiosk_att` và múi giờ nhân viên.

**View / Action / Menu**:

- **Views**:
  - ``hr_employee.xml``: Tùy chỉnh giao diện nhân viên, thêm bộ lọc hiển thị nhân viên liên kết với người dùng hiện tại.
  - Kế thừa ``hr.employee.kanban`` từ module gốc, bổ sung hiển thị giờ làm thêm (`total_overtime`) và giờ hôm nay (`hours_today`).

- **Menus**:
  - Tích hợp vào menu nhân viên của Odoo qua ``menu.xml``.


View / Action / Menu (Tổng quan)
-------------------------------

- **Views**:
  - `th.res.config.settings.view.form`
  - `th_view_attendance_tree`
  - `th_view_attendance_online_tree`
  - `th_view_attendance_form`
  - `hr.attendance.tree`
  - `th_hr_attendance_view_filter`

- **Actions**:
  - `Normal Attendances`
  - `External Attendances`
  - `Monthly Attendances`

- **Menus**:

  - `Tổng hợp chấm công`
    - `Công máy`
    - `Công online`
  - `Điều chỉnh công`
  - `Chấm công online`
  - `Máy vân tay`
    - `Tải chấm công lên`
    - `Nhân viên chấm công không xác định`
  - `Báo cáo chấm công`
      - `Báo cáo chấm công muộn`
  - `Cấu hình`

