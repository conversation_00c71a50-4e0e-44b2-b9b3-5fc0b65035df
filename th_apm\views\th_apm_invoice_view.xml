<odoo>
    <record id="th_apm_account_move_form_inherit" model="ir.ui.view">
        <field name="name">th_account_move_form_inherit</field>
        <field name="model">account.move</field>
        <field name="priority">99999</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_view_source_sale_orders']" position="attributes">
                <attribute name="invisible">context.get('view_apm', False)</attribute>
            </xpath>
            <xpath expr="//header/field[@name='state']" position="attributes">
                <attribute name="statusbar_visible">posted</attribute>
            </xpath>
<!--            <xpath expr="//header/button[@name='button_draft']" position="attributes">-->
<!--                <attribute name="invisible">1</attribute>-->
<!--            </xpath>-->
            <xpath expr="//field[@name='partner_id']" position="after">
                <!-- Thêm trường mới sau partner_id -->
                <field name="th_customer_code_aum" attrs="{'invisible': [('th_account_move', '!=', 'apm')]}" invisible="1"/>
                <field name="th_apm_lead_name" attrs="{'invisible': [('th_account_move', '!=', 'apm')]}"/>
                <field name="th_is_a_simple_lesson" attrs="{'invisible': [('th_account_move', '!=', 'apm')]}" readonly="1"/>
                <field name="th_responsibly_apm" attrs="{'invisible': [('th_account_move', '!=', 'apm')]}" options="{'no_create': True, 'no_open': True}"/>
                <field name="th_preferential_policy" attrs="{'invisible': [('th_account_move', '!=', 'apm')]}" readonly="1"/>
            </xpath>

            <xpath expr="//field[@name='invoice_date']" position="after">
                <!-- Thêm trường trước sau invoice_date -->
                <field name="th_apm_source_group_id" attrs="{'invisible': [('th_account_move', '!=', 'apm')]}" options="{'no_create': True, 'no_open': True}"/>
                <field name="th_apm_ownership_id" attrs="{'invisible': [('th_account_move', '!=', 'apm')]}" options="{'no_create': True, 'no_open': True}"/>
                <field name="th_channel_id" attrs="{'invisible': [('th_account_move', '!=', 'apm')]}" options="{'no_create': True, 'no_open': True}"/>
                <field name="th_introducer_id" attrs="{'invisible': [('th_account_move', '!=', 'apm')]}" options="{'no_create': True, 'no_open': True}"/>
            </xpath>

            <xpath expr="//field[@name='invoice_date']" position="after">
                <field name="th_apm_origin_name" attrs="{'invisible': [('th_account_move', '!=', 'apm')]}" readonly="1"/>
            </xpath>

            <xpath expr="//header" position="attributes">
                <attribute name="invisible">context.get('view_apm', False)</attribute>
            </xpath>

            <xpath expr="//field[@name='tax_totals']" position="attributes">
                <attribute name="attrs">{'invisible': [('th_account_move', '=', 'apm')]}</attribute>
            </xpath>
            <xpath expr="//field[@name='tax_totals']" position="after">
                <field name="amount_total" widget="monetary" readonly="1" style="font-size: 16px; font-weight: bold;" attrs="{'invisible': [('th_account_move', '!=', 'apm')]}"/>
            </xpath>

            <xpath expr="//notebook/page[@name='invoice_tab']/field/tree/field[@name='tax_ids']" position="attributes">
                <attribute name="attrs">{'column_invisible': [('parent.th_account_move', '=', 'apm')]}</attribute>
            </xpath>

            <xpath expr="//notebook/page[@name='invoice_tab']/field/tree/field[@name='tax_ids']" position="after">
                <field name="th_account_move" invisible="1"/>
            </xpath>

            <xpath expr="//notebook/page[@name='invoice_tab']/field/tree/field[@name='discount']" position="attributes">
                <attribute name="string">Chiết khấu</attribute>
            </xpath>

        </field>
    </record>

    <record id="th_apm_account_invoice_form" model="ir.ui.view">
        <field name="name">th_apm_account_invoice_form</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="sale.account_invoice_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='utm_link']//field[@name='campaign_id']" position="attributes">
                <attribute name="invisible">context.get('view_apm', False)</attribute>
            </xpath>
        </field>
    </record>

    <record id="view_account_invoice_filter_inherit" model="ir.ui.view">
        <field name="name">account_invoice_select_inherit</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_account_invoice_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <!-- Add search by product field -->
                <field name="name" string="Sản phẩm" filter_domain="['|', ('invoice_line_ids.name', 'ilike', self), ('invoice_line_ids.product_id.name', 'ilike', self)]"/>
                <separator/>
                    <!-- Add search by product line field -->
                    <field name="th_apm_origin_name" string="Dòng sản phẩm" filter_domain="[('th_apm_origin_name', 'ilike', self)]"/>
                <separator/>    
                <group>
                    <!--                <filter name="apm_order" string="APM" domain="[('th_account_move', '=', 'apm')]"/>-->
                    <!--                <filter name="crm_order" string="CRM" domain="[('th_account_move', '=', 'crm')]"/>-->
                    <!--                <filter name="crm_order" string="SRM" domain="[('th_account_move', '=', 'srm')]"/>-->
                    <filter name="th_apm_no_pay_invoice" string="Hóa đơn chưa thanh toán APM"
                            domain="[('th_account_move', '=', 'apm'),('payment_state', '=', 'not_paid'),('reversed_entry_id', '=', False)]"
                            invisible="not context.get('invoice_apm')"/>
                    <filter name="th_apm_half_pay_invoice" string="Hóa đơn thanh toán 1 phần APM"
                            domain="[('th_account_move', '=', 'apm'),('payment_state', '=', 'partial'),('reversed_entry_id', '=', False)]"
                            invisible="not context.get('invoice_apm')"/>
                    <filter name="th_apm_full_pay_invoice" string="Hóa đơn thanh toán đủ APM"
                            domain="[('th_account_move', '=', 'apm'),('payment_state', '=', 'paid'),('reversed_entry_id', '=', False)]"
                            invisible="not context.get('invoice_apm')"/>
                    <filter name="th_apm_refunded_invoice" string="Hóa đơn thu - đã hoàn APM"
                            domain="[('th_account_move', '=', 'apm'),('is_refund_invoice', '!=', False)]"
                            invisible="not context.get('invoice_apm')"/>
                </group>
            </xpath>
        </field>
    </record>
    <!--    Hóa đơn CRM-->
<!--    <record id="th_no_pay_invoice_apm" model="ir.actions.act_window">-->
<!--        <field name="name">Hóa đơn chưa thanh toán</field>-->
<!--        <field name="type">ir.actions.act_window</field>-->
<!--        <field name="res_model">account.move</field>-->
<!--        <field name="context">{'create':0, 'invoice_apm': True}</field>-->
<!--        <field name="view_mode">tree,form</field>-->
<!--        <field name="domain">[('th_account_move', '=', 'apm'),('payment_state', '=', 'not_paid'),('reversed_entry_id', '=', False)]</field>-->
<!--        <field name="view_ids" eval="[(5, 0, 0),-->
<!--            (0, 0, {'view_mode': 'tree', 'view_id': ref('account.view_out_invoice_tree')}),-->
<!--            (0, 0, {'view_mode': 'form', 'view_id': ref('account.view_move_form')}),-->
<!--            ]"/>-->
<!--    </record>-->

<!--    <record id="th_half_pay_invoice_apm" model="ir.actions.act_window">-->
<!--        <field name="name">Hóa đơn thanh toán 1 phần</field>-->
<!--        <field name="type">ir.actions.act_window</field>-->
<!--        <field name="res_model">account.move</field>-->
<!--        <field name="context">{'create':0, 'invoice_apm': True}</field>-->
<!--        <field name="view_mode">tree,form</field>-->
<!--        <field name="domain">[('th_account_move', '=', 'apm'),('payment_state', '=', 'partial'),('reversed_entry_id', '=', False)]</field>-->
<!--        <field name="view_ids" eval="[(5, 0, 0),-->
<!--            (0, 0, {'view_mode': 'tree', 'view_id': ref('account.view_out_invoice_tree')}),-->
<!--            (0, 0, {'view_mode': 'form', 'view_id': ref('account.view_move_form')}),-->
<!--            ]"/>-->
<!--    </record>-->

<!--    <record id="th_full_pay_invoice_apm" model="ir.actions.act_window">-->
<!--        <field name="name">Hóa đơn thanh toán đủ</field>-->
<!--        <field name="type">ir.actions.act_window</field>-->
<!--        <field name="res_model">account.move</field>-->
<!--        <field name="context">{'create':0, 'invoice_apm': True}</field>-->
<!--        <field name="view_mode">tree,form</field>-->
<!--        <field name="domain">[('th_account_move', '=', 'apm'),('payment_state', '=', 'paid'),('reversed_entry_id', '=', False)]</field>-->
<!--        <field name="view_ids" eval="[(5, 0, 0),-->
<!--            (0, 0, {'view_mode': 'tree', 'view_id': ref('account.view_out_invoice_tree')}),-->
<!--            (0, 0, {'view_mode': 'form', 'view_id': ref('account.view_move_form')}),-->
<!--            ]"/>-->
<!--    </record>-->
<!--    <record id="th_refunded_invoice_apm" model="ir.actions.act_window">-->
<!--        <field name="name">Hóa đơn thu - đã hoàn</field>-->
<!--        <field name="type">ir.actions.act_window</field>-->
<!--        <field name="res_model">account.move</field>-->
<!--        <field name="context">{'create':0, 'invoice_apm': True}</field>-->
<!--        <field name="view_mode">tree,form</field>-->
<!--        <field name="domain">[('th_account_move', '=', 'apm'),('is_refund_invoice', '!=', False)]</field>-->
<!--        <field name="view_ids" eval="[(5, 0, 0),-->
<!--            (0, 0, {'view_mode': 'tree', 'view_id': ref('account.view_out_invoice_tree')}),-->
<!--            (0, 0, {'view_mode': 'form', 'view_id': ref('account.view_move_form')}),-->
<!--            ]"/>-->
<!--    </record>-->
    <record id="th_out_invoice_apm" model="ir.actions.act_window">
        <field name="name">Phiếu thu APM</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.move</field>
        <field name="context">{'create':0, 'invoice_apm': True}</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('th_account_move', '=', 'apm'),('move_type', '=', 'out_invoice')]</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('account.view_out_invoice_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('account.view_move_form')}),
            ]"/>
    </record>
    <record id="th_in_invoice_apm" model="ir.actions.act_window">
        <field name="name">Phiếu chi APM</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.move</field>
        <field name="context">{'create':0, 'invoice_apm': True}</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('th_account_move', '=', 'apm'),('move_type', '=', 'in_invoice')]</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('account.view_out_invoice_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('account.view_move_form')}),
            ]"/>
    </record>
</odoo>