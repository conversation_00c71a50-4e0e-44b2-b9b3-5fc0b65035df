import json

from odoo import api, models, fields, _


class ThClipboard(models.Model):

    _name = "th.clipboard"
    _description = "Bảng tạm"

    @api.depends('th_data')
    def _get_th_data_str(self):
        for record in self:
            record.th_data_str = json.dumps(record.th_data, indent=4, sort_keys=True)

    @api.depends('th_data_send')
    def _get_th_data_send_str(self):
        for record in self:
            record.th_data_send_str = json.dumps(record.th_data_send, indent=4, sort_keys=True)

    th_method = fields.Char("Phương thức")
    th_type_sync = fields.Selection(selection=[('an_hour', '1 giờ 1 lần'), ('ontime', 'Đồng bộ ngay'),
                                               ('a_day', '1 ngày 1 lần')], default='ontime')
    th_data = fields.Json("Dữ liệu")
    th_data_str = fields.Text(string='Data', compute='_get_th_data_str', readonly=1)
    th_data_send = fields.Json("Dữ liệu gửi đi")
    th_data_send_str = fields.Text(string='Data Send', compute='_get_th_data_send_str', readonly=1)
    th_status = fields.Selection(selection=[('waiting', 'Chờ'), ('pending', 'Đang xử lý'),
                                            ('error', 'Lỗi'), ('success', 'Thành công')], default='waiting')
    th_request_time = fields.Datetime("Thời gian phản hồi request")
    th_response_data = fields.Char("Response")
    th_response_time = fields.Datetime("Thời gian phản hồi response")
    th_mapping_id = fields.Many2one(comodel_name='th.mapping.id', string='Bảng map id')
    name = fields.Char('Tên', related='th_mapping_id.name')
    th_model_name = fields.Char("Tên model", related='th_mapping_id.th_model_name', store=True)
    th_module_id = fields.Many2one(comodel_name='therp.module', string='Phân hệ', related='th_mapping_id.th_module_id')
    th_internal_id = fields.Integer(string="ID hệ thống nội bộ", related='th_mapping_id.th_internal_id', store=True)
    th_system = fields.Selection(string="Hệ thống", related='th_mapping_id.th_system')
    th_url = fields.Char(string="Url")
