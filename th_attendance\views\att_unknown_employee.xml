<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="th_att_unknown_employee_view_tree" model="ir.ui.view">
            <field name="name">th_att_unknown_employee_view_tree</field>
            <field name="model">th.att.unknown.employee</field>
            <field name="arch" type="xml">
                <tree string="th_unknown_tree" create="0" export_xlsx="0">
                    <field name="th_pin"/>
                    <field name="th_attendance_date"/>
                    <field name="th_checkin_time"/>
                    <field name="th_checkout_time"/>
                    <field name="active" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="th_att_unknown_employee_view_search" model="ir.ui.view">
            <field name="name">th_att_unknown_employee_view_search</field>
            <field name="model">th.att.unknown.employee</field>
            <field name="arch" type="xml">
                <search string="">
                    <field name="th_pin"/>
                    <field name="th_emp_name"/>
                    <group string="Group By">
                        <filter string="Employee" name="group_by_employee" context="{'group_by': 'th_emp_name'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="th_att_unknown_employee_action" model="ir.actions.act_window">
            <field name="name">Attendance Unknown Employee</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">th.att.unknown.employee</field>
            <field name="context">{'search_default_group_by_employee': 1}</field>
            <field name="view_mode">tree</field>
        </record>
    </data>
</odoo>