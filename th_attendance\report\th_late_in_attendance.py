from odoo import fields, models, api
from datetime import date
from dateutil.relativedelta import relativedelta
from odoo.exceptions import ValidationError


class ThLateInAttendence(models.TransientModel):
    _name = "report.th_attendance.th_late_in_attendance"
    _description = "Báo cáo đi muộn"
    _inherit = 'report.report_xlsx.abstract'


    th_date_from = fields.Date(string="Từ ngày")
    th_date_to = fields.Date(string="Đến ngày")

    def action_generate_excel_report(self):
        return self.env.ref('th_attendance.th_late_in_attendance_act_report_xlsx').report_action(self)

    @api.constrains('th_date_from', 'th_date_to')
    def _check_date_range(self):
        for rec in self:
            if rec.th_date_to and rec.th_date_from and rec.th_date_to < rec.th_date_from:
                raise ValidationError("Ngày kết thúc không thể nhỏ hơn ngày bắt đầu.")
            if not rec.th_date_from or not rec.th_date_to:
                raise ValidationError("Vui lòng điền vào cả hai trường 'Từ ngày' và 'Đến ngày'.")

    def generate_xlsx_report(self, workbook, data, partners):
        late_in_attendance = workbook.add_worksheet('Chấm công muộn')
        header_format = workbook.add_format(
            {'bold': True, 'font_name': 'Times New Roman', 'font_size': 11, 'align': 'center', 'valign': 'vcenter',
             'text_wrap': True, 'border': 1})
        title_format = workbook.add_format(
            {'bold': True, 'font_name': 'Times New Roman', 'font_size': 12, 'align': 'center', 'valign': 'vcenter',
             'text_wrap': True})
        normal_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'num_format': '@', 'right': 1, 'bottom': 3})
        date_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'num_format': 'dd/mm/yyyy hh:mm:ss', 'right': 1,
             'bottom': 3})
        format2 = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'align': 'center', 'valign': 'vcenter', 'right': 1, 'bottom': 3})

        # Generate header
        late_in_attendance.merge_range("A1:F1", "Chi tiết chấm công muộn", title_format)
        late_in_attendance.merge_range("A2:F2", f"{partners.th_date_from.strftime('%d/%m/%Y')} đến {partners.th_date_to.strftime('%d/%m/%Y')}", title_format)
        late_in_attendance.write(3, 0, 'Phòng ban:', header_format)
        late_in_attendance.write(3, 1, 'Tên N.Viên:', header_format)
        late_in_attendance.write(3, 2, 'Mã Chấm công', header_format)
        late_in_attendance.write(3, 3, 'Giờ vào', header_format)
        late_in_attendance.write(3, 4, 'Số lần chấm công', header_format)
        late_in_attendance.write(3, 5, 'Thời gian được đi muộn (phút)', header_format)

        # Generate data online
        start_row_index = 4
        time_format = workbook.add_format(
            {'font_name': 'Times New Roman', 'font_size': 11, 'num_format': 'hh:mm', 'right': 1, 'bottom': 3})

        attendance_type = {'two_times': '2 lần', 'four_times': '4 lần'}
        for department in self.env['hr.department'].search([]):
            if department.id == 763:
                print('a')
            num_der = 0
            start_row_der = start_row_index
            start_row_em = start_row_index + 1
            start_row_rq = start_row_em + 1
            member_number = department.member_ids
            late_em = 0
            num_der_late = 0
            for employee in department.member_ids:
                late_att = self.env['hr.attendance'].search([('employee_id', '=', employee.id), ('check_in', '>=', partners.th_date_from),
                     ('check_out', '<=', partners.th_date_to), ('state', '=', 'approved'), ('th_is_late_in', '=', True)], order='check_in asc')
                date_late = []
                num_em = len(late_att)
                num_em_late = 0
                num_der += num_em
                for request in late_att:
                    employee = request.sudo().employee_id
                    late_in_attendance.write(start_row_rq, 0, "", normal_format)
                    late_in_attendance.write(start_row_rq, 1, "", normal_format)
                    late_in_attendance.write(start_row_rq, 2, employee.pin if employee.pin else "", normal_format)
                    late_in_attendance.write(start_row_rq, 3, request.check_in + relativedelta(hours=7) if request.check_in else "", date_format)
                    late_in_attendance.write(start_row_rq, 4,  attendance_type.get(employee.th_attendance_type) if attendance_type.get(employee.th_attendance_type) else "", format2)
                    late_in_attendance.write(start_row_rq, 5,  employee.th_spec_att_id.th_time_is_late if employee.th_spec_att_id else "", format2)
                    start_row_rq += 1
                    if request.th_attendance_date not in date_late:
                        date_late.append(request.th_attendance_date)
                        num_em_late +=1
                    else:
                        if request.th_over_time:
                            num_em_late += 1
                member_number -= employee
                start_row_rq += 1 if len(member_number) > 0 and num_em>0 else 0
                num_der_late += num_em_late
                if num_em>0:
                    late_in_attendance.write(start_row_em, 0, "", normal_format)
                    late_in_attendance.write(start_row_em, 1, F'{employee.name} ({num_em_late})' if employee else "", normal_format)
                    late_in_attendance.write(start_row_em, 2, "", normal_format)
                    late_in_attendance.write(start_row_em, 3, "", normal_format)
                    late_in_attendance.write(start_row_em, 4, "", format2)
                    late_in_attendance.write(start_row_em, 5, "", format2)
                    start_row_em += 1 if num_em == 0 else (num_em + 1)
                    late_em +=1
            if num_der>0:
                late_in_attendance.write(start_row_der, 0, F'{department.name} ({num_der_late})' if department else "", header_format)
                late_in_attendance.write(start_row_der, 1, "", normal_format)
                late_in_attendance.write(start_row_der, 2, "", normal_format)
                late_in_attendance.write(start_row_der, 3, "", normal_format)
                late_in_attendance.write(start_row_der, 4, "", format2)
                late_in_attendance.write(start_row_der, 5, "", format2)
                start_row_index += late_em + (num_der if num_der != 0 else 0) + 1

            # Format Excel
        late_in_attendance.set_column("A:A", 41)
        late_in_attendance.set_column("B:B", 25)
        late_in_attendance.set_column("C:C", 10)
        late_in_attendance.set_column("D:D", 20)
        late_in_attendance.set_column("E:E", 10)
        late_in_attendance.set_column("F:F", 10)

