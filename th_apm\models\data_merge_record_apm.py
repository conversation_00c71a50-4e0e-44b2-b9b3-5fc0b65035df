from odoo import models, api, fields, _


class DataMergeRecord(models.Model):
    _inherit = 'data_merge.record'

    th_user_id = fields.Char(compute='_compute_fields',string='Người chăm sóc')
    is_sale_order_web = fields.Boolean(string='<PERSON>ó đơn hàng từ web')
    th_ownership_unit_apm = fields.Char(string='Đơn vị sở hữu')
    th_stage_id = fields.Many2one(comodel_name="th.apm.level", string="Mối quan hệ")
    @api.depends('res_model_name', 'res_id')
    def _compute_fields(self):
        super()._compute_fields()
        for rec in self:
            rec.th_user_id = False
            rec.th_ownership_unit_apm = False
            if rec.res_model_name == 'th.apm':
                apm_id = self.env['th.apm'].search([('id', '=', rec.res_id)])
                rec.th_user_id = apm_id.th_user_id.name
                rec.is_sale_order_web = True if apm_id.th_campaign_id.id == self.env.ref(
                    'th_apm.campaign_lead_auto').id else False
                rec.th_ownership_unit_apm = apm_id.th_ownership_unit_id.name
                rec.th_stage_id = apm_id.th_stage_id.id

