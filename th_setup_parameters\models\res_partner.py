from odoo import fields,models, api, _
from odoo.exceptions import ValidationError
import ast

class ResPartner(models.Model):
    _inherit = "res.partner"

    def _th_domain_place_of_birth(self):
        state_id = self.env['res.country.state'].search([]).filtered(lambda u: u.country_id == self.env.ref('base.vn'))
        return [('id', 'in', state_id.ids)]

    # tracking các trường có sẵn
    name = fields.Char(index=True, default_export_compatible=True, tracking=True)
    title = fields.Many2one('res.partner.title', tracking=True)
    function = fields.Char(string='Job Position', tracking=True)
    street = fields.Char(tracking=True)
    street2 = fields.Char(tracking=True)
    city = fields.Char(tracking=True)
    state_id = fields.Many2one("res.country.state", string='State', ondelete='restrict', domain="[('country_id', '=?', country_id)]", tracking=True)
    country_id = fields.Many2one('res.country', string='Country', ondelete='restrict', tracking=True)

    # th_university_id = fields.Many2one(comodel_name="th.university", string="Trường học")
    th_origin_id = fields.Many2one(comodel_name="th.origin", string="Trường học")
    th_ward_id = fields.Many2one(comodel_name='th.country.ward', string='Phường/ Xã', domain="[('th_district_id', '=?', th_district_id), ('th_district_id.th_state_id', '=?', state_id)]", tracking=True)
    th_district_id = fields.Many2one(comodel_name='th.country.district', string='Quận/ Huyện', domain="[('th_state_id', '=?', state_id)]", tracking=True)

    th_customer_code = fields.Char(string="Mã Khách Hàng", tracking=True, copy=False)
    th_affiliate_code = fields.Char(string="Mã Tiếp Thị Liên Kết", tracking=True, copy=False)

    th_gender = fields.Selection(string="Giới tính", selection=[('male', 'Nam'), ('female', 'Nữ'), ('other', 'Khác'), ], tracking=True)
    th_birthday = fields.Date(string="Ngày sinh", tracking=True)
    th_place_of_birth_id = fields.Many2one(comodel_name="res.country.state", string="Nơi sinh", tracking=True)

    th_ethnicity_id = fields.Many2one(comodel_name="th.ethnicity", string="Dân tộc", tracking=True)
    th_religion_id = fields.Many2one(comodel_name="th.religion", string="Tôn giáo", tracking=True)

    th_phone2 = fields.Char(string="Số điện thoại 2", tracking=True)
    th_citizen_identification = fields.Char(string="Số CMT/ CCCD", tracking=True)
    th_date_identification = fields.Date(string="Ngày cấp CMT/ CCCD", tracking=True)
    th_place_identification = fields.Char(string="Nơi cấp CMT/ CCCD", tracking=True)
    th_citizen_identification_image = fields.Many2many(comodel_name="ir.attachment", string="Tệp đính kèm", tracking=True)
    th_module_ids = fields.Many2many(comodel_name="therp.module", string="Module")

    th_street = fields.Char(string="Địa chỉ (Hộ khẩu)", tracking=True)
    th_ward_permanent_id = fields.Many2one(comodel_name='th.country.ward', string='Phường/ Xã (Hộ khẩu)',
                                 domain="[('th_district_id', '=?', th_district_permanent_id), ('th_district_id.th_state_id', '=?', th_state_id)]",
                                 tracking=True)
    th_district_permanent_id = fields.Many2one(comodel_name='th.country.district', string='Quận/ Huyện (Hộ khẩu)',
                                     domain="[('th_state_id', '=?', th_state_id)]", tracking=True)
    th_state_id = fields.Many2one("res.country.state", string='Tỉnh/ TP (Hộ khẩu)', ondelete='restrict',
                                  domain="[('country_id', '=?', th_country_id)]", tracking=True)
    th_country_id = fields.Many2one('res.country', string='Quốc gia (Hộ khẩu)', ondelete='restrict', tracking=True)
    th_module_name = fields.Char(string="Tên module", copy=False)
    th_type = fields.Char()
    th_check_module = fields.Boolean(default=False, copy=False)

    @api.model
    def _action_process_check_module(self):
        crm_module = self.env.ref('th_setup_parameters.th_crm_module', raise_if_not_found=False)
        srm_module = self.env.ref('th_setup_parameters.th_srm_module', raise_if_not_found=False)

        if not crm_module or not srm_module:
            return

        # Lấy partner và thông tin về các liên kết module
        query = """
            SELECT p.id, crm.id AS crm_lead_id, crm.active AS crm_active,
                         srm.id AS srm_lead_id
            FROM res_partner p
            LEFT JOIN crm_lead crm ON crm.partner_id = p.id
            LEFT JOIN th_student srm ON srm.th_partner_id = p.id
            WHERE p.th_check_module = TRUE
        """
        self.env.cr.execute(query)
        result = self.env.cr.fetchall()

        crm_with_opportunity, crm_without_opportunity = set(), set()
        srm_with_opportunity, srm_without_opportunity = set(), set()

        for partner_id, crm_lead_id, crm_active, srm_lead_id in result:
            # CRM module
            if crm_lead_id and crm_active:
                crm_with_opportunity.add(partner_id)
            else:
                crm_without_opportunity.add(partner_id)

            # SRM module
            if srm_lead_id:
                srm_with_opportunity.add(partner_id)
            else:
                srm_without_opportunity.add(partner_id)

        partner = self.env['res.partner']

        # Cập nhật module CRM
        if crm_with_opportunity:
            partner.browse(crm_with_opportunity).sudo().write({'th_module_ids': [(4, crm_module.id)]})
        if crm_without_opportunity:
            partner.browse(crm_without_opportunity).sudo().write({'th_module_ids': [(3, crm_module.id)]})

        # Cập nhật module SRM
        if srm_with_opportunity:
            partner.browse(srm_with_opportunity).sudo().write({'th_module_ids': [(4, srm_module.id)]})
        if srm_without_opportunity:
            partner.browse(srm_without_opportunity).sudo().write({'th_module_ids': [(3, srm_module.id)]})

    def th_get_name_module_partner(self):
        partner = self.search([('th_module_ids', '!=', False), ('th_module_name', '!=', True)], order="id")
        list_partner = [partner[i:i + 100] for i in range(0, len(partner), 100)]
        for i in range(3):
            list_partner[i].with_delay().th_write_module_name()

    def th_write_module_name(self):
        for part in self:
            # print(part.th_module_name)
            part.write({'th_module_name': ', '.join(part.th_module_ids.mapped('name')) if part.th_module_ids else False})

    # @api.constrains('phone', 'email', 'th_phone2')
    # def _check_duplicate_values(self):
    #     check_partner = self.env['ir.config_parameter'].sudo().get_param('th_check_partner')
    #     if not check_partner:
    #         return
    #     for rec in self:
    #         ex_phone = ex_email = self.env['res.partner']
    #         val_phone = val_email = False
    #         if rec.phone:
    #             val_phone = self.search([('id', '!=', rec.id), '|', ('phone', '=', rec.phone), ('th_phone2', '=', rec.phone)])
    #             domain = [
    #                 ('id', '!=', rec.id), '|',
    #                 ('phone', '=', rec.phone if rec.phone else ' '),
    #                 ('th_phone2', '=', rec.phone if rec.phone else ' '),
    #             ]
    #             ex_phone = self.search(domain)
    #         if rec.email:
    #             val_email = self.search([('id', '!=', rec.id), ('email', '=', rec.email)])
    #             domain = [
    #                 ('id', '!=', rec.id),
    #                 ('email', '=', rec.email if rec.email else ' '),
    #             ]
    #             ex_email = self.search(domain)
    #         if check_partner and ex_phone and not ex_email:
    #             raise ValidationError(_("Trùng số điện thoại '%s' với tên liên hệ '%s'") % (rec.phone, ', '.join(val_phone.mapped('name'))))
    #         elif check_partner and not ex_phone and ex_email:
    #             raise ValidationError(_("Trùng email '%s' với tên liên hệ là '%s'") % (rec.email,  ', '.join(val_email.mapped('name'))))
    #         elif check_partner and ex_phone and ex_email:
    #             raise ValidationError(_("Trùng email '%s' với liên hệ '%s' và trùng số điện thoại '%s' với liên hệ '%s'") % (rec.email,  ', '.join(val_email.mapped('name')), rec.phone, ', '.join(val_phone.mapped('name'))))
    #         ex_partner = self.env['res.partner'].search([('id', '!=', rec.id),'|', ('phone', '=', rec.th_phone2), ('th_phone2', '=', rec.th_phone2),])
    #         if check_partner and rec.th_phone2 and ex_partner:
    #             raise ValidationError("Số điện thoại 2 đã bị trùng với số điện thoại 1 hoặc liên hệ khác !")

    @api.constrains('phone')
    def _check_duplicate_values_phone(self):
        check_partner = self.env['ir.config_parameter'].sudo().get_param('th_check_partner')
        check_module = self.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')
        th_users_ids = self.env['ir.config_parameter'].sudo().get_param('th_pass_user_ids', default='[]')

        if not check_partner:
            return

        user_ids_list = ast.literal_eval(th_users_ids)
        if self.env.user.id in user_ids_list:
            return

        for rec in self:
            ex_phone = self.env['res.partner']
            if rec.phone:
                domain = [
                    ('id', '!=', rec.id), '|',
                    ('phone', '=', rec.phone if rec.phone else ' '),
                    ('th_phone2', '=', rec.phone if rec.phone else ' '),
                ]
                if check_module:
                    if rec.th_module_ids:
                        domain.append(('th_module_ids', 'in', rec.th_module_ids.ids))
                ex_phone = self.search(domain)

            if check_partner and ex_phone:
                raise ValidationError(_("Trùng số điện thoại '%s' với tên liên hệ '%s'") % (rec.phone, ', '.join(ex_phone.mapped('name'))))

    @api.constrains('th_phone2')
    def _check_duplicate_values_th_phone2(self):
        check_partner = self.env['ir.config_parameter'].sudo().get_param('th_check_partner')
        check_module = self.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')
        th_users_ids = self.env['ir.config_parameter'].sudo().get_param('th_pass_user_ids', default='[]')
        if not check_partner:
            return

        user_ids_list = ast.literal_eval(th_users_ids)
        if self.env.user.id in user_ids_list:
            return

        for rec in self:
            ex_phone = self.env['res.partner']

            if rec.th_phone2:
                domain = [
                    ('id', '!=', rec.id), '|',
                    ('phone', '=', rec.th_phone2 if rec.th_phone2 else ' '),
                    ('th_phone2', '=', rec.th_phone2 if rec.th_phone2 else ' '),
                ]
                if check_module:
                    if rec.th_module_ids:
                        domain.append(('th_module_ids', 'in', rec.th_module_ids.ids))
                ex_phone = self.search(domain)
            if check_partner and rec.th_phone2 and ex_phone:
                raise ValidationError("Số điện thoại 2 đã bị trùng với số điện thoại 1 hoặc liên hệ khác !")

    @api.constrains('email')
    def _check_duplicate_values_email(self):
        check_partner = self.env['ir.config_parameter'].sudo().get_param('th_check_partner')
        check_module = self.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')
        th_users_ids = self.env['ir.config_parameter'].sudo().get_param('th_pass_user_ids', default='[]')


        if not check_partner:
            return


        user_ids_list = ast.literal_eval(th_users_ids)
        if self.env.user.id in user_ids_list:
            return

        for rec in self:
            ex_email = self.env['res.partner']
            val_email = False
            if rec.email:
                val_email = self.search([('id', '!=', rec.id), ('email', '=', rec.email)])
                domain = [
                    ('id', '!=', rec.id),
                    ('email', '=', rec.email if rec.email else ' '),
                ]
                if check_module:
                    if rec.th_module_ids:
                        domain.append(('th_module_ids', 'in', rec.th_module_ids.ids))
                ex_email = self.search(domain)

            if check_partner and ex_email:
                raise ValidationError(
                    _("Trùng email '%s' với tên liên hệ là '%s'") % (rec.email, ', '.join(val_email.mapped('name'))))


    @api.onchange('th_ward_id')
    def onchange_th_ward_id(self):
        if self.th_ward_id:
            self.th_district_id = self.th_ward_id.th_district_id.id
            self.state_id = self.th_district_id.th_state_id.id
            self.country_id = self.state_id.country_id.id

    @api.onchange('th_district_id')
    def onchange_th_district_id(self):
        if self.th_district_id:
            self.state_id = self.th_district_id.th_state_id.id
            self.country_id = self.state_id.country_id.id
        if self.th_district_id != self.th_ward_id.th_district_id:
            self.th_ward_id = False

    @api.onchange('country_id')
    def onchange_th_country_id(self):
        if self.country_id != self.state_id.country_id:
            self.state_id = False

    @api.onchange('state_id')
    def onchange_th_state_id(self):
        if self.state_id:
            self.country_id = self.state_id.country_id.id
        if self.state_id != self.th_district_id.th_state_id:
            self.th_district_id = False

    @api.onchange('th_ward_permanent_id')
    def onchange_th_ward_permanent_id(self):
        if self.th_ward_permanent_id:
            self.th_district_permanent_id = self.th_ward_permanent_id.th_district_id.id
            self.th_state_id = self.th_district_permanent_id.th_state_id.id
            self.th_country_id = self.th_state_id.country_id.id

    @api.onchange('th_district_permanent_id')
    def onchange_th_district_permanent_id(self):
        if self.th_district_permanent_id:
            self.th_state_id = self.th_district_permanent_id.th_state_id.id
            self.th_country_id = self.th_state_id.country_id.id
        if self.th_district_permanent_id != self.th_ward_permanent_id.th_district_id:
            self.th_ward_permanent_id = False

    @api.onchange('th_country_id')
    def onchange_th_country_permanent_id(self):
        if self.th_country_id != self.th_state_id.country_id:
            self.th_state_id = False

    @api.onchange('th_state_id')
    def onchange_th_state_permanent_id(self):
        if self.th_state_id:
            self.th_country_id = self.th_state_id.country_id.id
        if self.th_state_id != self.th_district_permanent_id.th_state_id:
            self.th_district_permanent_id = False

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if not vals.get('th_customer_code', False) or not vals.get('th_affiliate_code', False):
                vals['th_customer_code'] = self.env['ir.sequence'].next_by_code('customer.code')
                vals['th_affiliate_code'] = self.env['ir.sequence'].next_by_code('affiliate.code')
            if vals.get('phone', False):
                vals['phone'] = vals['phone'].replace(" ", "")
        return super(ResPartner, self).create(vals_list)


    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        args = args or []
        if name:
            # Be sure name_search is symetric to name_get
            name = name.split(' / ')[-1]
            args = ['|',('name', operator, name),('phone', operator, name)] + args
        return self._search(args, limit=limit, access_rights_uid=name_get_uid)

    @api.model
    def th_update_customer_code_in_records(self, th_models=None):
        for th_model in th_models or []:
            model2 = self.env[th_model]
            partner_id = ''
            for field_name, field in model2._fields.items():
                if isinstance(field, fields.Many2one) and field.comodel_name == 'res.partner':
                    partner_id = field_name
                    break
            # for rec in model2.search([('th_customer_code_aum', '=', False), (partner_id, '!=', False)]) if partner_id else []:
            #     rec.th_customer_code_aum = rec.partner_id.th_customer_code
            if partner_id:
                datas = model2.search([('th_customer_code_aum', '=', False), (partner_id, '!=', False)])
                chunk_list = [datas[i:i + 100] for i in range(0, len(datas), 100)]
                for chunk in chunk_list:
                    data = self.sudo().with_delay(priority=20).th_update_customer_code(chunk, partner_id)

    def th_update_customer_code(self, data=None, partner_id=''):
        for rec in data or []:
            if partner_id == 'partner_id':
                rec.sudo().write({'th_customer_code_aum': rec.partner_id.th_customer_code})

            elif partner_id == 'th_partner_id':
                rec.sudo().write({'th_customer_code_aum': rec.th_partner_id.th_customer_code})
