from odoo import api, fields, models, _
from ast import literal_eval


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    th_l_a_manager_ids = fields.Many2many('res.users', string='<PERSON><PERSON><PERSON><PERSON> du<PERSON> lần 2')

    def _domain_th_l_a_manager_ids(self):
        return []

    def set_values(self):
        super().set_values()
        self.env['ir.config_parameter'].set_param('th_l_a_manager_ids', self.th_l_a_manager_ids.ids)

    @api.model
    def get_values(self):
        res = super().get_values()
        th_l_a_manager_ids = self.env['ir.config_parameter'].sudo().get_param('th_l_a_manager_ids')
        if th_l_a_manager_ids:
            res.update(th_l_a_manager_ids=[(6, 0, eval(th_l_a_manager_ids))])
        return res