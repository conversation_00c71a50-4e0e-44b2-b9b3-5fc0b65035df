from odoo import models, _

class AttUnknownEmpXlsx(models.TransientModel):
    _name = 'report.th_attendance.att_unknown_emp_xlsx'
    _description = "Att Unknown Employee Xlsx"
    _inherit = 'report.report_xlsx.abstract'

    def generate_xlsx_report(self, workbook, data, partners):
        for obj in partners:
            sheet = workbook.add_worksheet('sheet1')
            self.generate_header(workbook, sheet)
            self.generate_data(workbook, sheet)
            self.format_xlsx_sheet(sheet)

    def generate_header(self, workbook, sheet):
        big_header = workbook.add_format({'bold': True, 'font_name': 'Times New Roman', 'font_size': 22})
        sub_header = workbook.add_format({'bold': True, 'font_name': 'Times New Roman', 'font_size': 13, 'border': 1})
        sheet.merge_range('A1:C1', 'Danh sách nhân viên không xác định', big_header)
        sheet.write(1, 0, "Stt", sub_header)
        sheet.write(1, 1, "Mã <PERSON>", sub_header)
        sheet.write(1, 2, "<PERSON>ê<PERSON>", sub_header)

    def generate_data(self, workbook, sheet):
        row_index = 2
        exist_pin = {e.pin for e in self.env['hr.employee'].sudo().search([('pin', '!=', False)])}
        all_unknown_employee = self.env['th.att.unknown.employee'].sudo().search([])
        all_unknown_pin = set(all_unknown_employee.mapped('th_pin'))
        data_format = workbook.add_format({'font_name': 'Times New Roman', 'font_size': 13, 'num_format': '@', 'right': 1, 'bottom': 3})
        for pin in all_unknown_pin:
            if pin not in exist_pin:
                sheet.write(row_index, 0, row_index - 1, data_format)
                sheet.write(row_index, 1, pin, data_format)
                sheet.write(row_index, 2, all_unknown_employee.filtered(lambda l: l.th_pin == pin)[0].th_emp_name, data_format)
                row_index += 1

    def format_xlsx_sheet(self, sheet):
        sheet.set_column(0, 0, 11)
        sheet.set_column(1, 1, 22)
        sheet.set_column(2, 2, 35)

    def action_print_report_excel(self):
        res = self.sudo().create({})
        return res.id