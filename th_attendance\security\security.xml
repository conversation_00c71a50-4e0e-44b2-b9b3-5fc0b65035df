<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_attendance.group_hr_attendance_kiosk" model="res.groups">
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="hr_attendance_rule_base_group_user" model="ir.rule">
        <field name="name">Internal User: own attendance requests</field>
        <field name="model_id" ref="model_th_attendance_request"/>
        <field name="domain_force">['|',('th_employee_id.user_id', '=', user.id),('th_employee_id.parent_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(6,0, [ref('base.group_user')])]"/>
    </record>

    <record id="hr_attendance_rule_group_attendance_user" model="ir.rule">
        <field name="name">Attendance User: own attendance requests</field>
        <field name="model_id" ref="model_th_attendance_request"/>
        <field name="domain_force">['|',('th_employee_id.user_id', '=', user.id),('th_employee_id.parent_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(6,0, [ref('hr_attendance.group_hr_attendance_user')])]"/>
    </record>

    <record id="hr_attendance_rule_group_hr_attendance" model="ir.rule">
        <field name="name">Officer: own attendance requests</field>
        <field name="model_id" ref="model_th_attendance_request"/>
        <field name="domain_force">['|',('th_employee_id.user_id', '=', user.id),('th_employee_id.parent_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(6,0, [ref('hr_attendance.group_hr_attendance')])]"/>
    </record>

    <record id="hr_attendance_rule_attendance_request_manager" model="ir.rule">
        <field name="name">attendance requests officer: full access</field>
        <field name="model_id" ref="model_th_attendance_request"/>
        <field name="domain_force">[(1,'=',1)]</field>
        <field name="groups" eval="[(6,0, [ref('hr_attendance.group_hr_attendance_manager')])]"/>
    </record>

    <record id="hr_attendance.menu_hr_attendance_root" model="ir.ui.menu">
        <field name="groups_id" eval="[(6,0, [ref('hr_attendance.group_hr_attendance')])]"/>
    </record>

    <record id="th_attendance_rule_attendance_administrator" model="ir.rule">
        <field name="name">attendance administrator: full access</field>
        <field name="model_id" ref="model_hr_attendance"/>
        <field name="domain_force">[(1,'=',1)]</field>
        <field name="groups" eval="[(4,ref('hr_attendance.group_hr_attendance_manager'))]"/>
    </record>

    <!--Override hr_attendance_rule_attendance_manager core's rule-->
    <function name="write" model="ir.model.data">
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'hr_attendance'), ('name', '=', 'hr_attendance_rule_attendance_manager')]"/>
            </function>
            <value eval="{'noupdate': False}"/>
    </function>

    <record id="hr_attendance.hr_attendance_rule_attendance_manager" model="ir.rule">
        <field name="domain_force">['|',('employee_id.user_id', '=', user.id),('employee_id.parent_id.user_id', '=', user.id)]</field>
    </record>

    <function name="write" model="ir.model.data">
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'hr_attendance'), ('name', '=', 'hr_attendance_rule_attendance_manager')]"/>
            </function>
            <value eval="{'noupdate': True}"/>
    </function>

    <function name="write" model="ir.model.data">
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'hr_attendance'), ('name', '=', 'hr_attendance_rule_attendance_manual')]"/>
            </function>
            <value eval="{'noupdate': False}"/>
    </function>

    <record id="hr_attendance.hr_attendance_rule_attendance_manual" model="ir.rule">
        <field name="domain_force">['|',('employee_id.user_id', '=', user.id),('employee_id.parent_id.user_id', '=', user.id)]</field>
    </record>

    <function name="write" model="ir.model.data">
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'hr_attendance'), ('name', '=', 'hr_attendance_rule_attendance_manual')]"/>
            </function>
            <value eval="{'noupdate': True}"/>
    </function>

    <function name="write" model="ir.model.data">
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'hr_attendance'), ('name', '=', 'hr_attendance_rule_attendance_employee')]"/>
            </function>
            <value eval="{'noupdate': False}"/>
    </function>

    <record id="hr_attendance.hr_attendance_rule_attendance_employee" model="ir.rule">
        <field name="domain_force">['|',('employee_id.user_id', '=', user.id),('employee_id.parent_id.user_id', '=', user.id)]</field>
    </record>

    <function name="write" model="ir.model.data">
            <function name="search" model="ir.model.data">
                <value eval="[('module', '=', 'hr_attendance'), ('name', '=', 'hr_attendance_rule_attendance_employee')]"/>
            </function>
            <value eval="{'noupdate': True}"/>
    </function>

    <record id="group_closing_the_attacks" model="res.groups">
        <field name="name">Chấm công : Chốt công</field>
        <field name="category_id" ref="base.module_category_hidden"/>
        <field name="implied_ids" eval="[(4, ref('hr_attendance.group_hr_attendance_user'))]"/>
    </record>
</odoo>
