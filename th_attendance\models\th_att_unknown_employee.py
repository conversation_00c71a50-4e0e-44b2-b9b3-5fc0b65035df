# -*- coding: utf-8 -*-
from odoo import api, fields, models, _, exceptions
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta
import json


class AttendanceUnknownEmployee(models.Model):
    _name = "th.att.unknown.employee"
    _description = "Attendance Unknown Employee"
    _order = "th_pin asc"

    th_pin = fields.Char('Pin')
    th_emp_name = fields.Char('Employee name')
    th_attendance_date = fields.Date('Attendance date')
    th_checkin_time = fields.Char('Check-in Time')
    th_checkout_time = fields.Char('Check-out Time')
    active = fields.<PERSON><PERSON><PERSON>('Active', default=True)

    def get_exists_attendance(self, employee_id, check_in, check_out):
        """
        Input: employee_id, check_in and check-out (datetime)
        Output (2 cases): -If exists attendance record with input data : return record found
                          -If doesn't exists attendance record with input data : return False
        """
        employee_att = self.env['hr.attendance'].sudo().search([('employee_id', '=', employee_id)])
        result = False
        if check_in and check_out:
            result = employee_att.filtered(lambda l: l.check_in == check_in and l.check_out == check_out)
        if not result and check_in:
            result = employee_att.filtered(lambda l: l.check_in == check_in)
        if not result and check_out:
            result = employee_att.filtered(lambda l: l.check_out == check_out)
        return result[0] if result else False

    def action_regenerate_attendance(self):
        try:
            self.regenerate_attendance()
        except ValidationError as e:
            raise ValidationError(e)
        except Exception as e:
            raise ValidationError(_("There was an error while re-generate attendance, please contact the administrator!"))

    def regenerate_attendance(self):
        for rec in self.env['th.att.unknown.employee'].search([]):
            employee_pin = rec.th_pin
            employee_name = rec.th_emp_name
            employee_id = self.env['hr.employee'].search([('pin', '=', employee_pin)]).id
            attendance_date = rec.th_attendance_date
            checkin_time = rec.th_checkin_time
            checkout_time = rec.th_checkout_time

            if not employee_id:
                continue

            timezone_hour = timedelta(hours=7)
            check_in, check_out = False, False
            if checkin_time:
                checkin_str = "%s %s" % (attendance_date, checkin_time)
                check_in = datetime.strptime(checkin_str, '%Y-%m-%d %H:%M') - timezone_hour
            if checkout_time:
                checkout_str = "%s %s" % (attendance_date, checkout_time)
                check_out = datetime.strptime(checkout_str, '%Y-%m-%d %H:%M') - timezone_hour
            exists_att = self.get_exists_attendance(employee_id, check_in, check_out)
            if exists_att:
                if not exists_att.check_in:
                    exists_att.write({'check_in': check_in})
                if not exists_att.check_out:
                    exists_att.write({'check_out': check_out})
                rec.unlink()
                continue
            self.env['hr.attendance'].create({
                'employee_id': employee_id,
                'check_in': check_in,
                'check_out': check_out,
            })
            rec.unlink()
