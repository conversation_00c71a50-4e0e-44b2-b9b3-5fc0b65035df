<odoo>
    <record id="th_late_in_attendance_act_report_xlsx" model="ir.actions.report">
        <field name="name">Late In Attendance</field>
        <field name="model">report.th_attendance.th_late_in_attendance</field>
        <field name="report_type">xlsx</field>
        <field name="report_name">th_attendance.th_late_in_attendance</field>
        <field name="report_file">th_attendance.th_late_in_attendance</field>
        <field name="print_report_name">"Báo cáo chấm công muộn"</field>
        <field name="binding_type">report</field>
    </record>


    <record id="th_late_in_attendance_view_form" model="ir.ui.view">
        <field name="name">th_late_in_attendance_view_form</field>
        <field name="model">report.th_attendance.th_late_in_attendance</field>
        <field name="arch" type="xml">
            <form string="">
                <sheet>
                    <group>
                        <label for="th_date_from"/>
                        <div class="o_row">
                            <field name="th_date_from"/> <span> <PERSON><PERSON>n ng<PERSON></span> <field name="th_date_to"/>
                        </div>
                    </group>
                </sheet>
                <footer>
                    <button name="action_generate_excel_report" string="Xuất" type="object" class="btn btn-primary"/>
                    <button string="Hủy" special="cancel" class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="th_late_in_attendance_view_act" model="ir.actions.act_window">
        <field name="name">Báo cáo chấm công muộn</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">report.th_attendance.th_late_in_attendance</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

</odoo>