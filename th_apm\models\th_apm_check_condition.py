from odoo import models, api, fields, _
from odoo.exceptions import ValidationError

class THApmCheckCondition(models.Model):
    _name = "th.apm.check.condition"
    _description = "Đ<PERSON><PERSON>u kiện check trùng lead"

    name = fields.Char(string="<PERSON>ên điều kiện", compute="compute_name_check_condition", default="Mới")
    th_date_from = fields.Integer(string="Số ngày liên hệ lần cuối từ")
    th_date_to = fields.Integer(string="Đến")
    th_apm_level_id = fields.Many2one(comodel_name="th.apm.level", string="<PERSON><PERSON><PERSON> quan hệ")
    th_status_detail_id = fields.Many2one(comodel_name="th.status.detail", domain="[('th_type', '=', 'apm')]", string="Tình trạng gọi")
    th_result = fields.Selection(selection=[('keep', 'Giữ'), ('transfer', 'Chuyển')], string="<PERSON><PERSON><PERSON> qu<PERSON>ử lý")
    th_origin_ids = fields.Many2many(comodel_name='th.origin', string="Dòng sản phẩm")

    @api.depends('th_date_from', 'th_date_to')
    def compute_name_check_condition(self):
        for rec in self:
            name = False
            if not rec.th_date_from and rec.th_date_to:
                name = f'Dưới {rec.th_date_to} ngày'
            elif not rec.th_date_to and rec.th_date_from:
                name = f'Từ {rec.th_date_from} ngày'
            else:
                name = f'Từ {rec.th_date_from} - {rec.th_date_to} ngày'
            rec.name = name
            
    @api.constrains('th_date_from', 'th_date_to')
    def constrains_th_date_to(self):
        for rec in self:
            if rec.th_date_from and rec.th_date_to and rec.th_date_from > rec.th_date_to:
                raise ValidationError("Số ngày liên hệ lần cuối từ không được lớn hơn đến")
            elif not rec.th_date_from and not rec.th_date_to:
                raise ValidationError("Cần thiết lập đến ngày")
            elif rec.th_date_from and rec.th_date_to and self.search([('th_status_detail_id', '=', rec.th_status_detail_id.id),
                                                  ('th_apm_level_id', '=', rec.th_apm_level_id.id),
                                                  ('id', '!=', rec.id), ('th_date_to', '>', rec.th_date_from),
                                                  ('th_date_from', '<', rec.th_date_to)]):
                raise ValidationError(_("Khoảng thời gian đã bị trùng lặp. Vui lòng kiểm tra lại!"))