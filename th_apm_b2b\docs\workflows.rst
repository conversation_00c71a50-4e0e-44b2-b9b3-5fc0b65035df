Chi tiết chức năng và các hàm liên quan
=====================================

A. Quản lý cơ hội B2B (th_apm.py)
--------------------------------

1. Model ThApm (Mở rộng)
~~~~~~~~~~~~~~~~~~~~~~~

**<PERSON><PERSON> tả:** Mở rộng model th.apm để bổ sung các tính năng B2B

**Các trường chính:**

* readonly_domain: Trường tính toán để kiểm soát quyền chỉnh sửa
* th_stage_b2b_id: Trường liên kết đến trạng thái B2B
* is_handed_over: Trạng thái bàn giao cơ hội
* th_owner_history_ids: Lị<PERSON> sử đơn vị sở hữu cơ hội
* th_user_ownership_ids: <PERSON><PERSON> sách người dùng có quyền sở hữu
* th_apm_b2b_dividing_ring_id: Vòng chia cơ hội B2B

2. Các hàm chính
~~~~~~~~~~~~~~

* _compute_readonly_domain: Tính toán domain chỉ đọc dựa trên trạng thái bàn giao
* _compute_th_owner_history: Tính toán lịch sử đơn vị sở hữu và người dùng
* action_view_sale_order_b2b: Xem đơn hàng trong giao diện B2B
* action_open_apm_partner_b2b: Mở thông tin đối tác trong giao diện B2B
* th_action_hand_over_apm: Hiển thị popup chọn vòng chia
* th_accept_team_apm: Đánh dấu cơ hội đã bàn giao

3. Luồng chức năng
~~~~~~~~~~~~~~~~

3.1. Bàn giao cơ hội
'''''''''''''''''''

**Mô tả:** Quy trình bàn giao cơ hội giữa các đơn vị sở hữu

**Các bước:**

1. Người dùng chọn cơ hội cần bàn giao
2. Nhấn nút "Bàn giao Cơ hội"
3. Chọn vòng chia cơ hội (th_apm_b2b_dividing_ring_id)
4. Xác nhận bàn giao

**Các hàm liên quan:**

* th_action_hand_over_apm: Hiển thị popup chọn vòng chia
* th_accept_team_apm: Đánh dấu cơ hội đã bàn giao

3.2. Xem đơn hàng B2B
'''''''''''''''''''

**Mô tả:** Xem đơn hàng liên quan đến cơ hội B2B

**Các bước:**

1. Từ form cơ hội B2B, nhấn nút "Đơn hàng"
2. Hệ thống hiển thị danh sách đơn hàng liên quan trong giao diện B2B

**Các hàm liên quan:**

* action_view_sale_order_b2b: Hiển thị đơn hàng trong giao diện B2B

B. Quản lý đơn vị sở hữu (th_ownership.py)
----------------------------------------

1. Model ThOwnershipUnit (Mở rộng)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Mô tả:** Mở rộng model th.ownership.unit để bổ sung tính năng B2B

**Các trường chính:**

* th_user_apm_b2b_ids: Danh sách người dùng B2B thuộc đơn vị sở hữu

2. Luồng chức năng
~~~~~~~~~~~~~~~~

2.1. Quản lý người dùng B2B
''''''''''''''''''''''''''

**Mô tả:** Quản lý danh sách người dùng B2B thuộc đơn vị sở hữu

**Các bước:**

1. Truy cập vào form đơn vị sở hữu
2. Thêm/xóa người dùng B2B vào trường th_user_apm_b2b_ids

C. Quản lý mối quan hệ B2B (th_level.py)
--------------------------------------

1. Model ThApmLevel (Mở rộng)
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Mô tả:** Mở rộng model th.apm.level để bổ sung tính năng B2B

**Các trường chính:**

* is_level_partner: Xác định mối quan hệ dành cho đối tác

2. Luồng chức năng
~~~~~~~~~~~~~~~~

2.1. Cấu hình mối quan hệ B2B
'''''''''''''''''''''''''''

**Mô tả:** Cấu hình mối quan hệ dành cho đối tác B2B

**Các bước:**

1. Truy cập vào danh sách mối quan hệ
2. Đánh dấu các mối quan hệ dành cho đối tác B2B

D. Quản lý đơn hàng B2B (th_sale_order.py)
----------------------------------------

1. Giao diện đơn hàng B2B
~~~~~~~~~~~~~~~~~~~~~~~

**Mô tả:** Giao diện đơn hàng chuyên biệt cho môi trường B2B

**Các tính năng:**

* Hiển thị thông tin đơn hàng phù hợp với môi trường B2B
* Kiểm soát quyền chỉnh sửa đơn hàng
* Ẩn các trường không liên quan đến B2B

2. Luồng chức năng
~~~~~~~~~~~~~~~~

2.1. Xem đơn hàng B2B
'''''''''''''''''''

**Mô tả:** Xem danh sách đơn hàng B2B

**Các bước:**

1. Truy cập menu "Đơn hàng" trong module APM-B2B
2. Hệ thống hiển thị danh sách đơn hàng B2B
