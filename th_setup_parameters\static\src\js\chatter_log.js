/** @odoo-module **/
import {registerPatch} from '@mail/model/model_core';
import {attr} from '@mail/model/model_field';
import rpc from 'web.rpc';


// Thêm biến toàn cục để lưu trữ các model
let globalInvisibleLogModels = [];
let isGlobalModelsLoaded = false;

// 
async function loadInvisibleLogModelsGlobal() {
    if (isGlobalModelsLoaded) {
        return globalInvisibleLogModels;
    }
    // lấy danh sách model từ th.invisible.log
    try {
        const models = await rpc.query({
            model: 'th.invisible.log',
            method: 'th_get_ir_model',
            args: [],
            kwargs: {},
        });
        globalInvisibleLogModels = Array.isArray(models) ? models : [];
        isGlobalModelsLoaded = true;
    } catch (error) {
        globalInvisibleLogModels = [];
        isGlobalModelsLoaded = true;
    }
    return globalInvisibleLogModels;
}
loadInvisibleLogModelsGlobal();

registerPatch({
    name: 'Chatter',
    recordMethods: {
        async onClickShowMessageSystem() {
            await this.thread.cache.showMessageSystem();
        },
    },
});

registerPatch({
    name: 'ChatterTopbar',
    fields: {
        // field để kiểm tra model hiện tại có trong danh sách không
        isCurrentModelInInvisibleList: attr({
            compute() {
                if (!this.chatter?.thread || !globalInvisibleLogModels.length) {
                    return false;
                }
                const currentModel = this.chatter.thread.model;
                return globalInvisibleLogModels.includes(currentModel);
            }
        }),
    },
});
