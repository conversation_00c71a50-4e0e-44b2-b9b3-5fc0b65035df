from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class ThApmDuplicateCheckHistory(models.Model):
    _name = 'th.apm.duplicate.check.history'
    _inherit = ['mail.thread']
    _description = 'Lịch sử kiểm tra trùng APM'

    th_name_lead_lose = fields.Char(string="Tên cơ hội thua")
    th_ownership_id = fields.Many2one('th.ownership.unit', string="Đơn vị sở hữu")
    th_status_group_id = fields.Many2one(comodel_name="th.status.category", string="Nhóm tình trạng")
    th_status_detail_id = fields.Many2one(comodel_name="th.status.detail", string="Trạng thái chi tiết")
    th_stage_id = fields.Many2one("th.apm.level", string="Mối quan hệ")
    th_description = fields.Text(string="<PERSON>ô tả", tracking=True)
    th_last_check = fields.Datetime(string="Liên hệ lần cuối", tracking=True, copy=False)
    th_lead_apm_id_old = fields.Many2one("th.apm", 'ID cơ hội APM thua')
    th_lead_apm_id_new = fields.Many2one("th.apm", 'ID cơ hội APM thắng')
    th_channel_id = fields.Many2one(comodel_name="th.info.channel", string="Kênh", tracking=True)
    user_id = fields.Many2one(comodel_name="res.users", string="Người phụ trách", tracking=True)
    th_source_name = fields.Char(string="Tên nguồn", tracking=True, copy=False)
    th_source_group_id = fields.Many2one(comodel_name="th.source.group", string="Nhóm nguồn", tracking=True, copy=False)
    th_partner_referred_id = fields.Many2one(comodel_name="res.partner", string="Người giới thiệu", tracking=True, copy=False, index=True)
    th_duplicate_type = fields.Selection(selection=[('manual', 'Thủ công'), ('auto', 'Tự động'), ('need_handle', 'Cần xử lý'), ('no_results', 'Chưa có điều kiện')],
                                         string="Loại xử lý", copy=False, default='auto')
    th_origin_id = fields.Many2one(comodel_name='th.origin', string="Dòng sản phẩm")

    def th_action_merge(self):
        """
        Xử lý khi người dùng chọn chuyển hội
        - Cơ hội cũ (th_lead_apm_id_old) sẽ thắng
        - Cơ hội mới (th_lead_apm_id_new) sẽ thua
        """
        try:
            # Sau khi ấn nút chuyển thì old sẽ thắng, new thua
            for rec in self:
                # Xóa các message cũ
                rec.th_lead_apm_id_old.message_ids = False

                # Gửi thông báo cho người phụ trách cơ hội thắng (cơ hội cũ)
                if rec.th_lead_apm_id_old.th_user_id:
                    rec.th_lead_apm_id_old.activity_schedule(
                        'mail.mail_activity_data_todo',
                        summary='Phân xử cơ hội trùng đã xử lý xong',
                        note='Thông báo: Cơ hội %s sau khi phân xử đã được xử thắng' % rec.th_lead_apm_id_old.name,
                        user_id=rec.th_lead_apm_id_old.th_user_id.id,
                    )

                # Gửi thông báo cho người phụ trách cơ hội thua (cơ hội mới)
                if rec.th_lead_apm_id_new.th_user_id:
                    rec.th_lead_apm_id_new.activity_schedule(
                        'mail.mail_activity_data_todo',
                        summary='Phân xử cơ hội trùng đã xử lý xong',
                        note='Thông báo: Cơ hội %s sau khi phân xử đã bị xử thua' % rec.th_lead_apm_id_new.name,
                        user_id=rec.th_lead_apm_id_new.th_user_id.id,
                    )

                # Tìm các cơ hội trùng khác liên quan đến cơ hội thua (cơ hội mới)
                lead_dup_remaining = rec.env['th.apm.duplicate.check.history'].search([
                    '|',
                    ('th_lead_apm_id_old', '=', rec.th_lead_apm_id_new.id),
                    ('th_lead_apm_id_new', '=', rec.th_lead_apm_id_new.id),
                    ('th_duplicate_type', '=', 'need_handle'),
                    ('th_lead_apm_id_old', '!=', rec.th_lead_apm_id_old.id)]).mapped('th_lead_apm_id_old')

                # Gửi thông báo cho các cơ hội trùng khác
                if lead_dup_remaining:
                    for lead in lead_dup_remaining:
                        if lead.th_user_id:
                            lead.activity_schedule(
                                'mail.mail_activity_data_todo',
                                summary='Phân xử cơ hội trùng đã xử lý xong',
                                note='Thông báo: Cơ hội %s sau khi phân xử đã bị xử thua' % lead.name,
                                user_id=lead.th_user_id.id,
                            )

                # Tạo lịch sử kiểm tra trùng mới cho cơ hội thua (cơ hội mới)
                vals_history = {
                    'th_name_lead_lose': rec.th_lead_apm_id_new.name,
                    'th_ownership_id': rec.th_lead_apm_id_new.th_ownership_unit_id.id if rec.th_lead_apm_id_new.th_ownership_unit_id else False,
                    'th_status_group_id': rec.th_lead_apm_id_new.th_status_category_id.id if rec.th_lead_apm_id_new.th_status_category_id else False,
                    'th_status_detail_id': rec.th_lead_apm_id_new.th_status_detail_id.id if rec.th_lead_apm_id_new.th_status_detail_id else False,
                    'th_stage_id': rec.th_lead_apm_id_new.th_stage_id.id if rec.th_lead_apm_id_new.th_stage_id else False,
                    'th_description': rec.th_lead_apm_id_new.th_description if rec.th_lead_apm_id_new.th_description else '',
                    'th_last_check': rec.th_lead_apm_id_new.th_last_check if rec.th_lead_apm_id_new.th_last_check else fields.Datetime.now(),
                    'th_lead_apm_id_old': rec.th_lead_apm_id_new.id,
                    'th_lead_apm_id_new': rec.th_lead_apm_id_old.id,
                    'th_channel_id': rec.th_lead_apm_id_new.th_channel_id.id if rec.th_lead_apm_id_new.th_channel_id else False,
                    'user_id': rec.th_lead_apm_id_new.th_user_id.id if rec.th_lead_apm_id_new.th_user_id else False,
                    'th_source_name': rec.th_lead_apm_id_new.th_source_name if rec.th_lead_apm_id_new.th_source_name else '',
                    'th_source_group_id': rec.th_lead_apm_id_new.th_source_group_id.id if rec.th_lead_apm_id_new.th_source_group_id else False,
                    'th_partner_referred_id': rec.th_lead_apm_id_new.th_partner_reference_id.id if rec.th_lead_apm_id_new.th_partner_reference_id else False,
                    'th_duplicate_type': 'manual',
                    'th_origin_id': rec.th_lead_apm_id_new.th_origin_id.id if rec.th_lead_apm_id_new.th_origin_id else False,
                }

                # Đánh dấu cơ hội thua là cơ hội trùng (cơ hội mới)
                rec.th_lead_apm_id_new.sudo().with_context(th_test_import=True).write({
                    'th_is_a_duplicate_opportunity': True,
                    'th_duplicate_processed_lead': True,
                    'th_dup_state': 'processed',
                    'th_selection_dup_result': 'change',
                    'th_lead_apm_source_id': rec.th_lead_apm_id_new.id,
                    'th_duplicate_type': 'manual',
                    'th_dup_need_admin': False,
                    'th_is_under_complaint': False,
                })
                # Cập nhật thông tin cho cơ hội thắng (cơ hội cũ)
                rec.th_lead_apm_id_old.sudo().with_context(th_test_import=True).write({
                    'th_is_a_duplicate_opportunity': False,
                    'th_duplicate_processed_lead': True,
                    'th_dup_state': 'processed',
                    'th_selection_dup_result': 'keep',
                    'th_duplicate_type': 'manual',
                    'th_dup_need_admin': False,
                    'th_is_under_complaint': False,
                })

                # Sao chép lịch sử chăm sóc từ cơ hội thua sang cơ hội thắng (từ cơ hội mới sang cơ hội cũ)
                lose_note_ids = rec.th_lead_apm_id_new.sudo().message_ids.sorted('date')
                for note in lose_note_ids:
                    if note.message_type == 'notification' or note.message_type == 'comment':
                        note_new = note.copy()
                        note_new.date = note.sudo().date
                        note_new.sudo().res_id = rec.th_lead_apm_id_old.id
                        track_list = []
                        if note.tracking_value_ids:
                            for tracking in note.tracking_value_ids:
                                new_track_value = tracking.copy()
                                track_list.append(new_track_value.id)
                        note_new.sudo().tracking_value_ids = [(6, 0, track_list)]

                # Thêm thông báo vào chatter
                msg = _("Phân xử trùng thủ công, cơ hội %s - Thắng, cơ hội %s - Thua") % (
                    rec.th_lead_apm_id_old.name, rec.th_lead_apm_id_new.name)
                rec.th_lead_apm_id_old.message_post(body=msg)
                rec.th_lead_apm_id_new.message_post(body=msg)

                # Cập nhật trạng thái của bản ghi lịch sử
                rec.write({'th_duplicate_type': 'manual'})
                
                # sửa lại lịch sử kiểm tra trùng mới cho cơ hội thua
                rec.write(vals_history)    
                
                # Gửi thông báo cho người dùng
                self.env['bus.bus']._sendone(
                    (self._cr.dbname, 'res.partner', self.env.user.partner_id.id),
                    'simple_notification',
                    {
                        'title': _('Thông báo'),
                        'message': _("Đã phân xử thành công"),
                        'warning': False
                    }
                )
                # Xóa các khiếu nại liên quan đến cơ hội thua (cơ hội mới)
                rec.th_lead_apm_id_new.th_is_under_complaint = False
                
                # tìm tất cả các lịch sử kiểm tra trùng khác liên quan đến cơ hội thua (cơ hội cũ)
                lead_dup_remaining = rec.env['th.apm.duplicate.check.history'].search([('th_lead_apm_id_new', '=', rec.th_lead_apm_id_old.id)])
                for lead in lead_dup_remaining:
                    # gán lại cơ hội thắng cho các lịch sử kiểm tra trùng khác là cơ hội thắng (cơ hội mới)
                    lead.th_lead_apm_id_new = rec.th_lead_apm_id_new.id
                # rec.unlink()
            if self._context.get('th_duplicate_type', False):
                return self.env['ir.actions.act_window']._for_xml_id('th_apm.th_action_apm_complaint_duplicate')
            else:    
                return {
                    'type': 'ir.actions.client',
                    'tag': 'reload',
                }
        except Exception as e:
            raise ValidationError(str(e))
