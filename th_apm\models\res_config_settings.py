from odoo import models, fields, api

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'
    th_manager_ids = fields.Many2many('res.users', relation="th_manager_apm", string="<PERSON><PERSON><PERSON><PERSON> quản lý")
    link_form_ecommerce = fields.Char(string="Link Form", store=True)

    def set_values(self):
        super().set_values()
        additional_managers_group = self.env.ref('th_apm.group_apm_manager')
        additional_managers_group.write({'users': [(6, 0, self.th_manager_ids.ids)]})
        self.env['ir.config_parameter'].set_param('th_manager_ids', self.th_manager_ids.ids)
        self.env['ir.config_parameter'].set_param('link_form_ecommerce', self.link_form_ecommerce)

    @api.model
    def get_values(self):
        res = super().get_values()
        th_manager_ids = self.env['ir.config_parameter'].sudo().get_param('th_manager_ids', default="[]")
        link_form_ecommerce = self.env['ir.config_parameter'].sudo().get_param('link_form_ecommerce', default="")
        res.update(th_manager_ids=[(6, 0, eval(th_manager_ids))])
        res.update(link_form_ecommerce=link_form_ecommerce)
        # if th_manager_ids:
        #     try:
        #         th_manager_ids_list = ast.literal_eval(th_manager_ids)
        #         res.update(th_manager_ids=[(6, 0, th_manager_ids_list)])
        #
        #         # Assuming you want to add read access to the 'your_module.your_model' model
        #         additional_managers_group = self.env.ref('th_apm.group_apm_manager')
        #         additional_managers_group.write({
        #             'users': [(6, 0, th_manager_ids_list)],
        #             'implied_ids': [(4, self.env.ref('th_apm.group_apm_manager').id)]
        #         })
        #     except (SyntaxError, ValueError) as e:
        #         # Handle the exception if the evaluation fails
        #         # You may log the error or take appropriate action
        #         pass
        return res