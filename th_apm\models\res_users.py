from odoo import fields, models, api


class ResUsers(models.Model):
    _inherit = "res.users"

    th_apm_team_id = fields.Many2one(comodel_name="th.apm.team", string="<PERSON><PERSON><PERSON> họ<PERSON> li<PERSON>", store=True)
    th_member_ids = fields.Many2many(comodel_name="res.users", relation="th_member_rel", column1="th_member", column2="th_manager", string="Thành viên đội quản lý", store=True  )
    th_member2_ids = fields.Many2many(comodel_name="res.users", relation="th_member_rel2", column1="th_member2", column2="th_manager2", string="Thành viên đội quản lý", store=True  )
    th_team_leader_id = fields.Many2one(comodel_name="res.users", string="Đội trưởng", store=True)
    th_manager_apm_ids = fields.Many2many(comodel_name='res.users', relation="th_manager_users_apm_ref",
                                          column1="th_user_apm_id", column2="th_team_apm_id", string="Quản lý APM")