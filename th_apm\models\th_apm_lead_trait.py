import json

from odoo import fields, models, api
from odoo.exceptions import MissingError, ValidationError

class ApmLeadTrait(models.Model):
    _name = "th.apm.lead.trait"
    _inherit = 'th.apm.contact.trait'
    _description = "Apm Intermediary Trait"

    th_apm_lead_id = fields.Many2one(comodel_name="th.apm")
    th_apm_contact_trait_id = fields.Many2one("th.apm.contact.trait")
    th_product_line_domain = fields.Char(compute='_compute_th_product_category_domain')

    # @api.constrains('th_origin_id', 'th_apm_trait_id')
    # def _check_duplicate_values(self):
    #     for rec in self:
    #         if rec.th_apm_lead_id and len(self.search([('th_origin_id', '=', rec.th_origin_id.id),
    #                                                    ('th_apm_trait_id', '=', rec.th_apm_trait_id.id),
    #                                                    ('id', '!=', rec.id),
    #                                                    ('th_apm_lead_id', '=', rec.th_apm_lead_id.id)
    #                                                    ])) > 0:
    #             raise ValidationError(
    #                 'Không thể tồn tại 2 bản ghi có "dòng sản phẩm" và "đặc điểm" giống nhau trên cùng một cơ hội!')

    @api.depends('th_apm_lead_id.th_origin_id')
    def _compute_th_product_category_domain(self):
        for rec in self:
            domain = []
            if rec.th_apm_lead_id.th_origin_id:
                domain.append(('id', '=', rec.th_apm_lead_id.th_origin_id.id))
            rec.th_product_line_domain = json.dumps(domain)


    @api.model
    def create(self, values):
        res = super(ApmLeadTrait, self).create(values)
        return res


    def write(self, values):
        rec = super(ApmLeadTrait, self).create(values)
        return rec
