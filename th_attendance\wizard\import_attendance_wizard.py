# -*- coding: utf-8 -*-
from odoo import api, fields, models, _
import xlrd
import base64
from datetime import datetime, timedelta
from odoo.exceptions import ValidationError


class ImportAttendanceWizard(models.TransientModel):
    _name = 'th.import.attendance'
    _description = 'Import attendance'

    file = fields.Binary(string='Upload File',)
    file_name = fields.Char()

    def get_exists_attendance(self, employee_id, check_in, check_out):
        """
        Input: employee_id, check_in and check-out (datetime)
        Output (2 cases): -If exists attendance record with input data : return record found
                          -If doesn't exists attendance record with input data : return False
        """
        employee_att = self.env['hr.attendance'].sudo().search([('employee_id', '=', employee_id)])
        result = False
        if check_in and check_out:
            result = employee_att.filtered(lambda l: l.check_in == check_in and l.check_out == check_out)
        if not result and check_in:
            result = employee_att.filtered(lambda l: l.check_in == check_in)
        if not result and check_out:
            result = employee_att.filtered(lambda l: l.check_out == check_out)
        return result[0] if result else False

    def get_exists_unknown_employee(self, pin, attendance_date, check_in, check_out):
        """
        Input: employee_id, check_in and check-out (datetime)
        Output (2 cases): -If exists attendance record with input data : return record found
                          -If doesn't exists attendance record with input data : return False
        """
        unknown_record = self.env['th.att.unknown.employee'].sudo().search([('th_pin', '=', pin),
                                                                            ('th_attendance_date', '=', attendance_date)])
        result = False
        if check_in and check_out:
            result = unknown_record.filtered(lambda l: l.th_checkin_time == check_in and l.th_checkout_time == check_out)
        if not result and check_in:
            result = unknown_record.filtered(lambda l: l.th_checkin_time == check_in)
        if not result and check_out:
            result = unknown_record.filtered(lambda l: l.th_checkout_time == check_out)
        return result[0] if result else False

    def action_import_attendance(self):
        try:
            return self.import_attendance()
        except ValidationError as e:
            raise ValidationError(e)
        except Exception as e:
            raise ValidationError(_("There was an error while importing attendance, please contact the administrator!"))

    def import_attendance(self):
        wb = xlrd.open_workbook(file_contents=base64.decodebytes(self.file))
        sheet = wb.sheet_by_index(0)
        start_row = 5
        errors = {'success': 0, 'update': 0, 'unknown': 0}
        for i in range(start_row - 1, sheet.nrows):
            attendance_data = []
            row = sheet.row_values(i)
            employee_pin = row[1]
            employee_name = row[2]
            employee_id = self.sudo().env['hr.employee'].search([('pin', '=', employee_pin), ('active', '=', True)]).id
            if not self.sudo().env['hr.employee'].search([('pin', '=', employee_pin), ('active', '=', True)]):
                employee_id = self.sudo().env['hr.employee'].search([('pin', '=', employee_pin), ('active', '=', False)]).id
            attendance_date = xlrd.xldate.xldate_as_datetime(row[4], wb.datemode).date()
            if len(row) >= 9 and row[6] and type(row[6]) == str:
                attendance_data.append([row[6], row[7]])
            if len(row) >= 11 and row[8] and type(row[8]) == str:
                attendance_data.append([row[8], row[9]])
            if len(row) >= 13 and row[10] and type(row[10]) == str:
                attendance_data.append([row[10], row[11]])
            if len(row) >= 15 and row[12] and type(row[12]) == str:
                attendance_data.append([row[12], row[13]])
            for time in attendance_data:
                checkin_time = time[0] if time[0] else False
                checkout_time = time[1] if time[1] else False
                if not employee_id:
                    exists_unknown = self.get_exists_unknown_employee(employee_pin, attendance_date, checkin_time, checkout_time)
                    if not exists_unknown:
                        self.env['th.att.unknown.employee'].create({
                            'th_pin': employee_pin,
                            'th_emp_name': employee_name,
                            'th_attendance_date': attendance_date,
                            'th_checkin_time': checkin_time,
                            'th_checkout_time': checkout_time,
                        })
                        errors['unknown'] += 1
                    else:
                        if not exists_unknown.th_checkin_time and checkin_time:
                            exists_unknown.write({
                                'th_checkin_time': checkin_time
                            })
                            errors['unknown'] += 1
                        if not exists_unknown.th_checkout_time and checkout_time:
                            exists_unknown.write({
                                'th_checkout_time': checkout_time
                            })
                            errors['unknown'] += 1
                    continue
                timezone_hour = timedelta(hours=7)
                check_in, check_out = False, False
                if checkin_time:
                    checkin_str = "%s %s" % (attendance_date, checkin_time)
                    check_in = datetime.strptime(checkin_str, '%Y-%m-%d %H:%M:%S') - timezone_hour
                if checkout_time:
                    checkin_str = "%s %s" % (attendance_date, checkout_time)
                    check_out = datetime.strptime(checkin_str, '%Y-%m-%d %H:%M:%S') - timezone_hour
                exists_att = self.get_exists_attendance(employee_id, check_in, check_out)
                if exists_att:
                    if not exists_att.check_in and check_in:
                        exists_att.write({
                            'check_in': check_in,
                            # 'th_real_work_numbers': real_work_numbers,
                        })
                        errors['update'] += 1
                    if not exists_att.check_out and check_out:
                        exists_att.write({'check_out': check_out,
                                          # 'th_real_work_numbers': real_work_numbers,
                                          })
                        errors['update'] += 1
                    continue
                self.env['hr.attendance'].create({
                    'employee_id': employee_id,
                    'th_attendance_number': employee_pin,
                    'check_in': check_in,
                    'check_out': check_out,
                    # 'th_real_work_numbers': real_work_numbers,
                })
                errors['success'] += 1
        return self.th_notifications(errors)

    def th_notifications(self, e):
        message = _("Success: %s, Update: %s, Unknown employee: %s") % (e.get('success'), e.get('update'), e.get('unknown'))
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Import successfully.'),
                'message': message,
                'type': 'info',
                'sticky': True,
                'next': {'type': 'ir.actions.act_window_close'}
            }
        }

