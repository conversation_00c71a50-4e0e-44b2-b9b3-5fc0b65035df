<odoo>
    <record id="th_status_detail_apm_view_tree" model="ir.ui.view">
        <field name="name">th_status_detail_view_tree</field>
        <field name="model">th.status.detail</field>
        <field name="arch" type="xml">
            <tree string="">
                <field name="name"/>
                <field name="th_description"/>
            </tree>
        </field>
    </record>
    <record id="th_status_detail_view_form_apm" model="ir.ui.view">
        <field name="name">th_status_detail_view_form_apm</field>
        <field name="model">th.status.detail</field>
        <field name="arch" type="xml">
            <form string="">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="th_description"/>
                    </group>
                    <notebook>
                        <page name="state_detail" string="Trạng thái xử lý">
                            <field name="th_processing_status_ids">
                                <tree editable="bottom">
                                    <field name="name"/>
                                    <field name="th_description"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    <record id="th_status_detail_apm_action" model="ir.actions.act_window">
        <field name="name">trạng thái chi tiết</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.status.detail</field>
        <field name="domain">[('th_type','=','apm')]</field>
        <field name="context">{'default_th_type': 'apm','create': 0, 'edit': 1, 'delete': 0}</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('th_status_detail_apm_view_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_status_detail_view_form_apm')})]"/>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>