from odoo import models, fields, api, _
from datetime import datetime, timedelta, date

from odoo.exceptions import ValidationError


class AccountMove(models.Model):
    _inherit = 'account.move'

    th_apm_lead_id = fields.Many2one(comodel_name="th.apm")
    th_apm_source_group_id = fields.Many2one(string="Nhóm nguồn", related="th_apm_lead_id.th_source_group_id", store=True)
    th_channel_id = fields.Many2one(string="Kênh", related="th_apm_lead_id.th_channel_id", store=True)
    th_introducer_id = fields.Many2one(comodel_name="res.partner", string="Người giới thiệu", related='th_apm_lead_id.th_partner_reference_id', store=True)
    th_responsibly_apm = fields.Many2one(string="Nhân sự thực hiện", related='th_apm_lead_id.th_user_id')
    th_apm_ownership_id = fields.Many2one(string="Đơn vị sở hữu", related="th_apm_lead_id.th_ownership_unit_id", store=True)
    th_preferential_policy = fields.Char(string="Chính sách ưu đãi")
    th_apm_lead_name = fields.Char(string="Mã cơ hội APM", compute="_compute_th_apm_lead_name", store=True)
    th_is_a_simple_lesson = fields.Boolean(string="Là đơn học thử")
    th_apm_origin_name = fields.Char(string="Dòng sản phẩm")

    @api.model_create_multi
    def create(self, vals_list):
        if self._context.get('account_move_from_srm', False):
            res = super(AccountMove, self).create(vals_list)
        else:
            for vals in vals_list:
                if vals.get('invoice_origin'):
                    sale_order = self.env['sale.order'].search([('name', '=', vals['invoice_origin'])], limit=1)
                    if sale_order.th_apm_id:
                        discount_line = sale_order.order_line.filtered(lambda l: l.reward_id)
                        if discount_line:
                            vals['th_preferential_policy'] = discount_line[0].reward_id.program_id.name + ' - ' + discount_line[0].name_short
            res = super(AccountMove, self).create(vals_list)
            for rec in res:
                sale_order_id = self.env['sale.order'].search([('invoice_ids', 'in', rec.ids)], limit=1)
                if sale_order_id.th_is_a_simple_lesson == True:
                    rec.th_is_a_simple_lesson = True
                if sale_order_id.th_apm_id:
                    rec.th_account_move = 'apm'
                    rec.th_apm_lead_id = sale_order_id.th_apm_id.id
                    # Nếu là hóa đơn thu thì xác nhận hóa đơn và log note
                    if rec.move_type == 'out_invoice':
                        rec.sudo().action_post()
                        msg = ("Đã tạo hóa đơn thu với mã <b>%s</b>" % rec.name)
                        rec.th_apm_lead_id.message_post(body=msg)
                    # Log mã hóa đơn cho sale order khi xác nhận đơn hàng
                    sale_order_id.th_invoice_code = rec.name
        return res

    def write(self, vals):
        if self.invoice_line_ids.sale_line_ids.order_id.th_sale_order != 'srm':
            for move in self:
                invoice_origin = vals.get('invoice_origin') or move.invoice_origin
                if invoice_origin:
                    sale_order = self.env['sale.order'].search([('name', '=', invoice_origin)], limit=1)
                    if sale_order:
                        discount_line = sale_order.order_line.filtered(lambda l: l.reward_id)
                        if discount_line:
                            vals['th_preferential_policy'] = discount_line[0].reward_id.program_id.name + ' - ' + discount_line[0].name_short
        return super(AccountMove, self).write(vals)

    @api.depends('th_apm_lead_id')
    def _compute_th_apm_lead_name(self):
        for rec in self:
            rec.th_apm_lead_name = False
            if rec.th_apm_lead_id:
                rec.th_apm_lead_name = rec.th_apm_lead_id.name.split(']')[0].strip('[')

    def _compute_payment_state(self):
        res = super(AccountMove, self)._compute_payment_state()
        if not self._context.get('account_move_from_srm', False):
            for account_move in self:
                if account_move.move_type == 'out_invoice' and account_move.th_apm_lead_id:
                    sale_order_id = self.env['sale.order'].search([('invoice_ids', 'in', account_move.ids)], limit=1)
                    if account_move.th_payment_status in ('paid', 'over_payment'):
                        vmc_order = self.env['sale.order'].search([('invoice_ids', '=', account_move.id)])
                        if vmc_order and vmc_order.th_status != 'completed':
                            vmc_order.write({'th_status': 'completed'})
                        if vmc_order and account_move.move_type == 'out_refund':
                            vmc_order.write({'th_status': 'refund', 'state': 'cancel'})
                    if sale_order_id.th_apm_id and not any(sale_order_id.invoice_ids.filtered(
                            lambda d: d.th_payment_status in ['not_paid',
                                                              'partial'])) and sale_order_id and sale_order_id.invoice_status == 'invoiced':
                        if account_move.move_type == 'out_invoice' and not sale_order_id.th_is_a_simple_lesson:
                            sale_order_id.th_apm_id.th_partner_id.sudo().write({
                                'th_is_order_apm': True,
                                'th_origin_ids': [(4, sale_order_id.th_origin_id.id)]
                            })
                            th_search_apm = self.env['th.apm'].sudo().search(
                                [('th_partner_id', '=', sale_order_id.partner_id.id), ('th_is_lead_TTVH', '=', True),
                                 ('th_after_sales_care', '=', True), ('th_reason', '=', None), ('th_order_id', '=', False)],
                                limit=1)
                            if th_search_apm:
                                th_search_apm.sudo().message_post(
                                    message_type='notification',
                                    body='Khách hàng vừa mua thêm đơn hàng và đã được cập nhật vào lịch sử mua hàng',
                                )
                    if account_move.reversed_entry_id:
                        sale_order_id.th_refunded_invoice_apm = True
                        sale_order_id.th_refund_invoice_apm = False
                        for rec in sale_order_id.invoice_ids:
                            if rec.reversed_entry_id:
                                rec.is_refund_invoice_date = fields.Date.today()
                            else:
                                rec.is_refund_invoice = True
                        sale_order_id.th_refund_invoice_apm = False
                        if sale_order_id.th_total_received_excessive and sale_order_id.th_total_refund_excessive and (
                                sale_order_id.th_total_received_excessive <= sale_order_id.th_total_refund_excessive):
                            sale_order_id.th_apm_id.th_partner_id.sudo().write({'th_is_order_apm': False})
        return res
    
    def _cron_update_origin_names(self, batch_size=1000):
        try:
            # Đếm số records cần update
            count_query = """
                SELECT COUNT(*)
                FROM account_move am
                INNER JOIN sale_order so ON so.name = am.invoice_origin
                INNER JOIN th_origin orig ON orig.id = so.th_origin_id
                WHERE am.th_apm_origin_name IS NULL or am.th_apm_origin_name = ''
                AND am.move_type IN ('out_invoice', 'out_refund')
                AND so.th_origin_id IS NOT NULL
            """
            self.env.cr.execute(count_query)
            total_records = self.env.cr.fetchone()[0]
            
            if total_records == 0:
                return
            
            # Tạo queue job để xử lý
            self.with_delay(
                priority=10,
                description=f"Update origin names for {total_records} account moves",
                channel='root.database'
            ).process_origin_name_updates(batch_size=batch_size)

        except Exception as e:
            return f"Error scheduling origin name updates: {str(e)}"

    def process_origin_name_updates(self, batch_size):
        """Xử lý cập nhật origin names qua queue job"""
        total_updated = 0
        try:
            while True:
                update_query = """
                    WITH updates AS (
                        SELECT am.id as move_id, orig.name as origin_name
                        FROM account_move am
                        INNER JOIN sale_order so ON so.name = am.invoice_origin
                        INNER JOIN th_origin orig ON orig.id = so.th_origin_id
                        WHERE (am.th_apm_origin_name IS NULL OR am.th_apm_origin_name = '')
                        AND am.move_type IN ('out_invoice', 'out_refund')
                        AND so.th_origin_id IS NOT NULL
                        LIMIT %s
                    )
                    UPDATE account_move
                    SET th_apm_origin_name = updates.origin_name
                    FROM updates
                    WHERE account_move.id = updates.move_id
                """
                
                self.env.cr.execute(update_query, (batch_size,))
                updated_count = self.env.cr.rowcount
                
                if updated_count == 0:
                    break
                    
                total_updated += updated_count
                self.env.cr.commit() 
                
            return f"Successfully updated {total_updated} account moves"
            
        except Exception as e:
            self.env.cr.rollback()
            error_msg = f"Error updating account move origin names: {str(e)}"
            return error_msg