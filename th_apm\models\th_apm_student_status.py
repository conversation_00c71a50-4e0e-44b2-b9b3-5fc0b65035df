import json
from odoo.exceptions import UserError
from odoo import models, fields, api, _
from datetime import timedelta

class StudentStatus(models.Model):
    _name = 'th.apm.student.status'
    _description = "Tình trạng học viên"

    th_lead_apm_id = fields.Many2one(string="Cơ hội", comodel_name='th.apm')
    th_product_id = fields.Many2one('product.product', string='Sản phẩm', ondelete='cascade')

    th_status_detail_id = fields.Many2one(comodel_name="th.status.detail", domain="[('th_apm_level_ids', '=?', th_level), ('th_status_category_id', '=?', th_status_category_id)]", string="Trạng thái chi tiết")
    th_status_category_id = fields.Many2one(comodel_name='th.status.category', string="Nhóm trạng thái", domain="[('th_apm_level_category', '=?', th_level), ('th_categ_after_sale', '=', True)]")
    th_level = fields.Many2one(comodel_name="th.apm.level", string="Level", tracking=True)
    th_registration_date = fields.Datetime("Ngày đăng ký")
    th_activation_date = fields.Datetime("Ngày kích hoạt" )
    th_closing_date = fields.Datetime("Ngày đóng khóa")
    th_renewal_date = fields.Datetime("Ngày gia hạn")
    th_extension_end_date = fields.Datetime("Ngày kết thúc gia hạn")
    th_sale_order_id = fields.Many2one("sale.order", "Đơn hàng")
    th_ownership_unit_id = fields.Char(string="Đơn vị sở hữu")
    th_origin_ids = fields.Many2many(string='Dòng sản phẩm', comodel_name="th.origin",
                                     related='th_sale_order_id.th_origin_ids')
    th_origin_id = fields.Many2one(string='Dòng sản phẩm', comodel_name="th.origin")
    @api.onchange('th_activation_date', 'th_renewal_date')
    def onchange_th_activation_date(self):
        if self.th_activation_date:
            self.th_closing_date = self.th_activation_date + timedelta(days=90)
            self.th_extension_end_date = self.th_renewal_date + timedelta(days=15) if self.th_renewal_date else False

    @api.model
    def create(self, values):
        # Lưu các trường th_closing_date và th_extension_end_date khi lưu tình trạng học viên trong cơ hội sau bán
        if values.get('th_activation_date'):
            date_activation = fields.Datetime.from_string(values['th_activation_date']) if isinstance(values['th_activation_date'], str) else values['th_activation_date']
            values['th_closing_date'] = fields.Datetime.to_string(date_activation + timedelta(days=90))
            
        if values.get('th_renewal_date'):
            date_renewal = fields.Datetime.from_string(values['th_renewal_date']) if isinstance(values['th_renewal_date'], str) else values['th_renewal_date']
            values['th_extension_end_date'] = fields.Datetime.to_string(date_renewal + timedelta(days=15))
            
        return super(StudentStatus, self).create(values)


    def write(self, values):
        if values.get('th_activation_date'):
            date_activation = fields.Datetime.from_string(values['th_activation_date']) if isinstance(values['th_activation_date'], str) else values['th_activation_date']
            values['th_closing_date'] = fields.Datetime.to_string(date_activation + timedelta(days=90))
            
        if values.get('th_renewal_date'):
            date_renewal = fields.Datetime.from_string(values['th_renewal_date']) if isinstance(values['th_renewal_date'], str) else values['th_renewal_date']
            values['th_extension_end_date'] = fields.Datetime.to_string(date_renewal + timedelta(days=15))
        
        return super(StudentStatus, self).write(values)


class ReSign(models.Model):
    _name = 'th.apm.resign'
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = "Tái ký"
    name = fields.Char(string="Tên", required=True)
    th_detail_apm_resign_ids = fields.One2many('th.detail.apm.resign', 'th_resign_id', string='Chi tiết tái ký')

class DetalReSign(models.Model):
    _name = 'th.detail.apm.resign'
    _description = "Chi tiết tái Ký"
    name = fields.Char(string="Tên", required=True)
    th_description = fields.Char(string="Mô tả")
    th_resign_id = fields.Many2one('th.apm.resign', string='Tái ký')
