from odoo import models, fields

class HrEmployeeBase(models.AbstractModel):
    _inherit = 'hr.employee.base'

    work_location_id = fields.Many2one(compute=False, domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]")
    th_address = fields.Char(string="Địa chỉ làm việc", related="work_location_id.th_address", store=True)
    leave_manager_id = fields.Many2one('res.users', string='Nghỉ phép và chấm công bù')
