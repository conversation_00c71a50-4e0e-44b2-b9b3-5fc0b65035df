from odoo import fields, models, api, exceptions

class ThCohort(models.Model):
    _name = "th.cohort"
    _description = "Khóa tuyển sinh"

    name = fields.Char(string="Tên", required=True)
    th_cohort_code = fields.Char(string="Mã khóa tuyển sinh", required=True)
    th_start_date = fields.Date(string="<PERSON><PERSON>y bắt đầu", required=True)
    th_class_ids = fields.One2many("th.cohort.class", "th_cohort_id", string="Danh sách lớp học")
    th_cohort_mode = fields.Char(string="Hình thức")
    th_cohort_status = fields.Selection([('draft', 'Nháp'), ('approved', 'Đã duyệt')], string='Trạng thái', default='draft')
    # th_student_limit = fields.Integer(string="Giới hạn số lượng sinh viên", required=True)
    # sale_order_ids = fields.One2many('sale.order', 'th_cohort_id', string="Đơn hàng")
    # th_current_students = fields.Integer(
    #     string="Số lượng sinh viên hiện tại",
    #     compute="_compute_current_students",
    #     store=True
    # )
    # th_check_student_limit = fields.Boolean(
    #     string="Chưa đủ số lượng",
    #     compute="_compute_check_student_limit",
    #     store = True
    # )
    # # cập nhật số lượng sinh viên hiện tại khi có thay đổi đơn hàng
    # @api.depends( 'sale_order_ids.state')
    # def _compute_current_students(self):
    #     for cohort in self:
    #         cohort.th_current_students = len([
    #             order for order in cohort.sale_order_ids
    #             if order.state in ['sale', 'done']
    #         ])
    # # kiểm tra số lượng sinh viên hiện tại có nhỏ hơn giới hạn không
    # @api.depends('th_current_students', 'th_student_limit')
    # def _compute_check_student_limit(self):
    #     for rec in self:
    #         rec.th_check_student_limit = rec.th_current_students < rec.th_student_limit
    #
    # @api.constrains('name', 'th_cohort_code', 'th_start_date')
    # def _check_unique_constraints(self):
    #     for rec in self:
    #         # Kiểm tra mã khóa tuyển sinh là duy nhất
    #         if self.search_count([
    #             ('th_cohort_code', '=', rec.th_cohort_code),
    #             ('id', '!=', rec.id)
    #         ]):
    #             raise exceptions.ValidationError("Mã khóa tuyển sinh phải là duy nhất.")
    #         # Kiểm tra cặp tên + ngày bắt đầu là duy nhất
    #         if self.search_count([
    #             ('name', '=', rec.name),
    #             ('th_start_date', '=', rec.th_start_date),
    #             ('id', '!=', rec.id)
    #         ]):
    #             raise exceptions.ValidationError("Khóa tuyển sinh này đã tồn tại.")