from odoo import api, fields, models, _, exceptions


class ThAPMActiveAccount(models.Model):
    _name = "th.apm.active.account"
    _description = "<PERSON> dõi trạng thái kích hoạt tài khoản APM"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    th_customer_name = fields.Char(string="Tên khách hàng",
                                   required=True)
    th_customer_code = fields.Char(string="Mã khách hàng",
                                   required=True)
    th_customer_phone = fields.Char(string="Số điện thoại")
    th_customer_email = fields.Char(string="Email")
    th_stage = fields.Selection(selection=[('l0', 'L0 - Đợi kích hoạt'),
                                           ('l1', 'L1 - <PERSON><PERSON>ch hoạt lỗi'),
                                           ('l2', 'L2 - <PERSON><PERSON> gửi yêu cầu'),
                                           ('l3', 'L3 - <PERSON><PERSON> tạo tà<PERSON> kho<PERSON>n'),
                                           ('l4', 'L4 - Huỷ')],
                                string="Trạng thái",
                                default="l0",
                                required=True, compute='stage_computed')
    th_stages = fields.Selection(related="th_stage", store=True)
    th_status = fields.Selection(related="th_sale_order_id.th_status")
    th_account_status = fields.Selection(string="Đã update", related="th_sale_order_id.th_account_status")
    th_sale_order_id = fields.Many2one(comodel_name="sale.order", string="Đơn hàng")
    th_apm_active_account_line_ids = fields.One2many("th.apm.active.account.line",
                                                     "th_apm_active_account_id",
                                                     string="Trạng thái kích hoạt khoá học APM")
    th_apm_active_account_line_id = fields.Many2one("th.apm.active.account.line", string="Bộ lọc chi tiết bảng line",
                                                    compute="_compute_active_account_line_id", store=True)
    th_course_activation_status = fields.Selection(related="th_apm_active_account_line_id.th_course_activation_status",
                                                   store=True)
    th_description = fields.Text(string='Mô tả')
    th_origin_id = fields.Many2one(comodel_name='th.origin', string="Dòng sản phẩm", related="th_sale_order_id.th_origin_id")
    th_log_api_ids = fields.Many2many(
        "th.log.api",
        string="Log API",
        compute="_compute_th_log_apis",
    )
    @api.depends('th_sale_order_id')
    def _compute_th_log_apis(self):
        for rec in self:
            if rec:
                logs = self.env['th.log.api'].search([
                    ('th_record_id', '=', rec.th_sale_order_id.id),
                    ('th_function_call', '=', 'th_update_status_sale_order')
                ])
                rec.th_log_api_ids = logs
            else:
                rec.th_log_api_ids = False



    def name_get(self):
        result = []
        for rec in self:
            name = f"{rec.th_customer_name}"
            result.append((rec.id, name))
        return result


    def write(self, values):
        rec = super(ThAPMActiveAccount, self).write(values)
        return rec


    def re_active(self):
        result = self.th_sale_order_id.th_update_status_sale_order()
        return result

    @api.depends('th_status')
    def stage_computed(self):
        for rec in self:
            # Kiểm tra th_status
            if rec.th_status == "completed":
                if rec.th_sale_order_id.th_apm_id.th_ecommerce_platform == False and rec.th_sale_order_id.th_account_status == False or (
                        rec.th_sale_order_id.th_apm_id.th_ecommerce_platform == True and rec.th_sale_order_id.th_updated == True) and rec.th_sale_order_id.th_account_status == False:
                    rec.th_stage = "l2"
                elif rec.th_sale_order_id.th_apm_id.th_ecommerce_platform == False and rec.th_sale_order_id.th_account_status == "success" or (
                        rec.th_sale_order_id.th_apm_id.th_ecommerce_platform == True and rec.th_sale_order_id.th_updated == True and rec.th_sale_order_id.th_account_status == "success"):
                    rec.th_stage = "l3"
                elif rec.th_sale_order_id.th_apm_id.th_ecommerce_platform == False and rec.th_sale_order_id.th_account_status == "error" or (
                        rec.th_sale_order_id.th_apm_id.th_ecommerce_platform == True and rec.th_sale_order_id.th_updated == True and rec.th_sale_order_id.th_account_status == "error"):
                    rec.th_stage = "l1"
                else:
                    rec.th_stage = "l2"
            elif rec.th_status == "canceled":
                rec.th_stage = "l4"
            else:
                rec.th_stage = "l0"

    @api.model
    def create_record_from_sale_order(self, sale_order):
        if sale_order.state == 'sale' and sale_order.invoice_ids.th_payment_status == 'not_paid' and sale_order.th_apm_id != False:
            sale_order_line = self.env['sale.order.line'].search(
                [('order_id', '=', sale_order.id), ('is_reward_line', '=', False)])
            line_record = []
            for line in sale_order_line:
                if line.product_id.default_code:
                    line_record.append((0, 0, {
                        'th_product_id': line.product_id.id,
                        'th_course_activation_status': 'wait_for_open',
                        'th_waiting_reason': 'Chưa thanh toán',
                    }))
            vals = {
                'th_customer_name': sale_order.partner_id.name,
                'th_customer_code': sale_order.th_customer_code,
                'th_customer_phone': sale_order.th_customer_phone,
                'th_customer_email': sale_order.th_customer_email,
                'th_sale_order_id': sale_order.id,
                'th_apm_active_account_line_ids': line_record,
            }
            self.create(vals)
        return

    @api.depends('th_apm_active_account_line_ids')
    def _compute_active_account_line_id(self):
        for record in self:
            record.th_apm_active_account_line_id = record.th_apm_active_account_line_ids[:1].id \
                if record.th_apm_active_account_line_ids else False
