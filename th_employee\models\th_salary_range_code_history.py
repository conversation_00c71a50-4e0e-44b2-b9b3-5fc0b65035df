from odoo import fields, models, api

class ThSalaryRangeCodeHistory(models.Model):
    _name = "th.salary.range.code.history"
    _description = "Lịch sở mã ngạch bậc"

    name = fields.Char(string="Tên", required=True, compute="_compute_name")
    th_employee_id = fields.Many2one(comodel_name="hr.employee", string="Nhân viên", required=True)
    th_department_id = fields.Many2one(comodel_name="hr.department", related="th_employee_id.department_id", string="Phòng ban")
    th_job_id = fields.Many2one(comodel_name="hr.job", related="th_employee_id.job_id", string="Chức vụ")
    th_salary_range_code_id = fields.One2many(comodel_name="th.salary.range.code", inverse_name="th_salary_range_code_history", string="Lịch sử mã ngạch bậc")
    active = fields.Boolean('Active', default=True)

    @api.depends('th_employee_id')
    def _compute_name(self):
        for rec in self:
            rec.name = f"<PERSON><PERSON>ch sử mã ngạch bậc của {rec.th_employee_id.name}" if rec.th_employee_id else ""