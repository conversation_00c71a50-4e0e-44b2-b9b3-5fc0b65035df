import json
from odoo import api, fields, models, _, exceptions
from odoo.exceptions import ValidationError
from odoo.osv import expression
from datetime import datetime, timedelta
import xmlrpc.client


class opportunitylistpartner(models.Model):
    _name = "th.apm.opportunity.list.partner"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = "danh sách cơ hội"

    name = fields.Char(string='Tên cơ hội', readonly=True, required=True, default="MỚI", copy=False)
    th_origin_id = fields.Many2one(comodel_name='th.origin', string="Dòng sản phẩm")
    th_campaign_id = fields.Many2one(comodel_name="th.apm.campaign", string="Chiến dịch",
                                     domain=lambda cam: [('id', '!=', cam.env.ref('th_apm.campaign_lead_formio').id),
                                                         ('state', '=', 'approve'), ('th_origin_id', '!=', False)],)
    th_last_check = fields.Datetime(string="<PERSON>ên hệ lần cuối", default=lambda self: fields.Datetime.now(),tracking=True)
    th_stage_id = fields.Many2one(comodel_name="th.apm.level", string="Mối quan hệ", tracking=True,
                                  default=lambda self: self.env['th.apm.level'].search( [('th_first_status', '=', True)]).id,readonly=False, store=True)
    th_apm_id = fields.Many2one(comodel_name="th.apm")
    th_user_id = fields.Many2one('res.users', string='Người chăm sóc', tracking=True)
    is_sale_order_web  = fields.Boolean(string='Có đơn hàng từ web')
    th_ownership_unit_id = fields.Many2one(comodel_name="th.ownership.unit", string="Đơn vị sở hữu", tracking=True)