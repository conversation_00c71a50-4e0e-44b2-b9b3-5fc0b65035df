from odoo import models, fields, api
import xmlrpc


class ThEthnicity(models.Model):
    _name = 'th.ethnicity'
    _description = 'Dân tộc'

    active = fields.<PERSON><PERSON><PERSON>('Active', default=True)
    name = fields.Char(string='<PERSON>ân tộc', required=True)
    th_ethnicity_b2b_id = fields.Integer("Id Dân tộc B2B", copy=False)

    # @api.model
    # def create(self, values):
    #     res = super(ThEthnicity, self).create(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in res:
    #             rec.th_action_sync_b2b_ethnicity(rec)
    #     return res
    #
    # def write(self, values):
    #     res = super(ThEthnicity, self).write(values)
    #     if not self._context.get('th_test_import', False):
    #         for rec in self:
    #             rec.th_action_sync_b2b_ethnicity(rec)
    #     return res
    #
    # def unlink(self):
    #     for rec in self:
    #         rec.with_context(is_delete=True).th_action_sync_b2b_ethnicity(rec)
    #     res = super(ThEthnicity, self).unlink()
    #     return res
    #
    # def th_action_sync_b2b_ethnicity(self, res):
    #     vals = {}
    #     if not res:
    #         return
    #     server_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'aff')], limit=1,
    #                                                   order='id desc')
    #     try:
    #         if not server_api:
    #             return False
    #         result_apis = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(server_api.th_url_api))
    #         db = server_api.th_db_api
    #         uid_api = server_api.th_uid_api
    #         password = server_api.th_password
    #         for rec in res:
    #             vals = {'name': rec.name,
    #                     'th_ethnicity_samp_id': rec.id}
    #             if not rec.th_ethnicity_b2b_id and not self._context.get('is_delete'):
    #                 ethnicity = result_apis.execute_kw(db, uid_api, password, 'th.ethnicity', 'create', [vals])
    #                 rec.with_context(th_test_import=True).write({'th_ethnicity_b2b_id': ethnicity})
    #             else:
    #                 if not self._context.get('is_delete'):
    #                     ethnicity = result_apis.execute_kw(db, uid_api, password, 'th.ethnicity', 'write',
    #                                                     [[rec.th_ethnicity_b2b_id], vals])
    #                 else:
    #                     ethnicity = result_apis.execute_kw(db, uid_api, password, 'th.ethnicity', 'unlink',
    #                                                     [[rec.th_ethnicity_b2b_id]])
    #     except Exception as e:
    #         self.env['th.log.api'].create({
    #             'state': 'error',
    #             'th_model': str(self._name),
    #             'th_description': str(e),
    #             'th_record_id': str(res.ids),
    #             'th_input_data': str(vals),
    #             'th_function_call': str('th_action_sync_b2b_ethnicity'),
    #         })
    #         return