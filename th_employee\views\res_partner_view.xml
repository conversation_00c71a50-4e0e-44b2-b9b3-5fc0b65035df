<odoo>
    <record id="view_partner_form" model="ir.ui.view">
        <field name="name">res.partner.form.inherit.th_employee</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form/sheet/div[2]/div" position="after">                            
                <div class="o_row">
                    <field name="th_type" groups="base.group_no_one"/>
                </div>              
            </xpath>
            <xpath expr="//form/sheet/group/group[1]" position="after">                            
                <group>
                    <span class="o_form_label o_td_label" name="address_name">
                        <b attrs="{'invisible': [('is_company','=', True)]}">Permanent Address</b>
                        <b attrs="{'invisible': [('is_company','=', False)]}">Address</b>
                    </span>
                    <div class="o_address_format">
                        <field name="th_street" placeholder="Street..." class="o_address_street" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                        <field name="th_street2" placeholder="Street 2..." class="o_address_street" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                        <field name="th_city" placeholder="City" class="o_address_city" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                        <field name="th_state_id" class="o_address_state" placeholder="State" options="{'no_open': True, 'no_quick_create': True}" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}" context="{'country_id': country_id, 'default_country_id': country_id, 'zip': zip}"/>
                        <field name="th_zip" placeholder="ZIP" class="o_address_zip" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                        <field name="th_country_id" placeholder="Country" class="o_address_country" options="{&quot;no_open&quot;: True, &quot;no_create&quot;: True}" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                    </div>
                </group>
            </xpath>
            <xpath expr="//field[@name='type']" position="replace">
                <field name="type" invisible="1"/>
                <b attrs="{'invisible': [('is_company','=', True)]}">Current Address</b>
            </xpath>
        </field>
    </record>

    <record id="th_res_partner_private_view_form" model="ir.ui.view">
        <field name="name">th.res.partner.private.view.form</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.res_partner_view_form_private"/>
        <field name="arch" type="xml">

            <xpath expr="//form/sheet/group/group[1]" position="after">
                <group>
                    <span class="o_form_label o_td_label" name="address_name">
                        <b>Current Address</b>
                    </span>
                    <div class="o_address_format">
                        <field name="th_street" placeholder="Street..." class="o_address_street" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                        <field name="th_street2" placeholder="Street 2..." class="o_address_street" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                        <field name="th_city" placeholder="City" class="o_address_city" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                        <field name="th_state_id" class="o_address_state" placeholder="State" options="{'no_open': True, 'no_quick_create': True}" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}" context="{'country_id': country_id, 'default_country_id': country_id, 'zip': zip}"/>
                        <field name="th_zip" placeholder="ZIP" class="o_address_zip" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                        <field name="th_country_id" placeholder="Country" class="o_address_country" options="{&quot;no_open&quot;: True, &quot;no_create&quot;: True}" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                    </div>
                </group>
            </xpath>
            <xpath expr="//label[@for='street']" position="attributes">
                <attribute name="string">Permanent Address</attribute>
            </xpath>

        </field>
    </record>
</odoo>