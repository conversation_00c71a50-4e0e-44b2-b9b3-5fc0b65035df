from odoo import api, fields, models
import json

from odoo.exceptions import ValidationError


class ThApmDividingRing(models.Model):
    _name = 'th.apm.dividing.ring'
    _description = 'Vòng chia APM'

    name = fields.Char(string="Vòng chia", required=True)
    th_user_ids = fields.Many2many(comodel_name="res.users", string="Nhân viên")
    th_flag = fields.Char("Cờ")
    th_is_partner_dividing = fields.Boolean(string="Là vòng chia của đối tác")
    th_is_opportunity_dividing = fields.Boolean(string="Là vòng chia của cơ hội")
    th_origin_id = fields.Many2one(
        comodel_name='th.origin',
        string="Dòng sản phẩm",
        domain="[('th_module_ids', 'in', th_check_domain_origin_ids)]",
    )
    th_check_domain_origin_ids = fields.Many2many(comodel_name='th.origin', compute="_compute_check_domain_origin")
    @api.model
    def _get_origin_domain(self):
        apm_module = self.env.ref('th_setup_parameters.th_apm_module')
        return [('th_module_ids', 'in', [apm_module.id])]

    @api.depends('th_origin_id')
    def _compute_check_domain_origin(self):
        for record in self:
            record.th_check_domain_origin_ids = self.env['th.origin'].search(self._get_origin_domain())

    @api.constrains('name')
    def _constraint_name(self):
        if any(self.search([('id', '!=', rec.id), ('name', '=', rec.name)]) for rec in self):
            raise ValidationError("Tên vòng chia không được trùng, kiểm tra lại!")

    @api.model_create_multi
    def create(self, vals_list):
        res = super().create(vals_list)
        flag = 0
        for rec in res:
            rec.th_flag = json.dumps([flag, False])
        return res

    def write(self, vals):
        res = super().write(vals)
        if vals.get('th_user_ids'):
            members_ids = self.th_user_ids.ids
            members_ids.sort()
            th_flag_dict = json.loads(self.th_flag) if self.th_flag else [0, False]
            member_id = th_flag_dict[1] if th_flag_dict else False
            if not member_id:
                return res
            if member_id in members_ids:
                self.th_flag = json.dumps([members_ids.index(member_id), member_id])
                return res
            if len(members_ids) == 0 or max(members_ids) < member_id:
                flag = 0
                self.th_flag = json.dumps([flag, False])
                return res
            for index, member in enumerate(members_ids):
                if member > member_id:
                    self.th_flag = json.dumps([index, member])
                    return res
            return res

    def action_assign_leads_dividing_ring(self):
        member_ids = self.th_user_ids.ids
        if not member_ids:
            return
        member_ids.sort()
        flag = 0
        th_flag = json.loads(self.th_flag) if self.th_flag else [0, False]
        flag = th_flag[0]
        result = member_ids[flag]
        flag = (flag + 1) % len(member_ids)
        self.th_flag = json.dumps([flag, member_ids[flag]])
        return result