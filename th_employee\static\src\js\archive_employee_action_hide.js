/** @odoo-module */

import { registry } from '@web/core/registry';
import { listView } from '@web/views/list/list_view';
import { EmployeeListController } from '@hr/views/list_view';

export class CustomEmployeeListController extends EmployeeListController {
    setup() {
        super.setup();
        this.archiveEnabled = false;
    }
}

registry.category('views').add('custom_hr_employee_list', {
    ...listVie<PERSON>,
    Controller: CustomEmployeeListController,
});
