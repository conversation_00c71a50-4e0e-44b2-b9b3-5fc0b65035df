#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_qualifications__th_employee_id
msgid "Employee"
msgstr "Nhân viên"

#. module: th_employee
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_phone
#: model:ir.model.fields,field_description:hr.field_res_users__work_phone
msgid "Work Phone"
msgstr "Đi<PERSON>n tho<PERSON>i"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_qualifications__th_institution
msgid "Academic Institution"
msgstr "Trường học"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_qualifications__th_name_qlf
msgid "Name of Qualification"
msgstr "<PERSON><PERSON><PERSON> bậ<PERSON> học"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_qualifications__th_date_qlf
msgid "Date of Qualification"
msgstr "Ngày tốt nghiệp"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_qualifications__th_major
msgid "Major"
msgstr "Ngành học"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_res_partner_bank__th_branch_name
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_branch
msgid "Branch name"
msgstr "Chi nhánh"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_joined_date
msgid "Joined date"
msgstr "Ngày bắt đầu làm việc"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_left_date
msgid "Left date"
msgstr "Ngày thôi việc"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_activeness
msgid "Activeness"
msgstr "Tình trạng"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__hr_employee__th_activeness__working
msgid "Working"
msgstr "Đang làm việc"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__hr_employee__th_activeness__quit
msgid "Quit"
msgstr "Đã nghỉ việc"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__hr_employee__th_activeness__maternity
msgid "Maternity leave"
msgstr "Nghỉ thai sản"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__hr_employee__th_activeness__unpaid
msgid "Unpaid leave"
msgstr "Nghỉ không lương"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_issued_date
msgid "Issued date"
msgstr "Ngày cấp"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_place_of_issued
msgid "Place of Issue"
msgstr "Nơi cấp"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_email
msgid "Email AUM"
msgstr "Email AUM"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_emergency_rela
msgid "Relationship"
msgstr "Quan hệ"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_emergency_address
msgid "Address AUM"
msgstr "Địa chỉ"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_current_add
msgid "Current Address"
msgstr "Nơi ở hiện tại"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_perm_add
msgid "Permanent Address"
msgstr "Quê quán"

#. module: th_employee
#: model_terms:ir.ui.view,arch_db:th_employee.abs_hr_employee_view_form
msgid "Academic Qualifications"
msgstr "Thông tin bằng cấp"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_human_resource_management
msgid "Human resource management"
msgstr "Quản lý nhân sự"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_state
msgid "Employee status"
msgstr "Trạng thái của nhân viên"

#. module: th_employee
#: model_terms:ir.ui.view,arch_db:th_employee.abs_hr_employee_view_form
msgid "Job Title"
msgstr "Chức vụ"

#. module: th_employee
#: model_terms:ir.ui.view,arch_db:th_employee.abs_hr_employee_view_form2
msgid "Generate"
msgstr "Tạo"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__res_partner__th_type__employees
msgid "Employees"
msgstr "Nhân viên"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__res_partner__th_type__customers
msgid "Customers"
msgstr "Khách hàng"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__res_partner__th_type__suppliers
msgid "Suppliers"
msgstr "Nhà cung cấp"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__res_partner__th_type__contractors
msgid "Contractors"
msgstr "Nhà thầu"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__hr_employee__th_state__intern
msgid "Intern"
msgstr "Thực tập sinh"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__hr_employee__th_state__official_staff
msgid "Official Staff"
msgstr "Nhân viên chính thức"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__hr_employee__th_state__probationary
msgid "Probationary"
msgstr "Nhân viên thử việc"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__hr_employee__th_state__collaborators
msgid "Collaborators"
msgstr "Cộng tác viên"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__hr_employee__th_state__employee_quit
msgid "Employee Quit"
msgstr "Nhân viên nghỉ việc"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_date
msgid "Departure Date"
msgstr "Ngày nghỉ việc"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_contract_history__contract_count
msgid "Number of Contracts"
msgstr "Số lượng hợp đồng"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_departure_reason__name
msgid "Reason Name"
msgstr "Lý do"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_gamification_badge__name
msgid "Badge Name"
msgstr "Tên huy hiệu"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_type_of_id
msgid "Type of ID"
msgstr "Loại ID"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__hr_employee__th_type_of_id__01
msgid "Identity card"
msgstr "Chứng minh nhân dân"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__hr_employee__th_type_of_id__02
msgid "Citizen identification"
msgstr "Căn cước công dân"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__hr_employee__th_type_of_id__03
msgid "Passport"
msgstr "Hộ chiếu"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__hr_employee__th_type_of_id__04
msgid "Birth certificate"
msgstr "Giấy khai sinh"

#. module: th_employee
#: model:ir.model.fields.selection,name:th_employee.selection__hr_employee__th_type_of_id__05
msgid "Others"
msgstr "Khác"

#. module: th_employee
#: code:addons/th_employee/models/badge.py:0
#, python-format
msgid "The limitation number cannot be negative"
msgstr "Số lượng giới hạn không được âm"

#. module: th_employee
#: model:res.groups,name:th_employee.group_hr_staff
msgid "Staff"
msgstr "Nhân viên"

#. module: th_employee
#: model_terms:ir.ui.view,arch_db:th_employee.th_resume_line_view_form
msgid "Title"
msgstr "Tiêu đề"

#. module: th_employee
#: model_terms:ir.ui.view,arch_db:th_employee.abs_hr_employee_view_form
msgid "Private Information"
msgstr "Thông tin cá nhân"

#. module: th_employee
#: model_terms:ir.ui.view,arch_db:th_employee.abs_hr_employee_view_form
msgid "Private Contact"
msgstr "Liên lạc cá nhân"

#. module: th_employee
#: model_terms:ir.ui.view,arch_db:th_employee.abs_hr_employee_view_form
msgid "Phone"
msgstr "Điện thoại"

#. module: th_employee
#: model_terms:ir.ui.view,arch_db:th_employee.abs_hr_employee_view_form
msgid "Language"
msgstr "Ngôn ngữ"

#. module: th_employee
#: model_terms:ir.ui.view,arch_db:th_employee.abs_hr_employee_view_form
msgid "Marital Status"
msgstr "Tình trạng hôn nhân"

#. module: th_employee
#: model_terms:ir.ui.view,arch_db:th_employee.abs_hr_employee_view_form
msgid "Emergency"
msgstr "Khẩn cấp"

#. module: th_employee
#: model_terms:ir.ui.view,arch_db:th_employee.abs_hr_employee_view_form
msgid "Education"
msgstr "Trình độ"

#. module: th_employee
#: model_terms:ir.ui.view,arch_db:th_employee.abs_hr_employee_view_form
msgid "Citizenship"
msgstr "Quốc tịch"

#. module: th_employee
#: model_terms:ir.ui.view,arch_db:th_employee.abs_hr_employee_view_form
msgid "Dependant"
msgstr "Phụ thuộc"

#. module: th_employee
#: model_terms:ir.ui.view,arch_db:th_employee.abs_hr_employee_view_form
msgid "Work Permit"
msgstr "Giấy phép lao động"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__permit_no
#: model:ir.model.fields,field_description:hr.field_res_users__permit_no
msgid "Work Permit No"
msgstr "Số giấy phép lao động"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__mobile_phone
#: model:ir.model.fields,field_description:hr.field_res_users__mobile_phone
msgid "Work Mobile"
msgstr "Điện thoại cá nhân"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_email
#: model:ir.model.fields,field_description:hr.field_res_users__work_email
msgid "Work Email"
msgstr "Email Sambala"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__child_information
#: model:ir.model.fields,field_description:th_employee.field_res_users__child_information
msgid "Child information"
msgstr "Thông tin người phụ thuộc"

#. module: th_employee
#: model:ir.model.fields,field_description:hr.field_hr_employee__children
#: model:ir.model.fields,field_description:hr.field_res_users__children
msgid "Number of Dependent Children"
msgstr "Số người phụ thuộc"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_children_employee__th_children_name
msgid "First and last name"
msgstr "Họ và tên"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_children_employee__th_birthday
#: model:ir.model.fields,field_description:th_employee.field_res_users__th_birthday
msgid "Date of Birth"
msgstr "Ngày sinh"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__identification_id
#: model:ir.model.fields,field_description:hr.field_res_users__identification_id
msgid "Identification No"
msgstr "Số ID"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_hr_employee__th_tax_no
#: model:ir.model.fields,field_description:th_employee.field_res_users__th_tax_no
msgid "Tax No"
msgstr "Mã số thuế"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_root
msgid "Employees"
msgstr "Quản lý nhân sự"

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_reason
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_reason_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_reason_id
msgid "Departure Reason"
msgstr "Lý do nghỉ"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/views/archive_employee_hook.js:0
#: code:addons/hr/static/src/views/archive_employee_hook.js:0
#, python-format
msgid "Employee Termination"
msgstr "Thay đổi tình trạng nhân sự"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_work_permit
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Permit"
msgstr "Hợp đồng lao động"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__permit_no
#: model:ir.model.fields,field_description:hr.field_res_users__permit_no
msgid "Work Permit No"
msgstr "Số hợp đồng lao động"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_expiration_date
msgid "Work Permit Expiration Date"
msgstr "Ngày hết hạn hợp đồng lao động"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Archived"
msgstr "Đã nghỉ việc"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Configuration"
msgstr "Sửa"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "PIN Code"
msgstr "Mã chấm công"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Status"
msgstr "Liên kết"

#. module: hr
#: model:ir.model,name:hr.model_hr_work_location
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__name
#: model:ir.model.fields,field_description:hr.field_res_users__work_location_id
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_form_view
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_tree_view
msgid "Work Location"
msgstr "Khu vực làm việc"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_work_location_action
#: model:ir.ui.menu,name:hr.menu_hr_work_location_tree
msgid "Work Locations"
msgstr "Khu vực làm việc"

#. module: th_employee
#: model:ir.actions.act_window,name:th_employee.th_salary_range_code_action
#: model:ir.ui.menu,name:th_employee.th_salary_range_code_menu
msgid "Rank code"
msgstr "Mã Ngạch-bậc"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_salary_range_code__th_code
#: model:ir.model.fields,field_description:th_employee.field_hr_contract__th_code
#: model:ir.model.fields,field_description:th_employee.field_hr_payslip__th_code
msgid "Salary range code"
msgstr "Mã ngạch bậc"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_salary_range_code__th_insurance_paid
#: model:ir.model.fields,field_description:th_employee.field_hr_contract__th_insurance_paid
msgid "Paid for insurance"
msgstr "Lương ngạch bậc"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_salary_range_code__th_level
#: model:ir.model.fields,field_description:th_employee.field_hr_contract__th_level
msgid "Level of salary"
msgstr "Mức lương"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_salary_range_code__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_salary_range_code__create_datemsgid
msgid "Created on"
msgstr "Được tạo vào"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_salary_range_code____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_salary_range_code__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_salary_range_code__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_salary_range_code__th_currency_id
msgid "Currency unit"
msgstr "Đơn vị tiền tệ"

#. module: th_employee
#: model:ir.model.fields,field_description:th_employee.field_th_salary_range_code__th_responsible_unit
msgid "Responsible unit"
msgstr "Đơn vị chịu trách nhiệm"

#. module: th_employee
#: code:th_employee/models/th_salary_range_code.py:0
#, python-format
msgid "Paid for insurance must be greater than 0."
msgstr "Số lương ngạch bậc phải lớn hơn 0."

#. module: th_employee
#: code:th_employee/models/th_salary_range_code.py:0
#, python-format
msgid "Salary range code for a unit must be unique!"
msgstr "Mã mức lương cho một đơn vị phải là duy nhất!"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Departure"
msgstr "Nghỉ việc"