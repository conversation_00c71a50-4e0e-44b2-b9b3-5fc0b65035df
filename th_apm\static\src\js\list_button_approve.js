/** @odoo-module **/

import {registry} from '@web/core/registry';
import {listView} from '@web/views/list/list_view';
import {ListController} from '@web/views/list/list_controller';
import { ConfirmationDialog } from "@web/core/confirmation_dialog/confirmation_dialog";
import rpc from 'web.rpc';

export class APMListController extends ListController {
    setup() {
        super.setup()
    }

    async onClickConfirmApm() {
        const activeIds = await this.getSelectedResIds()
        const context = this.props.context['params']
        if (activeIds.length == 0) {
            this.dialogService.add(ConfirmationDialog, {
                title: "Xác nhận",
                body: "<PERSON>ạn chưa chọn liên hệ cần tạo cơ hội!",
                confirm: async () => {
                },
                cancel: () => {
                },
            });

        } else {
            const confirmed = await this.dialogService.add(ConfirmationDialog, {
                title: "<PERSON><PERSON><PERSON> nhận",
                body: "<PERSON><PERSON><PERSON> có chắc chắn muốn xác nhận ?",
                confirm: async () => {
                    await rpc.query({
                        model: this.model.root.resModel,
                        method: 'create_opportunities',
                        args: [[], activeIds, context],
                    });
                    return this.actionService.doAction({type: "ir.actions.act_window_close"});
                },
                cancel: () => {
                },
            });
        }


    }
}

export const ApmApproveListView = {
    ...listView,
    Controller: APMListController,
    buttonTemplate: "th_apm.ApmApproveListView.Buttons",
}
registry.category('views').add('apm_approve_list', ApmApproveListView);