/** @odoo-module **/

import rpc from 'web.rpc';
import { onWillStart } from "@odoo/owl";
import {patch} from "@web/core/utils/patch";
import { useService } from "@web/core/utils/hooks";
import {ListController} from "@web/views/list/list_controller";
import { ConfirmationDialog } from "@web/core/confirmation_dialog/confirmation_dialog";

// const list_model = ['th.opportunity.ctv']
patch(ListController.prototype, 'th_apm.ListController', {
    setup() {
        this._super.apply(this, arguments);
        this.user = useService("user");
        onWillStart(async () => {
            this.isModel = await this.check_model_help()
            // this.isSampleImport = await this.check_sample_import()
        });
    },
    async check_model_help() {
        // list_model.includes(this.props.resModel) &&
        if (await this.user.hasGroup("th_apm.group_apm_administrator")) return true
        return false
    },

    async onClickSyncDataOrder() {
        const confirmed = await this.dialogService.add(ConfirmationDialog, {
            title: "<PERSON><PERSON><PERSON> nhận đồng bộ",
            body: "Bạn có chắc chắn muốn đồng bộ dữ liệu đơn hàng?",
            confirm: async () => {
                await rpc.query({
                    model: 'sale.order',
                    method: 'th_action_sync_orders',
                    args: [[]],
                });
                return this.actionService.doAction({
                    'type': 'ir.actions.client',
                    'tag': 'reload',
                });
            },
            cancel: () => {
            },
        });
    },
});
