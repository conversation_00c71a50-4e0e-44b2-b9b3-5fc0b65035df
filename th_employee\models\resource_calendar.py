from odoo import fields, models, api

class ResourceCalender(models.Model):
    _inherit = "resource.calendar"

    @api.model
    def delete_values_resource_calendar(self):
        self.env['resource.calendar'].search([]).filtered(lambda d: len(d.attendance_ids) != 11).write({'active': False})
        self.sudo().env.company.write({'resource_calendar_id': self.env['resource.calendar'].search([], order="id asc").filtered(lambda d: len(d.attendance_ids) == 11)[0] if self.env['resource.calendar'].search([]).filtered(lambda d: len(d.attendance_ids) == 11) else False})