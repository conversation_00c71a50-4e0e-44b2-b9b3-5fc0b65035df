<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_cron_th_time_of_service_111" model="ir.cron">
        <field name="name">schedule ch<PERSON><PERSON> h<PERSON><PERSON> đồng bộ Lead APM</field>
        <field name="model_id" ref="model_th_apm" />
        <field name="state">code</field>
        <field name="code">model.search([]).run_opportunity_sync()</field>
        <field name="nextcall" eval="(DateTime.now().replace(hour=16, minute=0, second=0)).strftime('%Y-%m-%d %H:%M:%S')" />
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False" />
    </record>

    <record id="cron_update_account_move_origin_frequent" model="ir.cron">
        <field name="name">Update Account Move Origin Names (Frequent)</field>
        <field name="model_id" ref="model_account_move"/>
        <field name="state">code</field>
        <field name="code">model.search([])._cron_update_origin_names(batch_size=1000)</field>
        <field name="numbercall">-1</field>
        <field name="active" eval="False"/>
        <field name="user_id" ref="base.user_root"/>
    </record>

</odoo>