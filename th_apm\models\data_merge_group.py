# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import models, api, fields
from odoo.models import MAGIC_COLUMNS
from odoo.osv import expression
from odoo.tools import split_every
import xmlrpc.client


import logging
_logger = logging.getLogger(__name__)

IGNORED_FIELDS = MAGIC_COLUMNS
DM_CRON_BATCH_SIZE = 100


class DataMergeGroup(models.Model):
    _inherit = 'data_merge.group'

    def _elect_master_record(self):
        """
        Elect the "master" record.

        This method will look for a `_elect_method()` on the model.
        If it exists, this method is responsible to return the master record, otherwise, a generic method is used.
        """
        for group in self:
            if hasattr(self.env[group.res_model_name], '_elect_method'):
                elect_master = getattr(self.env[group.res_model_name], '_elect_method')
            else:
                elect_master = group._elect_method

            records = group.record_ids._original_records()
            if not records:
                return
            if not self._context.get('th_apm'):
                master = elect_master(records)
                if master:
                    master_record = group.record_ids.filtered(lambda r: r.res_id == master.id)
                    master_record.is_master = True
            else:
                is_duplicate_condition = False
                for rec in records:
                    th_last_check = (fields.Date.today() - rec.th_last_check.date()).days
                    check_condition = self.env['th.check.condition'].search(
                        [('th_date_from', '<=', th_last_check), ('th_date_to', '>', th_last_check),
                         ('th_stage_id', '=', rec.stage_id.id),
                         ('th_status_detail_id', '=', rec.th_status_detail_id.id)])
                    if check_condition and check_condition.th_result == 'transfer':
                        is_duplicate_condition = True
                    if is_duplicate_condition:
                        master_record = group.record_ids.filtered(lambda r: r.res_id == rec.id)
                        master_record.is_master = True
                if not group.record_ids.filtered(lambda r: r.res_id in records.ids).filtered(lambda r: r.is_master):
                    master = elect_master(records)
                    if master:
                        master_record = group.record_ids.filtered(lambda r: r.res_id == master.id)
                        master_record.is_master = True

    def merge_records(self, records=None):
        if self.res_model_name == 'th.apm':
            apm_lead_merge = self.record_ids._original_records()
            apm_lead_aff = self.env['th.apm'].search([('id', 'in', apm_lead_merge.ids)])
            self.ensure_one()
            if records is None:
                records = []
            domain = [('group_id', '=', self.id)]
            if records:
                domain += [('id', 'in', records)]
            to_merge = self.env['data_merge.record'].with_context(active_test=False).search(domain, order='id')
            to_merge_count = len(to_merge)
            if to_merge_count <= 1:
                return
            master_record = to_merge.filtered('is_master') or to_merge[0]
            to_merge = to_merge - master_record
            for rec in to_merge:
                if rec.res_id != master_record.res_id:
                    sale_master_record = self.env['sale.order'].search([('th_apm_id', '=', rec.res_id)])
                    if sale_master_record:
                        sale_master_record.th_apm_id = rec.res_id
                        lead_record = self.env['th.apm'].search([('id', '=', master_record.res_id)])
                        lead_record.th_stage_id = self.env['th.apm'].search([('id', '=', rec.res_id)]).th_stage_id
                        for res in sale_master_record.order_line.product_id.ids:
                            lead_record.th_product_ids = [(4, res)]
            if not master_record._original_records():
                _logger.warning('The master record does not exist')
                return

            _logger.info('Merging %s records %s into %s' % (
            self.res_model_name, to_merge.mapped('res_id'), master_record.res_id))

            model = self.env[self.res_model_name]
            if hasattr(model, '_merge_method'):
                merge = getattr(model, '_merge_method')
            else:
                merge = self._merge_method

            # Create a dict with chatter data, in case the merged records are deleted during the merge procedure
            chatter_data = {
                rec.res_id: dict(res_id=rec.res_id, merged_record=str(rec.name), changes=rec._record_snapshot()) for
                rec in to_merge}
            res = merge(master_record._original_records(), to_merge._original_records())
            if res.get('log_chatter'):
                self._log_merge(master_record, to_merge, chatter_data)

            if res.get('post_merge'):
                self._post_merge(master_record, to_merge)

            is_merge_action = master_record.model_id.is_contextual_merge_action
            (master_record + to_merge).unlink()

            return {
                'records_merged': res['records_merged'] if res.get('records_merged') else to_merge_count,
                # Used to get back to the functional model if deduplicate was
                # called from contextual action menu - instead of staying on
                # the deduplicate view.
                'back_to_model': is_merge_action
            }

        res = super(DataMergeGroup, self).merge_records(records)
        return res