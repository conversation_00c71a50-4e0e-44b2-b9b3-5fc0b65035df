from datetime import datetime
from odoo import models, fields, api
from odoo.exceptions import ValidationError


class ChildrenEmployee(models.Model):
    _name = 'children.employee'
    _description = "<PERSON>ư<PERSON>i phụ thuộc"

    th_child_information = fields.Many2one("hr.employee")
    th_children_name = fields.Char("First and last name")
    th_birthday = fields.Date("Date of Birth")
    th_relationship =fields.Char("Mối quan hệ")

    @api.onchange('th_birthday')
    def onchange_th_birthday(self):
        for rec in self:
            if rec.th_birthday and rec.th_birthday > datetime.today().date():
                raise ValidationError("<PERSON><PERSON><PERSON> sinh không được lớn hơn ngày hiên tại!")