/** @odoo-module **/

import KioskConfirm from "hr_attendance.kiosk_confirm";
import session from 'web.session';

KioskConfirm.include({
    events: _.extend(KioskConfirm.prototype.events, {
        "click .o_hr_attendance_pin_pad_button_ok": _.debounce(function() {
            var self = this;
            this.$('.o_hr_attendance_pin_pad_button_ok').attr("disabled", "disabled");
            this._rpc({
                    model: 'hr.employee',
                    method: 'attendance_manual',
                    args: [[this.employee_id], this.next_action, this.$('.o_hr_attendance_PINbox').val()],
                    context: Object.assign({}, session.user_context, {'is_kiosk_mode': true}),
                })
                .then(function(result) {
                    if (result.action) {
                        self.do_action(result.action);
                    } else if (result.warning) {
                        self.displayNotification({ title: result.warning, type: 'danger' });
                        self.$('.o_hr_attendance_PINbox').val('');
                        setTimeout( function() { self.$('.o_hr_attendance_pin_pad_button_ok').removeAttr("disabled"); }, 500);
                    }
                });
        }, 200, true),
    }),
});
