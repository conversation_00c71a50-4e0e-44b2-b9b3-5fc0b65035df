<odoo>
<!--    <record id="group_hr_staff" model="res.groups">-->
<!--        <field name="name">Staff</field>-->
<!--        <field name="category_id" ref="base.module_category_human_resources_employees"/>-->
<!--&lt;!&ndash;        <field name="implied_ids" eval="[(6, 0, [ref('hr_attendance.group_hr_attendance_kiosk'), ref('base.group_user'), ref('hr_attendance.group_hr_attendance')])]"/>&ndash;&gt;-->
<!--    </record>-->

<!--    <record id="hr.group_hr_user" model="res.groups">-->
<!--        <field name="name">Reporting Officier</field>-->
<!--        <field name="implied_ids" eval="[(4, ref('group_hr_staff'))]"/>-->
<!--    </record>-->

<!--    <record id="th_group_hr_staff_rule" model="ir.rule">-->
<!--        <field name="name">group hr staff access</field>-->
<!--        <field name="model_id" ref="model_hr_employee" />-->
<!--        <field name="domain_force">[('user_id','=',user.id)]</field>-->
<!--        <field name="groups" eval="[(4, ref('group_hr_staff'))]" />-->
<!--    </record>-->

<!--    <record id="th_group_hr_user_rule" model="ir.rule">-->
<!--        <field name="name">group hr employee access</field>-->
<!--        <field name="model_id" ref="model_hr_employee" />-->
<!--        <field name="domain_force">['|',('th_managers_users', 'in', user.id),('user_id','=',user.id)]</field>-->
<!--        <field name="groups" eval="[(4, ref('hr.group_hr_user'))]" />-->
<!--    </record>  -->

<!--    <record id="th_group_hr_manager_rule" model="ir.rule">-->
<!--        <field name="name">group manager employee access</field>-->
<!--        <field name="model_id" ref="model_hr_employee" />-->
<!--        <field name="domain_force">[(1, '=', 1)]</field>-->
<!--        <field name="groups" eval="[(4, ref('hr.group_hr_manager'))]" />-->
<!--    </record>-->
</odoo>