import json
from odoo.exceptions import UserError
from odoo import models, fields, api, _


class SaleOrderLine(models.Model):
    _name = 'th.order.history'
    _description = 'L<PERSON>ch sử mua hàng'

    th_apm_id = fields.Many2one(string="<PERSON><PERSON> hội", comodel_name='th.apm')
    name = fields.Char('Tên đơn hàng')
    th_sale_order_id = fields.Many2one(string="Đơn hàng", comodel_name='sale.order')
    th_origin_id = fields.Many2one(string='Dòng sản phẩm', comodel_name="th.origin", related='th_sale_order_id.th_origin_id')
    th_product_ids = fields.Many2many('product.product', string='Sản phẩm', ondelete='cascade')
    th_create_date = fields.Datetime(string='Ngày mua', related='th_sale_order_id.create_date')
    th_ownership_unit_id = fields.Char(string="Đơn vị sở hữu")
    th_ownership_unit_new_id = fields.Many2one(comodel_name="th.ownership.unit", string="Đơn vị sở hữu", related='th_sale_order_id.th_ownership_unit_id')
    user_id = fields.Many2one('res.users', string='Nhân viên chốt đơn')
    th_apm_source_group_id = fields.Many2one(string="Nhóm nguồn", related="th_sale_order_id.th_apm_source_group_id")
    th_introducer_id = fields.Many2one(string="Người giới thiệu", related='th_sale_order_id.th_introducer_id')
    th_channel_id = fields.Many2one(string="Kênh", comodel_name='th.info.channel', related='th_sale_order_id.th_channel_id')