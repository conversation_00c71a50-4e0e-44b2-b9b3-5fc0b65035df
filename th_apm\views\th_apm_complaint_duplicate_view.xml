<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="th_apm_complaint_duplicate_view_form" model="ir.ui.view">
        <field name="name">th.apm.complaint.duplicate.view.form</field>
        <field name="inherit_id" ref="th_apm.apm_lead_view_form"/>
        <field name="model">th.apm</field>
        <field name="arch" type="xml">
            <!-- Thê<PERSON> n<PERSON><PERSON> k<PERSON> nại vào header -->
            <xpath expr="//header" position="inside">
                <button name="th_action_list_duplicate" string="Danh sách lead trùng" type="object" class="oe_highlight no-wrap" invisible="not context.get('th_duplicate_type', False)"/>
                <button name="th_action_keep_opportunity" string="Giữ cơ hội" type="object" class="oe_highlight" invisible="not context.get('th_duplicate_type', False)"/>
                <!-- <button name="th_action_close_opportunity" string="Đóng cơ hội" type="object" class="btn-secondary" invisible="not context.get('th_duplicate_type', False)"/>
                <button name="th_action_open_opportunity" string="Mở cơ hội" type="object" class="btn-secondary" invisible="not context.get('th_duplicate_type', False)"/> -->
            </xpath>
            <field name="th_is_a_duplicate_opportunity" position="after">
                <field name="th_duplicate_type" invisible="1"/>
                <field name="th_is_under_complaint" invisible="1"/>
                <field name="th_is_close_lead" invisible="1"/>
            </field>
        </field>
    </record>

    <record id="th_action_apm_complaint_duplicate" model="ir.actions.act_window">
        <field name="name">Khiếu nại cơ hội trùng</field>
        <field name="res_model">th.apm</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'th_duplicate_type':'need_handle','create': False}</field>
        <field name="domain">[('th_is_under_complaint', '=', True),('th_is_a_duplicate_opportunity', '=', False)]</field>
    </record>
</odoo>
