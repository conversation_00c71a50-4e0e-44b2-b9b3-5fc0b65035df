from odoo import models, fields


class Partner(models.Model):
    _inherit = 'res.partner'

    th_type = fields.Selection([('employees', 'Employees'),
                                ('customers', 'Customers'),
                                ('suppliers', 'Suppliers'),
                                ('contractors', 'Contractors')], string="Type of contact")
    
    th_street = fields.Char()
    th_street2 = fields.Char()
    th_zip = fields.Char(change_default=True)
    th_city = fields.Char()
    th_state_id = fields.Many2one("res.country.state", string='State', ondelete='restrict', domain="[('country_id', '=?', th_country_id)]")
    th_country_id = fields.Many2one('res.country', string='Country', ondelete='restrict')
    