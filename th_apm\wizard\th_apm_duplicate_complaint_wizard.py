from odoo import fields, models, api, _
from odoo.exceptions import ValidationError


class ThApmDuplicateComplaintWizard(models.TransientModel):
    _name = 'th.apm.duplicate.complaint.wizard'
    _description = '<PERSON> khiếu nại cơ hội trùng'

    th_apm_id = fields.Many2one('th.apm', string='Cơ hội khiếu nại', required=True, readonly=True)
    th_description = fields.Text(string='Lý do khiếu nại', required=True)

    @api.model
    def default_get(self, fields_list):
        """Lấy thông tin mặc định từ context"""
        res = super(ThApmDuplicateComplaintWizard, self).default_get(fields_list)
        if self._context.get('active_model') == 'th.apm' and self._context.get('active_id'):
            apm_id = self.env['th.apm'].browse(self._context.get('active_id'))
            if not apm_id.th_is_a_duplicate_opportunity:
                raise ValidationError(_('Chỉ có thể khiếu nại cơ hội đã bị đánh dấu trùng!'))
            if apm_id.th_dup_need_admin:
                raise ValidationError(_('Cơ hội này đã có khiếu nại đang chờ xử lý!'))
            if apm_id.th_is_under_complaint:
                raise ValidationError(_('Cơ hội này đã có khiếu nại đang chờ xử lý!'))
            res['th_apm_id'] = apm_id.id
        return res

    def action_submit_complaint(self):
        """Tạo khiếu nại và gửi cho admin xử lý"""
        self.ensure_one()

        # kiểm tra lịch sử cơ hội trùng
        if self.th_apm_id:
            # Lấy cơ hội thắng làm cơ hội khiếu nại
            th_history = self.env['th.apm.duplicate.check.history'].search([
                ('th_lead_apm_id_old', '=', self.th_apm_id.id),
            ], limit=1)
            if not th_history:
                raise ValidationError(_('Không tìm thấy lịch sử cơ hội trùng!'))
            th_lead_apm_id_new = th_history.th_lead_apm_id_new

        # Kiểm tra lại điều kiện
        if th_lead_apm_id_new.th_is_a_duplicate_opportunity:
            raise ValidationError(_('Chỉ có thể khiếu nại khi có cơ hội đang được chăm!'))

        if th_lead_apm_id_new.th_dup_need_admin:
            raise ValidationError(_('Cơ hội này đã có khiếu nại đang chờ xử lý!'))

        if th_lead_apm_id_new.th_is_under_complaint:
            raise ValidationError(_('Cơ hội này đã có khiếu nại đang chờ xử lý!'))

        # Đánh dấu cơ hội là đang khiếu nại
        th_lead_apm_id_new.sudo().write({
            'th_dup_need_admin': True,
            'th_is_under_complaint': True,
            'th_duplicate_type': 'need_handle',
        })
        # Đánh dấu cơ hội trùng đang khiếu nại
        self.th_apm_id.sudo().write({
            'th_is_under_complaint': True,
            'th_dup_need_admin': True,
        })

        th_history.sudo().write({
            'th_duplicate_type': 'need_handle',
            'th_description': th_history.th_description + '\n\nKhiếu nại: ' + self.th_description if th_history.th_description else 'Khiếu nại: ' + self.th_description
        })

        # Hiển thị thông báo thành công
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Thành công'),
                'message': _('Đã gửi khiếu nại thành công!'),
                'sticky': False,
                'type': 'success',
                'next': {'type': 'ir.actions.act_window_close'},
            }
        }
